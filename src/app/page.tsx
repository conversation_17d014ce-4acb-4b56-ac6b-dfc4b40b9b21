/**
 * Homepage - Simple Restaurant Ordering System
 * Focus on core functionality only
 */

'use client'

import { useRouter } from 'next/navigation'
import { QrCode, ShoppingCart, Users, Bar<PERSON>hart, <PERSON><PERSON>, Clock } from 'lucide-react'

export default function HomePage() {
  const router = useRouter()

  return (
    <div className="min-h-screen bg-gradient-to-b from-orange-50 to-white">
      {/* Header */}
      <header className="px-6 py-4 bg-white shadow-sm">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">R</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900">Restaurant Admin</h1>
          </div>
          <nav className="flex items-center space-x-6">
            <button
              onClick={() => router.push('/menu')}
              className="text-gray-600 hover:text-gray-900 transition-colors"
            >
              View Menu
            </button>
            <button
              onClick={() => router.push('/login')}
              className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium"
            >
              Admin Login
            </button>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="px-6 py-20">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-5xl font-bold text-gray-900 mb-6">
            Simple Restaurant Ordering
            <span className="block text-orange-600 mt-2">Made Easy</span>
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Digital menu, cart functionality, order management, admin dashboard, and QR code generation.
            Everything you need for modern restaurant operations.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <button
              onClick={() => router.push('/menu')}
              className="px-8 py-4 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-lg font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              View Demo Menu
            </button>
            <button
              onClick={() => router.push('/login')}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Admin Login
            </button>
          </div>
        </div>
      </section>

      {/* Core Features */}
      <section className="px-6 py-16 bg-white">
        <div className="max-w-7xl mx-auto">
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Core Restaurant Features
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard
              icon={<Menu className="w-8 h-8 text-orange-600" />}
              title="Digital Menu"
              description="Display your menu items with images, prices, and descriptions"
              onClick={() => router.push('/menu')}
            />
            <FeatureCard
              icon={<ShoppingCart className="w-8 h-8 text-orange-600" />}
              title="Cart & Orders"
              description="Customers can add items to cart and place orders easily"
              onClick={() => router.push('/menu')}
            />
            <FeatureCard
              icon={<Users className="w-8 h-8 text-orange-600" />}
              title="Admin Dashboard"
              description="Manage menu items, view orders, and handle operations"
              onClick={() => router.push('/login')}
            />
            <FeatureCard
              icon={<QrCode className="w-8 h-8 text-orange-600" />}
              title="QR Code Generation"
              description="Generate QR codes for tables to access digital menu"
              onClick={() => router.push('/login')}
            />
            <FeatureCard
              icon={<BarChart className="w-8 h-8 text-orange-600" />}
              title="Order Management"
              description="Track and manage incoming orders in real-time"
              onClick={() => router.push('/login')}
            />
            <FeatureCard
              icon={<Clock className="w-8 h-8 text-orange-600" />}
              title="Simple & Fast"
              description="Quick setup with no unnecessary complexity"
              onClick={() => router.push('/menu')}
            />
          </div>
        </div>
      </section>

      {/* Demo Section */}
      <section className="px-6 py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto bg-orange-50 rounded-2xl p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-4 text-center">
            Try the Demo
          </h3>
          <p className="text-gray-600 text-center mb-6">
            Experience the core functionality
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={() => router.push('/menu')}
              className="p-4 bg-white rounded-lg border border-orange-200 hover:border-orange-300 transition-colors"
            >
              <h4 className="font-semibold text-gray-900 mb-2">Customer Menu</h4>
              <p className="text-sm text-gray-600">
                View menu, add items to cart, and place orders
              </p>
            </button>
            <button
              onClick={() => router.push('/login')}
              className="p-4 bg-white rounded-lg border border-orange-200 hover:border-orange-300 transition-colors"
            >
              <h4 className="font-semibold text-gray-900 mb-2">Admin Dashboard</h4>
              <p className="text-sm text-gray-600">
                <NAME_EMAIL> / admin123
              </p>
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="px-6 py-8 bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto text-center text-gray-600">
          <p>&copy; 2024 Simple Restaurant Ordering System. Built for core functionality.</p>
        </div>
      </footer>
    </div>
  )
}

function FeatureCard({ 
  icon, 
  title, 
  description, 
  onClick 
}: { 
  icon: React.ReactNode
  title: string
  description: string
  onClick: () => void
}) {
  return (
    <button
      onClick={onClick}
      className="p-6 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors text-left group"
    >
      <div className="mb-4 group-hover:scale-110 transition-transform">
        {icon}
      </div>
      <h4 className="text-lg font-semibold text-gray-900 mb-2">{title}</h4>
      <p className="text-gray-600 text-sm">{description}</p>
    </button>
  )
}