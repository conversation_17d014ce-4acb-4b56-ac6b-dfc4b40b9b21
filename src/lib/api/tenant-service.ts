/**
 * Tenant signup service
 * Handles the complete tenant creation workflow
 */

import prisma from './database';
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Helper functions
export async function createAuthUser(email: string, password: string, metadata: any = {}) {
  try {
    const { data, error } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: metadata
    })

    if (error) throw error

    return {
      success: true,
      user: data.user,
      auth_user_id: data.user?.id
    }
  } catch (error: any) {
    console.error('Supabase user creation error:', error)
    
    if (error.message?.includes('already registered')) {
      throw new Error('Email address is already registered')
    }
    
    if (error.message?.includes('password')) {
      throw new Error('Password does not meet security requirements')
    }
    
    throw new Error('Failed to create user account')
  }
}

export async function generateUserToken(authUserId: string) {
  try {
    return `auth_token_${authUserId}_${Date.now()}`
  } catch (error) {
    console.error('Token generation error:', error)
    throw new Error('Failed to generate authentication token')
  }
}
import { TenantSignupData } from '../validation/tenant-signup';

export interface TenantSignupResult {
  tenant: {
    id: string;
    name: string;
    slug: string;
    email: string;
    status: string;
    plan_type: string;
    created_at: string;
  };
  user: {
    id: string;
    email: string;
    first_name: string | null;
    last_name: string | null;
    role: string;
    status: string;
    tenant_id: string;
    created_at: string;
  };
  access_token: string;
}

export class TenantService {
  /**
   * Check if email or slug is already in use
   */
  static async checkUniqueness(email?: string, slug?: string) {
    const results: { email_available?: boolean; slug_available?: boolean } = {};

    if (email) {
      const existingUser = await prisma.adminUser.findUnique({
        where: { email }
      });
      results.email_available = !existingUser;
    }

    if (slug) {
      const existingTenant = await prisma.tenant.findUnique({
        where: { slug }
      });
      results.slug_available = !existingTenant;
    }

    return results;
  }

  /**
   * Complete tenant signup workflow
   */
  static async createTenantWithAdmin(signupData: TenantSignupData): Promise<TenantSignupResult> {
    // Step 1: Validate uniqueness
    const { email_available, slug_available } = await this.checkUniqueness(
      signupData.email,
      signupData.tenant_slug
    );

    if (email_available === false) {
      throw new Error('Email address is already registered');
    }

    if (slug_available === false) {
      throw new Error('Business URL is already taken');
    }

    // Step 2: Create Supabase auth user
    const authResult = await createAuthUser(
      signupData.email,
      signupData.password,
      {
        first_name: signupData.first_name,
        last_name: signupData.last_name,
        role: 'OWNER',
        tenant_slug: signupData.tenant_slug
      }
    );

    if (!authResult.auth_user_id) {
      throw new Error('Failed to create authentication account');
    }

    try {
      // Step 3: Create tenant and admin user in a transaction
      const result = await prisma.$transaction(async (tx) => {
        // Create tenant
        const tenant = await tx.tenant.create({
          data: {
            name: signupData.tenant_name,
            slug: signupData.tenant_slug,
            email: signupData.email,
            phone: signupData.phone || null,
            address: signupData.address || null,
            city: signupData.city || null,
            state: signupData.state || null,
            zipCode: signupData.zip_code || null,
            status: 'ACTIVE',
            planType: 'TRIAL',
            settings: JSON.stringify({
              business_hours: {
                monday: { open: '09:00', close: '22:00', closed: false },
                tuesday: { open: '09:00', close: '22:00', closed: false },
                wednesday: { open: '09:00', close: '22:00', closed: false },
                thursday: { open: '09:00', close: '22:00', closed: false },
                friday: { open: '09:00', close: '22:00', closed: false },
                saturday: { open: '09:00', close: '22:00', closed: false },
                sunday: { open: '09:00', close: '22:00', closed: false }
              },
              ordering: {
                allow_preorders: true,
                max_order_advance_days: 7,
                min_order_amount: 0,
                tax_rate: 0.08
              },
              notifications: {
                email_orders: true,
                sms_orders: false,
                order_sound: true
              }
            }),
            features: JSON.stringify({
              qr_ordering: true,
              realtime_orders: true,
              analytics: false, // Disabled for trial
              staff_management: true,
              menu_management: true,
              room_management: true
            })
          }
        });

        // Create admin user
        const adminUser = await tx.adminUser.create({
          data: {
            tenantId: tenant.id,
            authUserId: authResult.auth_user_id,
            email: signupData.email,
            firstName: signupData.first_name,
            lastName: signupData.last_name,
            role: 'OWNER',
            status: 'ACTIVE',
            permissions: JSON.stringify([
              'tenant:manage',
              'users:manage',
              'orders:manage',
              'menu:manage',
              'rooms:manage',
              'analytics:view',
              'settings:manage'
            ]),
            preferences: JSON.stringify({
              theme: 'light',
              notifications: true,
              dashboard_layout: 'default'
            })
          }
        });

        // Log the signup event
        await tx.auditLog.create({
          data: {
            tenantId: tenant.id,
            userId: adminUser.id,
            action: 'tenant:signup_completed',
            resource: 'tenant',
            resourceId: tenant.id,
            metadata: JSON.stringify({
              event: 'signup_success',
              plan_type: 'TRIAL',
              timestamp: new Date().toISOString()
            })
          }
        });

        return { tenant, adminUser };
      });

      // Step 4: Generate JWT token
      const accessToken = await generateUserToken(authResult.auth_user_id);

      // Step 5: Return formatted response
      return {
        tenant: {
          id: result.tenant.id,
          name: result.tenant.name,
          slug: result.tenant.slug,
          email: result.tenant.email,
          status: result.tenant.status,
          plan_type: result.tenant.planType,
          created_at: result.tenant.createdAt.toISOString()
        },
        user: {
          id: result.adminUser.id,
          email: result.adminUser.email,
          first_name: result.adminUser.firstName,
          last_name: result.adminUser.lastName,
          role: result.adminUser.role,
          status: result.adminUser.status,
          tenant_id: result.adminUser.tenantId,
          created_at: result.adminUser.createdAt.toISOString()
        },
        access_token: accessToken
      };

    } catch (dbError) {
      console.error('Database transaction failed:', dbError);
      
      // TODO: Clean up the Supabase user if database transaction fails
      // This would require implementing a proper rollback mechanism
      
      throw new Error('Failed to complete tenant registration');
    }
  }
}