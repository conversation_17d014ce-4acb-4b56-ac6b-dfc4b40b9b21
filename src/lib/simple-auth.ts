/**
 * Simple Authentication System
 * Basic auth for restaurant ordering app
 */

export interface User {
  id: string
  email: string
  name: string
  role: 'admin' | 'customer'
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
}

// Simple auth store using localStorage
class SimpleAuth {
  private listeners: Array<(state: AuthState) => void> = []
  private currentUser: User | null = null

  constructor() {
    // Check for existing session (only in browser)
    if (typeof window !== 'undefined') {
      const storedUser = localStorage.getItem('bheemdine_user')
      if (storedUser) {
        this.currentUser = JSON.parse(storedUser)
      }
    }
  }

  login(email: string, password: string): Promise<{ success: boolean; error?: string }> {
    return new Promise((resolve) => {
      // Simple mock authentication
      if (email === '<EMAIL>' && password === 'admin123') {
        this.currentUser = {
          id: '1',
          email: '<EMAIL>',
          name: 'Admin User',
          role: 'admin'
        }
        localStorage.setItem('bheemdine_user', JSON.stringify(this.currentUser))
        this.notifyListeners()
        resolve({ success: true })
      } else {
        resolve({ success: false, error: 'Invalid credentials' })
      }
    })
  }

  logout() {
    this.currentUser = null
    localStorage.removeItem('bheemdine_user')
    this.notifyListeners()
  }

  getUser(): User | null {
    return this.currentUser
  }

  isAuthenticated(): boolean {
    return this.currentUser !== null
  }

  isAdmin(): boolean {
    return this.currentUser?.role === 'admin'
  }

  subscribe(listener: (state: AuthState) => void) {
    this.listeners.push(listener)
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener)
    }
  }

  private notifyListeners() {
    const state: AuthState = {
      user: this.currentUser,
      isAuthenticated: this.isAuthenticated(),
      isLoading: false
    }
    this.listeners.forEach(listener => listener(state))
  }
}

export const auth = new SimpleAuth()

// React hooks for authentication
import { useState, useEffect } from 'react'

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: auth.getUser(),
    isAuthenticated: auth.isAuthenticated(),
    isLoading: false
  })

  useEffect(() => {
    return auth.subscribe(setAuthState)
  }, [])

  return {
    ...authState,
    login: auth.login.bind(auth),
    logout: auth.logout.bind(auth),
    isAdmin: auth.isAdmin.bind(auth)
  }
}