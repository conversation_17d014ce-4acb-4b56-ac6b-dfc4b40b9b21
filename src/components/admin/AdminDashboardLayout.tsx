/**
 * Simple Admin Dashboard Layout
 * Simplified layout for restaurant admin
 */

'use client'

import React, { useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { 
  LayoutDashboard,
  Menu as MenuIcon,
  ShoppingBag,
  QrCode,
  LogOut,
  X,
  Building
} from 'lucide-react'
import { useAuth } from '@/lib/simple-auth'
import { ProtectedRoute } from '@/components/ProtectedRoute'

interface AdminDashboardLayoutProps {
  children: React.ReactNode
  title?: string
  className?: string
}

interface NavigationItem {
  name: string
  href: string
  icon: React.ElementType
}

export function AdminDashboardLayout({ 
  children, 
  title, 
  className = '' 
}: AdminDashboardLayoutProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { user, logout } = useAuth()
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const navigation: NavigationItem[] = [
    {
      name: 'Dashboard',
      href: '/admin/dashboard',
      icon: LayoutDashboard,
    },
    {
      name: 'Menu Management',
      href: '/admin/menu',
      icon: MenuIcon,
    },
    {
      name: 'Orders',
      href: '/admin/orders',
      icon: ShoppingBag,
    },
    {
      name: 'QR Codes',
      href: '/admin/qr',
      icon: QrCode,
    },
  ]

  const isActiveRoute = (href: string): boolean => {
    if (href === '/admin/dashboard') {
      return pathname === href
    }
    return pathname.startsWith(href)
  }

  const handleLogout = () => {
    logout()
    router.push('/login')
  }

  const renderNavigationItem = (item: NavigationItem) => {
    const Icon = item.icon
    const isActive = isActiveRoute(item.href)
    
    return (
      <button
        key={item.href}
        onClick={() => {
          router.push(item.href)
          setSidebarOpen(false)
        }}
        className={`
          group flex items-center w-full px-3 py-2 text-sm font-medium rounded-md transition-colors
          ${isActive
            ? 'bg-orange-100 text-orange-700 border-r-2 border-orange-600'
            : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
          }
        `}
      >
        <Icon className={`
          mr-3 flex-shrink-0 h-5 w-5 transition-colors
          ${isActive ? 'text-orange-600' : 'text-gray-400 group-hover:text-gray-500'}
        `} />
        <span className="flex-1 text-left">{item.name}</span>
      </button>
    )
  }

  const renderSidebar = () => (
    <div className="flex flex-col w-64 bg-white border-r border-gray-200">
      {/* Logo */}
      <div className="flex items-center h-16 px-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center">
            <Building className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-semibold text-gray-900">Restaurant Admin</h1>
            <p className="text-xs text-gray-500">Management Portal</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
        {navigation.map(item => renderNavigationItem(item))}
      </nav>

      {/* User Section */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium text-gray-700">
              {user?.name?.charAt(0) || 'A'}
            </span>
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-900">{user?.name}</p>
            <p className="text-xs text-gray-500">{user?.email}</p>
          </div>
        </div>
        
        <button
          onClick={handleLogout}
          className="flex items-center w-full px-3 py-2 text-sm text-red-700 hover:bg-red-50 rounded-md transition-colors"
        >
          <LogOut className="w-4 h-4 mr-3" />
          Sign Out
        </button>
      </div>
    </div>
  )

  const renderMobileSidebar = () => (
    <>
      {/* Overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
      
      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-white transform transition-transform duration-300 ease-in-out lg:hidden
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        {/* Close button */}
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center">
              <Building className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-lg font-semibold text-gray-900">Restaurant Admin</h1>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Mobile navigation content */}
        <div className="flex flex-col h-full">
          <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
            {navigation.map(item => renderNavigationItem(item))}
          </nav>

          {/* User Section */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-gray-700">
                  {user?.name?.charAt(0) || 'A'}
                </span>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">{user?.name}</p>
                <p className="text-xs text-gray-500">{user?.email}</p>
              </div>
            </div>
            
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-3 py-2 text-sm text-red-700 hover:bg-red-50 rounded-md transition-colors"
            >
              <LogOut className="w-4 h-4 mr-3" />
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </>
  )

  return (
    <ProtectedRoute requireAdmin>
      <div className={`min-h-screen bg-gray-50 ${className}`}>
        <div className="flex h-screen">
          {/* Desktop Sidebar */}
          <div className="hidden lg:flex lg:flex-shrink-0">
            {renderSidebar()}
          </div>

          {/* Mobile Sidebar */}
          {renderMobileSidebar()}

          {/* Main Content */}
          <div className="flex flex-col flex-1 overflow-hidden">
            {/* Header */}
            <header className="bg-white shadow-sm border-b border-gray-200">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex items-center justify-between h-16">
                  {/* Mobile menu button */}
                  <button
                    onClick={() => setSidebarOpen(true)}
                    className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 lg:hidden"
                  >
                    <MenuIcon className="w-6 h-6" />
                  </button>

                  {/* Page title */}
                  {title && (
                    <div className="flex-1 lg:flex-none">
                      <h1 className="text-xl font-semibold text-gray-900">{title}</h1>
                    </div>
                  )}

                  {/* Header actions */}
                  <div className="flex items-center space-x-4">
                    <span className="text-sm text-gray-500">
                      Welcome, {user?.name}
                    </span>
                  </div>
                </div>
              </div>
            </header>

            {/* Main Content Area */}
            <main className="flex-1 overflow-auto">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                {children}
              </div>
            </main>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}