// Order details panel showing comprehensive order information and actions
// Displays selected order details with real-time updates and management options

'use client';

import React, { useState, useMemo } from 'react';
import {
  X,
  User,
  MapPin,
  Phone,
  Mail,
  Clock,
  DollarSign,
  MessageSquare,
  Edit,
  Save,
  AlertTriangle,
  CheckCircle,
  Star,
  Utensils,
  Timer,
  Bell,
  FileText,
  ChevronDown,
  ChevronUp,
} from 'lucide-react';

import { useOrder, useOrderEvents, useAddOrderNotes, useAssignOrderToStaff } from '@/hooks/useOrderQueries';
// Removed complex auth import
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

import type { OrderEvent } from '@/types/realtime-orders';

interface OrderDetailsPanelProps {
  orderId: string | null;
  onClose: () => void;
  className?: string;
}

/**
 * Order Timeline Component
 * Shows the chronological history of order events
 */
interface OrderTimelineProps {
  events: OrderEvent[];
}

function OrderTimeline({ events }: OrderTimelineProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Sort events by creation time (newest first)
  const sortedEvents = useMemo(() => {
    return [...events].sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  }, [events]);
  
  // Show only first 3 events by default
  const visibleEvents = isExpanded ? sortedEvents : sortedEvents.slice(0, 3);
  
  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'ORDER_CREATED': return <Star className="w-4 h-4 text-blue-500" />;
      case 'STATUS_CHANGED': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'ASSIGNED_TO_STAFF': return <User className="w-4 h-4 text-orange-500" />;
      case 'NOTES_ADDED': return <MessageSquare className="w-4 h-4 text-purple-500" />;
      case 'CUSTOMER_NOTIFIED': return <Bell className="w-4 h-4 text-blue-500" />;
      default: return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };
  
  const getEventDescription = (event: OrderEvent) => {
    switch (event.event_type) {
      case 'ORDER_CREATED':
        return 'Order was created';
      case 'STATUS_CHANGED':
        return `Status changed from ${event.old_status} to ${event.new_status}`;
      case 'ASSIGNED_TO_STAFF':
        return `Assigned to ${event.metadata?.staff_name || 'staff member'}`;
      case 'NOTES_ADDED':
        return `Note added: "${event.notes}"`;
      case 'CUSTOMER_NOTIFIED':
        return `Customer notified via ${event.metadata?.notification_method || 'system'}`;
      default:
        return event.event_type.replace('_', ' ').toLowerCase();
    }
  };
  
  if (events.length === 0) {
    return (
      <div className="text-center py-4 text-gray-500">
        <Clock className="w-6 h-6 mx-auto mb-2 opacity-50" />
        <p className="text-sm">No activity yet</p>
      </div>
    );
  }
  
  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h4 className="font-medium text-gray-900">Order Timeline</h4>
        {sortedEvents.length > 3 && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center space-x-1 text-sm text-orange-600 hover:text-orange-700"
          >
            <span>{isExpanded ? 'Show less' : `Show all (${sortedEvents.length})`}</span>
            {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </button>
        )}
      </div>
      
      <div className="space-y-3">
        {visibleEvents.map((event, index) => (
          <div key={event.id} className="flex items-start space-x-3">
            <div className="flex-shrink-0 mt-1">
              {getEventIcon(event.event_type)}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-900">
                  {getEventDescription(event)}
                </p>
                <time className="text-xs text-gray-500">
                  {new Date(event.created_at).toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </time>
              </div>
              
              {event.notes && (
                <p className="text-sm text-gray-600 mt-1 italic">
                  "{event.notes}"
                </p>
              )}
              
              <p className="text-xs text-gray-500 mt-1">
                by {event.changed_by_role.toLowerCase()}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

/**
 * Add Notes Component
 * Allows staff to add notes to orders
 */
interface AddNotesProps {
  orderId: string;
  tenantId: string;
  authToken: string;
}

function AddNotes({ orderId, tenantId, authToken }: AddNotesProps) {
  const [notes, setNotes] = useState('');
  const [isAdding, setIsAdding] = useState(false);
  
  const addNotesMutation = useAddOrderNotes(tenantId, authToken);
  
  const handleAddNotes = async () => {
    if (!notes.trim()) return;
    
    setIsAdding(true);
    try {
      await addNotesMutation.mutateAsync({
        orderId,
        notes: notes.trim(),
      });
      
      setNotes('');
      console.log('✅ Notes added successfully');
    } catch (error) {
      console.error('❌ Failed to add notes:', error);
    } finally {
      setIsAdding(false);
    }
  };
  
  return (
    <div className="space-y-3">
      <h4 className="font-medium text-gray-900">Add Notes</h4>
      
      <div className="space-y-2">
        <textarea
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          placeholder="Add notes about this order..."
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-sm"
        />
        
        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-500">
            {notes.length}/500 characters
          </span>
          
          <button
            onClick={handleAddNotes}
            disabled={!notes.trim() || isAdding || addNotesMutation.isPending}
            className="flex items-center space-x-2 px-3 py-1.5 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm"
          >
            {isAdding ? (
              <>
                <LoadingSpinner size="sm" />
                <span>Adding...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span>Add Note</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}

/**
 * Main Order Details Panel Component
 */
export function OrderDetailsPanel({ orderId, onClose, className = '' }: OrderDetailsPanelProps) {
  
  // ==================== HOOKS ====================
  
  // Simplified - no complex auth needed
  const tenantId = 'default';
  const authToken = 'mock-token';
  
  // Fetch order details and events
  const {
    data: order,
    isLoading: isLoadingOrder,
    isError: orderError,
  } = useOrder(orderId);
  
  const {
    data: events,
    isLoading: isLoadingEvents,
  } = useOrderEvents(orderId);
  
  // ==================== COMPUTED VALUES ====================
  
  // Calculate order timing information
  const orderTimings = useMemo(() => {
    if (!order) return null;
    
    const now = new Date();
    const createdAt = new Date(order.created_at);
    const estimatedCompletion = order.estimated_completion_time 
      ? new Date(order.estimated_completion_time)
      : null;
    const actualCompletion = order.actual_completion_time
      ? new Date(order.actual_completion_time)
      : null;
    
    const timeSinceCreated = Math.floor((now.getTime() - createdAt.getTime()) / (1000 * 60));
    const estimatedTimeRemaining = estimatedCompletion
      ? Math.floor((estimatedCompletion.getTime() - now.getTime()) / (1000 * 60))
      : null;
    
    return {
      timeSinceCreated,
      estimatedTimeRemaining,
      isOverdue: estimatedTimeRemaining !== null && estimatedTimeRemaining < 0,
      actualCompletion,
    };
  }, [order]);
  
  // Calculate order totals breakdown
  const orderTotals = useMemo(() => {
    if (!order) return null;
    
    const subtotal = order.items.reduce((sum: number, item: any) => sum + (item.unit_price * item.quantity), 0);
    const tax = order.tax_amount || 0;
    const deliveryFee = order.delivery_fee || 0;
    const total = order.total_amount;
    
    return {
      subtotal,
      tax,
      deliveryFee,
      total,
    };
  }, [order]);
  
  // ==================== RENDER HELPERS ====================
  
  const renderOrderHeader = () => {
    if (!order) return null;
    
    return (
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between mb-3">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {order.order_number}
            </h3>
            <div className="flex items-center space-x-2 mt-1">
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                order.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                order.status === 'CONFIRMED' ? 'bg-blue-100 text-blue-800' :
                order.status === 'PREPARING' ? 'bg-orange-100 text-orange-800' :
                order.status === 'READY' ? 'bg-green-100 text-green-800' :
                order.status === 'DELIVERED' ? 'bg-gray-100 text-gray-800' :
                'bg-red-100 text-red-800'
              }`}>
                {order.status}
              </span>
              
              {order.is_urgent && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  URGENT
                </span>
              )}
            </div>
          </div>
          
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        {orderTimings && (
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-600">Time since created</p>
              <p className="font-medium text-gray-900">
                {orderTimings.timeSinceCreated}m ago
              </p>
            </div>
            
            {orderTimings.estimatedTimeRemaining !== null && (
              <div>
                <p className="text-gray-600">Time remaining</p>
                <p className={`font-medium ${
                  orderTimings.isOverdue ? 'text-red-600' : 'text-gray-900'
                }`}>
                  {orderTimings.isOverdue 
                    ? `${Math.abs(orderTimings.estimatedTimeRemaining)}m overdue`
                    : `${orderTimings.estimatedTimeRemaining}m left`
                  }
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };
  
  const renderCustomerInfo = () => {
    if (!order) return null;
    
    return (
      <div className="p-4 border-b border-gray-200">
        <h4 className="font-medium text-gray-900 mb-3">Customer Information</h4>
        
        <div className="space-y-2 text-sm">
          {order.customer_name && (
            <div className="flex items-center space-x-2">
              <User className="w-4 h-4 text-gray-400" />
              <span className="text-gray-900">{order.customer_name}</span>
            </div>
          )}
          
          {order.customer_email && (
            <div className="flex items-center space-x-2">
              <Mail className="w-4 h-4 text-gray-400" />
              <span className="text-gray-600">{order.customer_email}</span>
            </div>
          )}
          
          {order.customer_phone && (
            <div className="flex items-center space-x-2">
              <Phone className="w-4 h-4 text-gray-400" />
              <span className="text-gray-600">{order.customer_phone}</span>
            </div>
          )}
          
          {order.room_number && (
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-gray-400" />
              <span className="text-gray-600">Room {order.room_number}</span>
            </div>
          )}
          
          {!order.customer_name && !order.room_number && (
            <p className="text-gray-500 italic">Walk-in customer</p>
          )}
        </div>
      </div>
    );
  };
  
  const renderOrderItems = () => {
    if (!order) return null;
    
    return (
      <div className="p-4 border-b border-gray-200">
        <h4 className="font-medium text-gray-900 mb-3">Order Items</h4>
        
        <div className="space-y-3">
          {order.items.map((item: any, index: number) => (
            <div key={index} className="flex justify-between items-start">
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-gray-900">
                    {item.quantity}x {item.menu_item_name}
                  </span>
                  {item.item_status && (
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      item.item_status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                      item.item_status === 'PREPARING' ? 'bg-orange-100 text-orange-800' :
                      item.item_status === 'READY' ? 'bg-green-100 text-green-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {item.item_status}
                    </span>
                  )}
                </div>
                
                {item.modifications && item.modifications.length > 0 && (
                  <p className="text-sm text-gray-600 mt-1">
                    Modifications: {item.modifications.join(', ')}
                  </p>
                )}
                
                {item.special_instructions && (
                  <p className="text-sm text-gray-600 mt-1 italic">
                    Note: {item.special_instructions}
                  </p>
                )}
              </div>
              
              <div className="text-right">
                <p className="font-medium text-gray-900">
                  ${(item.unit_price * item.quantity).toFixed(2)}
                </p>
                <p className="text-sm text-gray-500">
                  ${item.unit_price.toFixed(2)} each
                </p>
              </div>
            </div>
          ))}
        </div>
        
        {/* Order Totals */}
        {orderTotals && (
          <div className="mt-4 pt-3 border-t border-gray-200 space-y-1">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Subtotal:</span>
              <span className="text-gray-900">${orderTotals.subtotal.toFixed(2)}</span>
            </div>
            
            {orderTotals.tax > 0 && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Tax:</span>
                <span className="text-gray-900">${orderTotals.tax.toFixed(2)}</span>
              </div>
            )}
            
            {orderTotals.deliveryFee > 0 && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Delivery Fee:</span>
                <span className="text-gray-900">${orderTotals.deliveryFee.toFixed(2)}</span>
              </div>
            )}
            
            <div className="flex justify-between font-medium text-base pt-1 border-t border-gray-200">
              <span className="text-gray-900">Total:</span>
              <span className="text-gray-900">${orderTotals.total.toFixed(2)}</span>
            </div>
          </div>
        )}
      </div>
    );
  };
  
  // ==================== MAIN RENDER ====================
  
  if (!orderId) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-8 ${className}`}>
        <div className="text-center text-gray-500">
          <FileText className="w-8 h-8 mx-auto mb-3 opacity-50" />
          <p>Select an order to view details</p>
        </div>
      </div>
    );
  }
  
  if (isLoadingOrder) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-8 ${className}`}>
        <div className="text-center">
          <LoadingSpinner size="lg" text="Loading order details..." />
        </div>
      </div>
    );
  }
  
  if (orderError || !order) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-8 ${className}`}>
        <div className="text-center text-red-500">
          <AlertTriangle className="w-8 h-8 mx-auto mb-3" />
          <p>Failed to load order details</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-2 text-sm text-orange-600 hover:text-orange-700 underline"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden ${className}`}>
      {/* Header */}
      {renderOrderHeader()}
      
      {/* Content */}
      <div className="max-h-screen overflow-y-auto">
        {/* Customer Information */}
        {renderCustomerInfo()}
        
        {/* Order Items */}
        {renderOrderItems()}
        
        {/* Staff Assignment */}
        {order.staff_assigned && (
          <div className="p-4 border-b border-gray-200">
            <h4 className="font-medium text-gray-900 mb-2">Assigned Staff</h4>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-orange-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">
                  {order.staff_assigned.name}
                </p>
                <p className="text-xs text-gray-500">
                  {order.staff_assigned.role}
                </p>
              </div>
            </div>
          </div>
        )}
        
        {/* Add Notes */}
        <div className="p-4 border-b border-gray-200">
          <AddNotes
            orderId={orderId}
            tenantId={tenantId}
            authToken={authToken}
          />
        </div>
        
        {/* Order Timeline */}
        <div className="p-4">
          {isLoadingEvents ? (
            <div className="text-center py-4">
              <LoadingSpinner size="sm" text="Loading timeline..." />
            </div>
          ) : (
            <OrderTimeline events={events || []} />
          )}
        </div>
      </div>
    </div>
  );
}