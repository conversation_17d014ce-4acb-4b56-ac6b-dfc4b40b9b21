// Kanban-style order status board with drag-and-drop status updates
// Displays orders grouped by status with real-time updates and interactions

'use client';

import React, { useState, useCallback, useMemo } from 'react';
import {
  Clock,
  User,
  MapPin,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Coffee,
  Utensils,
  Timer,
  MessageSquare,
  MoreVertical,
  ArrowRight,
  Star,
  Bell,
} from 'lucide-react';

import { useUpdateOrderStatus } from '@/hooks/useOrderQueries';
// Removed complex auth import
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

import type { 
  RealtimeOrder, 
  OrderStatus, 
  OrderGroupBy,
  StaffRole 
} from '@/types/realtime-orders';

interface OrderStatusBoardProps {
  ordersByStatus: Record<OrderStatus, RealtimeOrder[]>;
  onOrderSelect: (orderId: string) => void;
  selectedOrderId?: string;
  groupBy: OrderGroupBy;
  className?: string;
}

// Status configuration with colors, icons, and allowed transitions
const STATUS_CONFIG = {
  PENDING: {
    label: 'Pending',
    color: 'yellow',
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    textColor: 'text-yellow-800',
    icon: Clock,
    nextStatuses: ['CONFIRMED', 'CANCELLED'],
  },
  CONFIRMED: {
    label: 'Confirmed',
    color: 'blue',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    textColor: 'text-blue-800',
    icon: CheckCircle,
    nextStatuses: ['PREPARING', 'CANCELLED'],
  },
  PREPARING: {
    label: 'Preparing',
    color: 'orange',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    textColor: 'text-orange-800',
    icon: Coffee,
    nextStatuses: ['READY', 'CANCELLED'],
  },
  READY: {
    label: 'Ready',
    color: 'green',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    textColor: 'text-green-800',
    icon: Bell,
    nextStatuses: ['DELIVERED'],
  },
  DELIVERED: {
    label: 'Delivered',
    color: 'gray',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200',
    textColor: 'text-gray-800',
    icon: CheckCircle,
    nextStatuses: [],
  },
  CANCELLED: {
    label: 'Cancelled',
    color: 'red',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    textColor: 'text-red-800',
    icon: XCircle,
    nextStatuses: [],
  },
} as const;

/**
 * Individual Order Card Component
 * Displays order information with status-specific styling and actions
 */
interface OrderCardProps {
  order: RealtimeOrder;
  isSelected: boolean;
  onSelect: () => void;
  onStatusChange: (newStatus: OrderStatus) => void;
  canUpdateStatus: boolean;
}

function OrderCard({ order, isSelected, onSelect, onStatusChange, canUpdateStatus }: OrderCardProps) {
  const [showActions, setShowActions] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  
  // Calculate time since order was created
  const timeSinceCreated = useMemo(() => {
    const now = new Date().getTime();
    const created = new Date(order.created_at).getTime();
    const diffMinutes = Math.floor((now - created) / (1000 * 60));
    
    if (diffMinutes < 60) {
      return `${diffMinutes}m ago`;
    } else {
      const hours = Math.floor(diffMinutes / 60);
      const minutes = diffMinutes % 60;
      return `${hours}h ${minutes}m ago`;
    }
  }, [order.created_at]);
  
  // Determine if order is urgent (taking too long)
  const isUrgent = useMemo(() => {
    const now = new Date().getTime();
    const created = new Date(order.created_at).getTime();
    const diffMinutes = (now - created) / (1000 * 60);
    
    // Different urgency thresholds based on status
    const urgencyThresholds = {
      PENDING: 10,    // 10 minutes to confirm
      CONFIRMED: 15,  // 15 minutes to start preparing
      PREPARING: 30,  // 30 minutes to prepare
      READY: 15,      // 15 minutes to deliver
    };
    
    const threshold = urgencyThresholds[order.status as keyof typeof urgencyThresholds];
    return threshold && diffMinutes > threshold;
  }, [order.created_at, order.status]);
  
  const statusConfig = STATUS_CONFIG[order.status];
  const nextStatuses = statusConfig.nextStatuses;
  
  const handleStatusChange = async (newStatus: OrderStatus) => {
    if (!canUpdateStatus) return;
    
    setIsUpdating(true);
    try {
      await onStatusChange(newStatus);
    } finally {
      setIsUpdating(false);
      setShowActions(false);
    }
  };
  
  return (
    <div
      className={`relative bg-white rounded-lg border-2 transition-all duration-200 cursor-pointer hover:shadow-md ${
        isSelected 
          ? 'border-orange-500 shadow-md ring-2 ring-orange-500 ring-opacity-20' 
          : isUrgent
            ? 'border-red-300 shadow-sm'
            : 'border-gray-200 hover:border-gray-300'
      }`}
      onClick={onSelect}
    >
      {/* Urgent indicator */}
      {isUrgent && (
        <div className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1">
          <AlertTriangle className="w-3 h-3" />
        </div>
      )}
      
      {/* Card Header */}
      <div className="p-4 pb-3">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <span className="font-bold text-gray-900 text-lg">
              {order.order_number}
            </span>
            {order.is_urgent && (
              <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-medium">
                URGENT
              </span>
            )}
          </div>
          
          <div className="relative">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowActions(!showActions);
              }}
              className="p-1 text-gray-400 hover:text-gray-600 rounded"
            >
              <MoreVertical className="w-4 h-4" />
            </button>
            
            {/* Action Menu */}
            {showActions && nextStatuses.length > 0 && canUpdateStatus && (
              <div className="absolute right-0 top-8 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10 min-w-40">
                <div className="px-3 py-1 text-xs font-medium text-gray-500 border-b border-gray-100 mb-1">
                  Update Status
                </div>
                {nextStatuses.map((status) => {
                  const nextConfig = STATUS_CONFIG[status];
                  const Icon = nextConfig.icon;
                  
                  return (
                    <button
                      key={status}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleStatusChange(status);
                      }}
                      disabled={isUpdating}
                      className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors disabled:opacity-50"
                    >
                      <Icon className={`w-4 h-4 ${nextConfig.textColor}`} />
                      <span>{nextConfig.label}</span>
                      {isUpdating && <LoadingSpinner size="sm" />}
                    </button>
                  );
                })}
              </div>
            )}
          </div>
        </div>
        
        {/* Customer Information */}
        <div className="space-y-1 text-sm">
          {order.customer_name && (
            <div className="flex items-center space-x-2 text-gray-600">
              <User className="w-4 h-4" />
              <span>{order.customer_name}</span>
            </div>
          )}
          
          {order.room_number && (
            <div className="flex items-center space-x-2 text-gray-600">
              <MapPin className="w-4 h-4" />
              <span>Room {order.room_number}</span>
            </div>
          )}
          
          <div className="flex items-center space-x-2 text-gray-600">
            <DollarSign className="w-4 h-4" />
            <span className="font-medium">${order.total_amount.toFixed(2)}</span>
          </div>
          
          <div className="flex items-center space-x-2 text-gray-500">
            <Clock className="w-4 h-4" />
            <span>{timeSinceCreated}</span>
          </div>
        </div>
      </div>
      
      {/* Order Items Preview */}
      <div className="px-4 pb-3">
        <div className="text-sm text-gray-600">
          <div className="font-medium mb-1">{order.items.length} item(s):</div>
          <div className="space-y-1">
            {order.items.slice(0, 2).map((item, index) => (
              <div key={index} className="flex justify-between">
                <span className="truncate mr-2">
                  {item.quantity}x Item {item.menuItemId}
                </span>
                <span>${(item.unitPrice * item.quantity).toFixed(2)}</span>
              </div>
            ))}
            {order.items.length > 2 && (
              <div className="text-gray-500 text-xs">
                +{order.items.length - 2} more items
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Staff Assignment */}
      {order.staff_assigned && (
        <div className="px-4 pb-3">
          <div className="flex items-center space-x-2 text-sm">
            <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center">
              <User className="w-3 h-3 text-orange-600" />
            </div>
            <span className="text-gray-600">
              Assigned to {order.staff_assigned.name}
            </span>
          </div>
        </div>
      )}
      
      {/* Footer with timing */}
      <div className={`px-4 py-2 border-t ${statusConfig.bgColor} rounded-b-lg`}>
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-2">
            <statusConfig.icon className={`w-4 h-4 ${statusConfig.textColor}`} />
            <span className={`font-medium ${statusConfig.textColor}`}>
              {statusConfig.label}
            </span>
          </div>
          
          {order.estimated_completion_time && (
            <div className="flex items-center space-x-1 text-gray-600">
              <Timer className="w-3 h-3" />
              <span className="text-xs">
                ETA: {new Date(order.estimated_completion_time).toLocaleTimeString([], { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * Status Column Component
 * Displays a column of orders for a specific status
 */
interface StatusColumnProps {
  status: OrderStatus;
  orders: RealtimeOrder[];
  onOrderSelect: (orderId: string) => void;
  selectedOrderId?: string;
  canUpdateStatus: boolean;
  onStatusChange: (orderId: string, newStatus: OrderStatus) => void;
}

function StatusColumn({ 
  status, 
  orders, 
  onOrderSelect, 
  selectedOrderId, 
  canUpdateStatus,
  onStatusChange 
}: StatusColumnProps) {
  const statusConfig = STATUS_CONFIG[status];
  const StatusIcon = statusConfig.icon;
  
  // Sort orders by urgency and creation time
  const sortedOrders = useMemo(() => {
    return [...orders].sort((a, b) => {
      // Urgent orders first
      if (a.is_urgent && !b.is_urgent) return -1;
      if (!a.is_urgent && b.is_urgent) return 1;
      
      // Then by creation time (oldest first)
      return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
    });
  }, [orders]);
  
  const urgentCount = orders.filter(order => order.is_urgent).length;
  
  return (
    <div className="flex flex-col h-full">
      {/* Column Header */}
      <div className={`p-4 border-b-2 ${statusConfig.borderColor} ${statusConfig.bgColor}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <StatusIcon className={`w-5 h-5 ${statusConfig.textColor}`} />
            <h3 className={`font-semibold ${statusConfig.textColor}`}>
              {statusConfig.label}
            </h3>
            <span className={`px-2 py-1 rounded-full text-xs font-medium bg-white ${statusConfig.textColor}`}>
              {orders.length}
            </span>
          </div>
          
          {urgentCount > 0 && (
            <div className="flex items-center space-x-1 bg-red-100 text-red-800 px-2 py-1 rounded-full">
              <AlertTriangle className="w-3 h-3" />
              <span className="text-xs font-medium">{urgentCount}</span>
            </div>
          )}
        </div>
      </div>
      
      {/* Orders List */}
      <div className="flex-1 p-4 space-y-4 overflow-y-auto min-h-96">
        {sortedOrders.length > 0 ? (
          sortedOrders.map((order) => (
            <OrderCard
              key={order.id}
              order={order}
              isSelected={selectedOrderId === order.id}
              onSelect={() => onOrderSelect(order.id)}
              onStatusChange={(newStatus) => onStatusChange(order.id, newStatus)}
              canUpdateStatus={canUpdateStatus}
            />
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            <StatusIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No {statusConfig.label.toLowerCase()} orders</p>
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * Main Order Status Board Component
 */
export function OrderStatusBoard({
  ordersByStatus,
  onOrderSelect,
  selectedOrderId,
  groupBy,
  className = ''
}: OrderStatusBoardProps) {
  
  // ==================== HOOKS ====================
  
  // Simplified - no complex auth needed
  const tenantId = 'default';
  const authToken = 'mock-token';
  const staffRole = 'ADMIN' as StaffRole;
  
  // Mutation for updating order status
  const updateOrderMutation = useUpdateOrderStatus(tenantId, authToken);
  
  // ==================== PERMISSIONS ====================
  
  // Check if current user can update order status based on role
  const canUpdateStatus = useMemo(() => {
    const rolePermissions = {
      ADMIN: true,
      MANAGER: true,
      CHEF: true,
      WAITER: true,
      RECEPTIONIST: false, // Receptionists can only view
    };
    
    return rolePermissions[staffRole] || false;
  }, [staffRole]);
  
  // ==================== HANDLERS ====================
  
  const handleStatusChange = useCallback(async (orderId: string, newStatus: OrderStatus) => {
    try {
      console.log(`🔄 Updating order ${orderId} status to ${newStatus}`);
      
      await updateOrderMutation.mutateAsync({
        orderId,
        status: newStatus,
      });
      
      console.log(`✅ Order ${orderId} status updated successfully`);
      
    } catch (error) {
      console.error(`❌ Failed to update order ${orderId} status:`, error);
      // Error handling is managed by the mutation hook
    }
  }, [updateOrderMutation]);
  
  // ==================== COMPUTED VALUES ====================
  
  // Get visible statuses based on role
  const visibleStatuses = useMemo(() => {
    const roleStatusAccess = {
      ADMIN: ['PENDING', 'CONFIRMED', 'PREPARING', 'READY', 'DELIVERED', 'CANCELLED'],
      MANAGER: ['PENDING', 'CONFIRMED', 'PREPARING', 'READY', 'DELIVERED', 'CANCELLED'],
      CHEF: ['CONFIRMED', 'PREPARING', 'READY'],
      WAITER: ['READY', 'DELIVERED'],
      RECEPTIONIST: ['PENDING', 'CONFIRMED', 'DELIVERED'],
    } as Record<StaffRole, OrderStatus[]>;
    
    return roleStatusAccess[staffRole] || [];
  }, [staffRole]);
  
  // Get total order count across all visible statuses
  const totalOrders = useMemo(() => {
    return visibleStatuses.reduce((total, status) => {
      return total + (ordersByStatus[status]?.length || 0);
    }, 0);
  }, [ordersByStatus, visibleStatuses]);
  
  // Get urgent order count
  const urgentOrders = useMemo(() => {
    return visibleStatuses.reduce((total, status) => {
      return total + (ordersByStatus[status]?.filter(order => order.is_urgent).length || 0);
    }, 0);
  }, [ordersByStatus, visibleStatuses]);
  
  // ==================== RENDER ====================
  
  if (totalOrders === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-8 ${className}`}>
        <div className="text-center">
          <Utensils className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
          <p className="text-gray-600">
            {groupBy !== 'status' 
              ? `No orders match the current ${groupBy} grouping.`
              : 'Orders will appear here as they come in.'
            }
          </p>
        </div>
      </div>
    );
  }
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden ${className}`}>
      {/* Board Header */}
      <div className="bg-gray-50 border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Order Status Board</h2>
            <p className="text-sm text-gray-600">
              {totalOrders} total orders
              {urgentOrders > 0 && (
                <span className="ml-2 text-red-600 font-medium">
                  • {urgentOrders} urgent
                </span>
              )}
            </p>
          </div>
          
          <div className="text-sm text-gray-500">
            View: {staffRole.toLowerCase()} • Group: {groupBy}
          </div>
        </div>
      </div>
      
      {/* Status Columns */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 divide-x divide-gray-200 min-h-screen">
        {visibleStatuses.map((status) => (
          <StatusColumn
            key={status}
            status={status}
            orders={ordersByStatus[status] || []}
            onOrderSelect={onOrderSelect}
            selectedOrderId={selectedOrderId}
            canUpdateStatus={canUpdateStatus}
            onStatusChange={handleStatusChange}
          />
        ))}
      </div>
    </div>
  );
}