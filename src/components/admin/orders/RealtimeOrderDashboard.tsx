// Real-time order management dashboard with live updates and status management
// This is the main component that orchestrates all order management functionality

'use client';

import React, { useEffect, useState, useCallback, useMemo } from 'react';
// import { useQueryClient } from '@tanstack/react-query';
import {
  Bell,
  Users,
  Clock,
  DollarSign,
  AlertTriangle,
  Settings,
  RefreshCw,
  Wifi,
  WifiOff,
  Search,
  Filter,
  Grid,
  List,
  Play,
  Pause,
  Volume2,
  VolumeX,
} from 'lucide-react';

import { AdminDashboardLayout } from '@/components/admin/AdminDashboardLayout';
// Removed complex auth imports
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

import { createRealtimeManager } from '@/lib/realtime/supabase-realtime';
import { useOrders, useOrderRealtimeSync } from '@/hooks/useOrderQueries';
import { useOrderDashboardStore, useNotificationStore, soundManager } from '@/stores/orderDashboardStore';
// Removed complex auth types

import { OrderStatusBoard } from './OrderStatusBoard';
import { OrderDetailsPanel } from './OrderDetailsPanel';
import { OrderFilters } from './OrderFilters';
import { OrderMetrics } from './OrderMetrics';

import type { 
  RealtimeSubscriptionConfig, 
  OrderDashboardFilters,
  OrderGroupBy,
  StaffRole 
} from '@/types/realtime-orders';

interface RealtimeOrderDashboardProps {
  className?: string;
}

/**
 * Real-time Order Dashboard Component
 * 
 * This component provides a comprehensive real-time view of all orders with:
 * - Live updates via Supabase Realtime
 * - Order status management with role-based permissions
 * - Sound notifications and visual alerts
 * - Advanced filtering and search
 * - Performance metrics and monitoring
 */
export function RealtimeOrderDashboard({ className = '' }: RealtimeOrderDashboardProps) {
  
  // ==================== HOOKS & AUTH ====================
  
  // Simplified - no complex auth needed
  const tenantId = 'default';
  // const queryClient = useQueryClient();
  const staffId = 'admin';
  const staffRole = 'ADMIN' as StaffRole;
  const authToken = 'mock-token';
  
  // ==================== STATE MANAGEMENT ====================
  
  // Zustand stores for state management
  const {
    // Data
    orders,
    isConnected,
    connectionError,
    filters,
    groupBy,
    selectedOrderId,
    lastUpdated,
    updateCount,
    
    // Actions
    setFilters,
    setGroupBy,
    selectOrder,
    setConnectionStatus,
    getFilteredOrders,
    getOrdersByStatus,
    getDashboardMetrics,
  } = useOrderDashboardStore();
  
  const { settings: notificationSettings, updateSettings: updateNotificationSettings } = useNotificationStore();
  
  // Local component state
  const [realtimeManager, setRealtimeManager] = useState<any>(null);
  const [isInitializing, setIsInitializing] = useState(true);
  const [viewMode, setViewMode] = useState<'board' | 'list'>('board');
  const [showFilters, setShowFilters] = useState(false);
  const [soundsEnabled, setSoundsEnabled] = useState(notificationSettings.enableSounds);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // ==================== REACT QUERY INTEGRATION ====================
  
  // Fetch orders using React Query (this provides the initial data and handles API calls)
  const {
    data: queryOrders,
    isLoading: isLoadingOrders,
    isError: ordersError,
    refetch: refetchOrders,
  } = useOrders(tenantId, filters);
  
  // Set up real-time sync between React Query and Zustand
  useOrderRealtimeSync(tenantId);
  
  // ==================== REAL-TIME SETUP ====================
  
  /**
   * Initialize Supabase Realtime connection
   * This sets up tenant-scoped subscriptions based on user role
   */
  const initializeRealtime = useCallback(async () => {
    if (!tenantId || !staffId) {
      console.log('⏳ Waiting for auth state...');
      return;
    }
    
    try {
      setIsInitializing(true);
      
      // Simplified - use direct Supabase client
      const supabaseClient = null; // TODO: Add Supabase client if needed
      
      if (!supabaseClient) {
        console.log('Supabase client not available - using mock data');
        return;
      }
      
      // Create real-time manager
      const manager = createRealtimeManager(supabaseClient);
      
      // Configure subscriptions based on staff role
      const config: RealtimeSubscriptionConfig = {
        tenant_id: tenantId,
        staff_role: staffRole,
        staff_id: staffId,
        subscribeToOrders: true,
        subscribeToOrderEvents: true,
        subscribeToOrderItems: staffRole === 'CHEF', // Only chefs need item-level updates
        enablePresence: true, // Track who's online
        heartbeatInterval: 30000, // 30 seconds
      };
      
      // Set up event listeners
      manager.on('connected', () => {
        console.log('✅ Real-time connected');
        setConnectionStatus(true);
      });
      
      manager.on('disconnected', () => {
        console.log('❌ Real-time disconnected');
        setConnectionStatus(false);
      });
      
      manager.on('error', (error: any) => {
        console.error('❌ Real-time error:', error);
        setConnectionStatus(false, error.message);
      });
      
      manager.on('reconnected', () => {
        console.log('🔄 Real-time reconnected');
        setConnectionStatus(true);
        // Refetch data after reconnection
        refetchOrders();
      });
      
      // Initialize the connection
      await manager.initialize(config);
      
      setRealtimeManager(manager);
      
    } catch (error) {
      console.error('❌ Failed to initialize real-time:', error);
      setConnectionStatus(false, error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setIsInitializing(false);
    }
  }, [tenantId, staffId, staffRole, setConnectionStatus, refetchOrders]);
  
  // Initialize real-time on mount and auth changes
  useEffect(() => {
    initializeRealtime();
    
    // Cleanup on unmount
    return () => {
      if (realtimeManager) {
        realtimeManager.cleanup();
      }
    };
  }, [initializeRealtime]);
  
  // ==================== DATA PROCESSING ====================
  
  // Sync React Query data to Zustand store
  useEffect(() => {
    if (queryOrders && queryOrders.length > 0) {
      useOrderDashboardStore.getState().setOrders(queryOrders);
    }
  }, [queryOrders]);
  
  // Get processed data for display
  const filteredOrders = useMemo(() => getFilteredOrders(), [getFilteredOrders]);
  const ordersByStatus = useMemo(() => getOrdersByStatus(), [getOrdersByStatus]);
  const dashboardMetrics = useMemo(() => getDashboardMetrics(), [getDashboardMetrics]);
  
  // ==================== EVENT HANDLERS ====================
  
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await refetchOrders();
      console.log('🔄 Orders refreshed manually');
    } catch (error) {
      console.error('❌ Failed to refresh orders:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [refetchOrders]);
  
  const handleFiltersChange = useCallback((newFilters: Partial<OrderDashboardFilters>) => {
    setFilters(newFilters);
    console.log('🔍 Filters updated:', newFilters);
  }, [setFilters]);
  
  const handleGroupByChange = useCallback((newGroupBy: OrderGroupBy) => {
    setGroupBy(newGroupBy);
    console.log('📊 Group by changed:', newGroupBy);
  }, [setGroupBy]);
  
  const handleSoundToggle = useCallback(() => {
    const newSoundsEnabled = !soundsEnabled;
    setSoundsEnabled(newSoundsEnabled);
    updateNotificationSettings({ enableSounds: newSoundsEnabled });
    
    if (newSoundsEnabled) {
      soundManager.play('STATUS_CHANGE'); // Test sound
    }
    
    console.log('🔊 Sounds', newSoundsEnabled ? 'enabled' : 'disabled');
  }, [soundsEnabled, updateNotificationSettings]);
  
  // ==================== RENDER HELPERS ====================
  
  const renderConnectionStatus = () => (
    <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
      isConnected 
        ? 'bg-green-100 text-green-800' 
        : 'bg-red-100 text-red-800'
    }`}>
      {isConnected ? (
        <>
          <Wifi className="w-4 h-4" />
          <span className="text-sm font-medium">Live</span>
        </>
      ) : (
        <>
          <WifiOff className="w-4 h-4" />
          <span className="text-sm font-medium">Offline</span>
        </>
      )}
      
      {updateCount > 0 && (
        <span className="text-xs bg-white bg-opacity-50 px-2 py-1 rounded-full">
          {updateCount} updates
        </span>
      )}
    </div>
  );
  
  const renderToolbar = () => (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 bg-white p-4 rounded-lg shadow-sm border border-gray-200">
      {/* Left side - Status and controls */}
      <div className="flex items-center space-x-4">
        {renderConnectionStatus()}
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
            title="Refresh orders"
          >
            <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          </button>
          
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`p-2 rounded-lg transition-colors ${
              showFilters 
                ? 'bg-orange-100 text-orange-600' 
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
            }`}
            title="Toggle filters"
          >
            <Filter className="w-4 h-4" />
          </button>
          
          <button
            onClick={handleSoundToggle}
            className={`p-2 rounded-lg transition-colors ${
              soundsEnabled 
                ? 'bg-blue-100 text-blue-600' 
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
            }`}
            title={soundsEnabled ? 'Disable sounds' : 'Enable sounds'}
          >
            {soundsEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
          </button>
        </div>
      </div>
      
      {/* Center - Search */}
      <div className="flex-1 max-w-md mx-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search orders, customers, rooms..."
            value={filters.search_query || ''}
            onChange={(e) => handleFiltersChange({ search_query: e.target.value })}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
          />
        </div>
      </div>
      
      {/* Right side - View controls */}
      <div className="flex items-center space-x-2">
        <div className="flex items-center border border-gray-300 rounded-lg overflow-hidden">
          <button
            onClick={() => setViewMode('board')}
            className={`p-2 transition-colors ${
              viewMode === 'board' 
                ? 'bg-orange-100 text-orange-600' 
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            title="Board view"
          >
            <Grid className="w-4 h-4" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 transition-colors ${
              viewMode === 'list' 
                ? 'bg-orange-100 text-orange-600' 
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            title="List view"
          >
            <List className="w-4 h-4" />
          </button>
        </div>
        
        <select
          value={groupBy}
          onChange={(e) => handleGroupByChange(e.target.value as OrderGroupBy)}
          className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <option value="status">Group by Status</option>
          <option value="staff">Group by Staff</option>
          <option value="room">Group by Room</option>
          <option value="time">Group by Time</option>
          <option value="none">No Grouping</option>
        </select>
      </div>
    </div>
  );
  
  const renderContent = () => {
    if (isInitializing || isLoadingOrders) {
      return (
        <div className="flex items-center justify-center py-20">
          <LoadingSpinner size="lg" text="Loading order dashboard..." />
        </div>
      );
    }
    
    if (ordersError) {
      return (
        <div className="text-center py-20">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load orders</h3>
          <p className="text-gray-600 mb-4">There was an error loading the order data.</p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      );
    }
    
    return (
      <div className="space-y-6">
        {/* Metrics Dashboard */}
        <OrderMetrics metrics={dashboardMetrics} />
        
        {/* Filters Panel */}
        {showFilters && (
          <OrderFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onClose={() => setShowFilters(false)}
          />
        )}
        
        {/* Main Content Area */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Orders Display */}
          <div className="lg:col-span-3">
            {viewMode === 'board' ? (
              <OrderStatusBoard
                ordersByStatus={ordersByStatus}
                onOrderSelect={selectOrder}
                selectedOrderId={selectedOrderId}
                groupBy={groupBy}
              />
            ) : (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Orders List</h3>
                {/* List view would be implemented here */}
                <p className="text-gray-500">List view coming soon...</p>
              </div>
            )}
          </div>
          
          {/* Order Details Panel */}
          <div className="lg:col-span-1">
            <OrderDetailsPanel
              orderId={selectedOrderId || null}
              onClose={() => selectOrder(undefined)}
            />
          </div>
        </div>
        
        {/* Connection Status Footer */}
        {connectionError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
              <div className="text-red-800">
                <p className="font-medium">Connection Error</p>
                <p className="mt-1 text-sm">{connectionError}</p>
                <button
                  onClick={initializeRealtime}
                  className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
                >
                  Retry connection
                </button>
              </div>
            </div>
          </div>
        )}
        
        {lastUpdated && (
          <div className="text-center text-sm text-gray-500">
            Last updated: {new Date(lastUpdated).toLocaleTimeString()}
          </div>
        )}
      </div>
    );
  };
  
  // ==================== MAIN RENDER ====================
  
  return (
    <AdminDashboardLayout title="Order Management" className={className}>
        <div className="space-y-6">
          {/* Toolbar */}
          {renderToolbar()}
          
          {/* Main Content */}
          {renderContent()}
        </div>
      </AdminDashboardLayout>
  );
}