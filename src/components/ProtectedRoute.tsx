/**
 * Simple Protected Route Component
 * Basic route protection for admin pages
 */

'use client'

import { useAuth } from '@/lib/simple-auth'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireAdmin?: boolean
}

export function ProtectedRoute({ children, requireAdmin = false }: ProtectedRouteProps) {
  const { isAuthenticated, isAdmin, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push('/login')
        return
      }
      
      if (requireAdmin && !isAdmin()) {
        router.push('/login')
        return
      }
    }
  }, [isAuthenticated, isAdmin, isLoading, router, requireAdmin])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500"></div>
      </div>
    )
  }

  if (!isAuthenticated || (requireAdmin && !isAdmin())) {
    return null
  }

  return <>{children}</>
}