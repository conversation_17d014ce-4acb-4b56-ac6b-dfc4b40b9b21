# SIMPLIFIED RESTAURANT ORDERING SYSTEM
# Environment Variables for Core Functionality Only

# =====================================================
# APPLICATION CONFIGURATION
# =====================================================

# Application environment
NODE_ENV=development

# Application URL
NEXT_PUBLIC_APP_URL=http://localhost:3000

# =====================================================
# SUPABASE (DATABASE & BACKEND)
# =====================================================

# Supabase Project URL
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co

# Supabase Anon Key
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Supabase Service Role Key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Database URL
DATABASE_URL=postgresql://postgres:password@localhost:5432/restaurant

# =====================================================
# BASIC AUTHENTICATION
# =====================================================

# Session secret
SESSION_SECRET=your-session-secret-key

# =====================================================
# OPTIONAL FEATURES
# =====================================================

# QR Code generation settings
QR_CODE_SIZE=400
QR_CODE_LOGO_ENABLED=true

# Email notifications (optional)
EMAIL_ENABLED=false
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# File uploads (optional)
UPLOAD_MAX_SIZE=5MB
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/webp

# =====================================================
# DEVELOPMENT SETTINGS
# =====================================================

# Enable debug logging
DEBUG=false

# Mock data for development
USE_MOCK_DATA=false

# Hot reload settings
NEXT_PUBLIC_ENABLE_DEVTOOLS=true