/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/qr/page";
exports.ids = ["app/admin/qr/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fqr%2Fpage&page=%2Fadmin%2Fqr%2Fpage&appPaths=%2Fadmin%2Fqr%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fqr%2Fpage.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fqr%2Fpage&page=%2Fadmin%2Fqr%2Fpage&appPaths=%2Fadmin%2Fqr%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fqr%2Fpage.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: [\n        'qr',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/qr/page.tsx */ \"(rsc)/./src/app/admin/qr/page.tsx\")), \"/Users/<USER>/Desktop/BHEEMDINE/src/app/admin/qr/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Desktop/BHEEMDINE/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/BHEEMDINE/src/app/admin/qr/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/admin/qr/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/qr/page\",\n        pathname: \"/admin/qr\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fqr%2Fpage&page=%2Fadmin%2Fqr%2Fpage&appPaths=%2Fadmin%2Fqr%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fqr%2Fpage.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp%2Fglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp%2Fglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp%2Fadmin%2Fqr%2Fpage.tsx&server=true!":
/*!*****************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp%2Fadmin%2Fqr%2Fpage.tsx&server=true! ***!
  \*****************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/qr/page.tsx */ \"(ssr)/./src/app/admin/qr/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUlMkZzcmMlMkZhcHAlMkZhZG1pbiUyRnFyJTJGcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmhlZW1kaW5lLz81NGI4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3NoaXZhL0Rlc2t0b3AvQkhFRU1ESU5FL3NyYy9hcHAvYWRtaW4vcXIvcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp%2Fadmin%2Fqr%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/admin/qr/page.tsx":
/*!***********************************!*\
  !*** ./src/app/admin/qr/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QRManagementPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_admin_qr_QRManagementDashboard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/admin/qr/QRManagementDashboard */ \"(ssr)/./src/components/admin/qr/QRManagementDashboard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction QRManagementPage() {\n    // In a real app, this would come from auth context\n    const tenantId = \"demo-tenant-id\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_qr_QRManagementDashboard__WEBPACK_IMPORTED_MODULE_1__.QRManagementDashboard, {\n            tenantId: tenantId,\n            className: \"w-full\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/admin/qr/page.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/admin/qr/page.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2FkbWluL3FyL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRW9GO0FBRXJFLFNBQVNDO0lBQ3RCLG1EQUFtRDtJQUNuRCxNQUFNQyxXQUFXO0lBRWpCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDSiw2RkFBcUJBO1lBQ3BCRSxVQUFVQTtZQUNWRSxXQUFVOzs7Ozs7Ozs7OztBQUlsQiIsInNvdXJjZXMiOlsid2VicGFjazovL2JoZWVtZGluZS8uL3NyYy9hcHAvYWRtaW4vcXIvcGFnZS50c3g/MDU0OSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFFSTWFuYWdlbWVudERhc2hib2FyZCB9IGZyb20gJ0AvY29tcG9uZW50cy9hZG1pbi9xci9RUk1hbmFnZW1lbnREYXNoYm9hcmQnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBRUk1hbmFnZW1lbnRQYWdlKCkge1xuICAvLyBJbiBhIHJlYWwgYXBwLCB0aGlzIHdvdWxkIGNvbWUgZnJvbSBhdXRoIGNvbnRleHRcbiAgY29uc3QgdGVuYW50SWQgPSAnZGVtby10ZW5hbnQtaWQnO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgPFFSTWFuYWdlbWVudERhc2hib2FyZCBcbiAgICAgICAgdGVuYW50SWQ9e3RlbmFudElkfVxuICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn0iXSwibmFtZXMiOlsiUVJNYW5hZ2VtZW50RGFzaGJvYXJkIiwiUVJNYW5hZ2VtZW50UGFnZSIsInRlbmFudElkIiwiZGl2IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/admin/qr/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/admin/qr/QRManagementDashboard.tsx":
/*!***********************************************************!*\
  !*** ./src/components/admin/qr/QRManagementDashboard.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QRManagementDashboard: () => (/* binding */ QRManagementDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Download,Filter,Grid,List,QrCode,RefreshCw,Search,Settings,Users,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Download,Filter,Grid,List,QrCode,RefreshCw,Search,Settings,Users,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Download,Filter,Grid,List,QrCode,RefreshCw,Search,Settings,Users,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Download,Filter,Grid,List,QrCode,RefreshCw,Search,Settings,Users,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Download,Filter,Grid,List,QrCode,RefreshCw,Search,Settings,Users,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Download,Filter,Grid,List,QrCode,RefreshCw,Search,Settings,Users,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Download,Filter,Grid,List,QrCode,RefreshCw,Search,Settings,Users,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Download,Filter,Grid,List,QrCode,RefreshCw,Search,Settings,Users,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Download,Filter,Grid,List,QrCode,RefreshCw,Search,Settings,Users,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Download,Filter,Grid,List,QrCode,RefreshCw,Search,Settings,Users,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Download,Filter,Grid,List,QrCode,RefreshCw,Search,Settings,Users,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Download,Filter,Grid,List,QrCode,RefreshCw,Search,Settings,Users,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Download,Filter,Grid,List,QrCode,RefreshCw,Search,Settings,Users,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Download,Filter,Grid,List,QrCode,RefreshCw,Search,Settings,Users,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Clock,Download,Filter,Grid,List,QrCode,RefreshCw,Search,Settings,Users,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(ssr)/./src/components/ui/LoadingSpinner.tsx\");\n/* harmony import */ var _hooks_useCrud__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useCrud */ \"(ssr)/./src/hooks/useCrud.ts\");\n/* harmony import */ var _types_qr_management__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/types/qr-management */ \"(ssr)/./src/types/qr-management.ts\");\n/* harmony import */ var _services_qr_generation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/qr-generation */ \"(ssr)/./src/services/qr-generation.ts\");\n// Main QR Management Dashboard - Comprehensive room and QR code management interface\n// Provides intuitive controls for generating, previewing, and downloading QR codes\n/* __next_internal_client_entry_do_not_use__ QRManagementDashboard auto */ \n\n\n\n\n\n\nfunction QRManagementDashboard({ tenantId, className = \"\" }) {\n    // =====================================================\n    // STATE MANAGEMENT\n    // =====================================================\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        rooms: [],\n        qrCodes: new Map(),\n        selectedRooms: new Set(),\n        isLoading: false,\n        isGenerating: false,\n        isExporting: false,\n        error: null,\n        filters: {},\n        sortOptions: {\n            field: \"roomNumber\",\n            direction: \"asc\"\n        },\n        viewMode: \"grid\",\n        bulkOperation: null,\n        defaultQROptions: _types_qr_management__WEBPACK_IMPORTED_MODULE_4__.DEFAULT_QR_OPTIONS,\n        customizationOptions: {\n            size: 256,\n            foregroundColor: \"#000000\",\n            backgroundColor: \"#FFFFFF\",\n            errorCorrectionLevel: \"M\",\n            includelogo: true,\n            logoSize: 20,\n            margin: 4,\n            border: false,\n            borderWidth: 1,\n            borderColor: \"#000000\"\n        },\n        exportProgress: {\n            current: 0,\n            total: 0,\n            isActive: false\n        },\n        notifications: []\n    });\n    // Initialize CRUD operations for rooms\n    const [roomsCrudState, roomsCrudActions] = (0,_hooks_useCrud__WEBPACK_IMPORTED_MODULE_3__.useCrud)({\n        apiEndpoint: `/api/tenants/${tenantId}/rooms`,\n        optimisticUpdates: true,\n        onSuccess: (action, room)=>{\n            addNotification({\n                type: \"success\",\n                title: \"Success\",\n                message: `Room ${action === \"create\" ? \"created\" : action === \"update\" ? \"updated\" : \"deleted\"} successfully`,\n                autoClose: true,\n                duration: 3000\n            });\n        },\n        onError: (action, error)=>{\n            addNotification({\n                type: \"error\",\n                title: \"Error\",\n                message: `Failed to ${action} room: ${error.message}`,\n                autoClose: true,\n                duration: 5000\n            });\n        }\n    });\n    // Initialize QR generation service\n    const qrService = new _services_qr_generation__WEBPACK_IMPORTED_MODULE_5__.QRCodeGenerationService();\n    // =====================================================\n    // UTILITY FUNCTIONS\n    // =====================================================\n    const updateState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((updates)=>{\n        setState((prev)=>({\n                ...prev,\n                ...updates\n            }));\n    }, []);\n    const addNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((notification)=>{\n        const newNotification = {\n            ...notification,\n            id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            timestamp: new Date().toISOString()\n        };\n        updateState({\n            notifications: [\n                ...state.notifications,\n                newNotification\n            ]\n        });\n        // Auto-remove notification if specified\n        if (notification.autoClose && notification.duration) {\n            setTimeout(()=>{\n                removeNotification(newNotification.id);\n            }, notification.duration);\n        }\n    }, [\n        state.notifications,\n        updateState\n    ]);\n    const removeNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((notificationId)=>{\n        updateState({\n            notifications: state.notifications.filter((n)=>n.id !== notificationId)\n        });\n    }, [\n        state.notifications,\n        updateState\n    ]);\n    // =====================================================\n    // DATA OPERATIONS\n    // =====================================================\n    const loadRooms = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filters)=>{\n        updateState({\n            isLoading: true,\n            error: null\n        });\n        try {\n            const rooms = await roomsCrudActions.fetchItems(filters);\n            updateState({\n                rooms,\n                isLoading: false\n            });\n        } catch (error) {\n            updateState({\n                error: error instanceof Error ? error.message : \"Failed to load rooms\",\n                isLoading: false\n            });\n        }\n    }, [\n        roomsCrudActions,\n        updateState\n    ]);\n    const generateQRCode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (roomId, options)=>{\n        const room = state.rooms.find((r)=>r.id === roomId);\n        if (!room) {\n            throw new Error(\"Room not found\");\n        }\n        updateState({\n            isGenerating: true\n        });\n        try {\n            const qrOptions = {\n                ...state.defaultQROptions,\n                ...options\n            };\n            const result = await qrService.generateQRCode(room, qrOptions);\n            if (result.success) {\n                // Update QR codes map\n                const newQRCodes = new Map(state.qrCodes);\n                newQRCodes.set(roomId, result.qrCode);\n                updateState({\n                    qrCodes: newQRCodes,\n                    isGenerating: false\n                });\n                addNotification({\n                    type: \"success\",\n                    title: \"QR Code Generated\",\n                    message: `QR code for room ${room.roomNumber} generated successfully`,\n                    autoClose: true,\n                    duration: 3000\n                });\n            } else {\n                throw new Error(result.error || \"QR generation failed\");\n            }\n            return result;\n        } catch (error) {\n            updateState({\n                isGenerating: false\n            });\n            addNotification({\n                type: \"error\",\n                title: \"Generation Failed\",\n                message: error instanceof Error ? error.message : \"QR code generation failed\",\n                autoClose: true,\n                duration: 5000\n            });\n            throw error;\n        }\n    }, [\n        state.rooms,\n        state.defaultQROptions,\n        state.qrCodes,\n        qrService,\n        updateState,\n        addNotification\n    ]);\n    const bulkGenerateQRCodes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (roomIds, options)=>{\n        const rooms = state.rooms.filter((r)=>roomIds.includes(r.id));\n        if (rooms.length === 0) {\n            throw new Error(\"No valid rooms selected\");\n        }\n        updateState({\n            isGenerating: true,\n            exportProgress: {\n                current: 0,\n                total: rooms.length,\n                isActive: true\n            }\n        });\n        try {\n            const qrOptions = {\n                ...state.defaultQROptions,\n                ...options\n            };\n            const result = await qrService.bulkGenerateQRCodes(rooms, qrOptions, (current, total)=>{\n                updateState({\n                    exportProgress: {\n                        current,\n                        total,\n                        isActive: true\n                    }\n                });\n            });\n            // Update QR codes map with successful generations\n            const newQRCodes = new Map(state.qrCodes);\n            result.results.forEach((qrResult, index)=>{\n                if (qrResult.success) {\n                    newQRCodes.set(rooms[index].id, qrResult.qrCode);\n                }\n            });\n            updateState({\n                qrCodes: newQRCodes,\n                isGenerating: false,\n                exportProgress: {\n                    current: 0,\n                    total: 0,\n                    isActive: false\n                }\n            });\n            addNotification({\n                type: result.success ? \"success\" : \"warning\",\n                title: \"Bulk Generation Complete\",\n                message: `Generated ${result.successCount}/${result.totalProcessed} QR codes successfully`,\n                autoClose: true,\n                duration: 5000\n            });\n            return result;\n        } catch (error) {\n            updateState({\n                isGenerating: false,\n                exportProgress: {\n                    current: 0,\n                    total: 0,\n                    isActive: false\n                }\n            });\n            addNotification({\n                type: \"error\",\n                title: \"Bulk Generation Failed\",\n                message: error instanceof Error ? error.message : \"Bulk QR generation failed\",\n                autoClose: true,\n                duration: 5000\n            });\n            throw error;\n        }\n    }, [\n        state.rooms,\n        state.defaultQROptions,\n        state.qrCodes,\n        qrService,\n        updateState,\n        addNotification\n    ]);\n    const exportQRCode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (roomId, format)=>{\n        const qrCode = state.qrCodes.get(roomId);\n        if (!qrCode) {\n            throw new Error(\"QR code not found. Please generate it first.\");\n        }\n        updateState({\n            isExporting: true\n        });\n        try {\n            const exportOptions = {\n                format,\n                quality: 95,\n                size: state.customizationOptions.size\n            };\n            const result = await qrService.exportQRCode(qrCode, exportOptions);\n            if (result.success && result.downloadUrl) {\n                // Trigger download\n                const link = document.createElement(\"a\");\n                link.href = result.downloadUrl;\n                link.download = result.filename;\n                document.body.appendChild(link);\n                link.click();\n                document.body.removeChild(link);\n                // Clean up URL\n                URL.revokeObjectURL(result.downloadUrl);\n                addNotification({\n                    type: \"success\",\n                    title: \"Export Complete\",\n                    message: `QR code exported as ${format.toUpperCase()}`,\n                    autoClose: true,\n                    duration: 3000\n                });\n            } else {\n                throw new Error(result.error || \"Export failed\");\n            }\n            updateState({\n                isExporting: false\n            });\n            return result;\n        } catch (error) {\n            updateState({\n                isExporting: false\n            });\n            addNotification({\n                type: \"error\",\n                title: \"Export Failed\",\n                message: error instanceof Error ? error.message : \"QR code export failed\",\n                autoClose: true,\n                duration: 5000\n            });\n            throw error;\n        }\n    }, [\n        state.qrCodes,\n        state.customizationOptions,\n        qrService,\n        updateState,\n        addNotification\n    ]);\n    // =====================================================\n    // SELECTION MANAGEMENT\n    // =====================================================\n    const toggleRoomSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((roomId)=>{\n        const newSelection = new Set(state.selectedRooms);\n        if (newSelection.has(roomId)) {\n            newSelection.delete(roomId);\n        } else {\n            newSelection.add(roomId);\n        }\n        updateState({\n            selectedRooms: newSelection\n        });\n    }, [\n        state.selectedRooms,\n        updateState\n    ]);\n    const selectAllRooms = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const allRoomIds = state.rooms.map((room)=>room.id);\n        updateState({\n            selectedRooms: new Set(allRoomIds)\n        });\n    }, [\n        state.rooms,\n        updateState\n    ]);\n    const clearSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        updateState({\n            selectedRooms: new Set()\n        });\n    }, [\n        updateState\n    ]);\n    // =====================================================\n    // FILTERS AND SORTING\n    // =====================================================\n    const applyFilters = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newFilters)=>{\n        updateState({\n            filters: {\n                ...state.filters,\n                ...newFilters\n            }\n        });\n        loadRooms({\n            ...state.filters,\n            ...newFilters\n        });\n    }, [\n        state.filters,\n        updateState,\n        loadRooms\n    ]);\n    const applySorting = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((sortOptions)=>{\n        updateState({\n            sortOptions\n        });\n        // Sort rooms locally or reload with new sort\n        const sortedRooms = [\n            ...state.rooms\n        ].sort((a, b)=>{\n            const aValue = a[sortOptions.field] ?? \"\";\n            const bValue = b[sortOptions.field] ?? \"\";\n            if (sortOptions.direction === \"asc\") {\n                return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n            } else {\n                return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n            }\n        });\n        updateState({\n            rooms: sortedRooms\n        });\n    }, [\n        state.rooms,\n        updateState\n    ]);\n    // =====================================================\n    // EFFECTS\n    // =====================================================\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadRooms();\n    }, [\n        loadRooms\n    ]);\n    // =====================================================\n    // RENDER HELPERS\n    // =====================================================\n    const renderRoom = (room)=>{\n        const isSelected = state.selectedRooms.has(room.id);\n        const hasQRCode = state.qrCodes.has(room.id);\n        const statusInfo = _types_qr_management__WEBPACK_IMPORTED_MODULE_4__.ROOM_STATUSES.find((s)=>s.value === room.status);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `relative p-4 rounded-lg border-2 transition-all cursor-pointer hover:shadow-md ${isSelected ? \"border-orange-500 bg-orange-50\" : \"border-gray-200 bg-white hover:border-gray-300\"}`,\n            onClick: ()=>toggleRoomSelection(room.id),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-2 left-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"checkbox\",\n                        checked: isSelected,\n                        onChange: ()=>toggleRoomSelection(room.id),\n                        className: \"w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                    lineNumber: 435,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-2 right-2\",\n                    children: hasQRCode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"w-5 h-5 text-green-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-5 h-5 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                    lineNumber: 445,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: [\n                                        \"Room \",\n                                        room.roomNumber\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 13\n                                }, this),\n                                room.floor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Floor \",\n                                        room.floor\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 455,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `px-2 py-1 rounded-full text-xs font-medium ${statusInfo?.color || \"bg-gray-100 text-gray-800\"}`,\n                                children: statusInfo?.label || room.status\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-1 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        room.capacity,\n                                        \" guests\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, this),\n                        hasQRCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-gray-100 rounded border flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-8 h-8 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-2\",\n                            children: hasQRCode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            exportQRCode(room.id, \"png\");\n                                        },\n                                        className: \"p-2 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded transition-colors\",\n                                        title: \"Download PNG\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            generateQRCode(room.id);\n                                        },\n                                        className: \"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors\",\n                                        title: \"Regenerate QR\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    generateQRCode(room.id);\n                                },\n                                className: \"p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded transition-colors\",\n                                title: \"Generate QR Code\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                    lineNumber: 453,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, room.id, true, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n            lineNumber: 425,\n            columnNumber: 7\n        }, this);\n    };\n    const renderNotifications = ()=>{\n        if (state.notifications.length === 0) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed top-4 right-4 z-50 space-y-2\",\n            children: state.notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `\n              p-4 rounded-lg shadow-lg max-w-sm transition-all transform\n              ${notification.type === \"success\" ? \"bg-green-100 border border-green-200\" : notification.type === \"error\" ? \"bg-red-100 border border-red-200\" : notification.type === \"warning\" ? \"bg-yellow-100 border border-yellow-200\" : \"bg-blue-100 border border-blue-200\"}\n            `,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        notification.type === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-5 h-5 text-green-600 mt-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 53\n                                        }, this),\n                                        notification.type === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-5 h-5 text-red-600 mt-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 51\n                                        }, this),\n                                        notification.type === \"warning\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-5 h-5 text-yellow-600 mt-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 53\n                                        }, this),\n                                        notification.type === \"info\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-5 h-5 text-blue-600 mt-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 50\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: notification.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-700\",\n                                                    children: notification.message\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>removeNotification(notification.id),\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 13\n                        }, this),\n                        notification.action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: notification.action.onClick,\n                                className: \"text-sm font-medium text-orange-600 hover:text-orange-700 transition-colors\",\n                                children: notification.action.label\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                lineNumber: 569,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 568,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, notification.id, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                    lineNumber: 535,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n            lineNumber: 533,\n            columnNumber: 7\n        }, this);\n    };\n    // =====================================================\n    // MAIN RENDER\n    // =====================================================\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `min-h-screen bg-gray-50 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900 flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-8 h-8 text-orange-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"QR Code Management\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"Generate, preview, and download QR codes for room access\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    state.selectedRooms.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 px-3 py-2 bg-orange-50 rounded-lg border border-orange-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-orange-700\",\n                                                children: [\n                                                    state.selectedRooms.size,\n                                                    \" selected\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>bulkGenerateQRCodes(Array.from(state.selectedRooms)),\n                                                disabled: state.isGenerating,\n                                                className: \"px-3 py-1 bg-orange-600 text-white rounded text-sm hover:bg-orange-700 transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    state.isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_2__.LoadingSpinner, {\n                                                        size: \"sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 43\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 74\n                                                    }, this),\n                                                    \"Generate QRs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearSelection,\n                                                className: \"px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors\",\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1 bg-gray-100 rounded-lg p-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>updateState({\n                                                        viewMode: \"grid\"\n                                                    }),\n                                                className: `p-2 rounded transition-colors ${state.viewMode === \"grid\" ? \"bg-white text-orange-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                                    lineNumber: 639,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>updateState({\n                                                        viewMode: \"list\"\n                                                    }),\n                                                className: `p-2 rounded transition-colors ${state.viewMode === \"list\" ? \"bg-white text-orange-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                        lineNumber: 592,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                    lineNumber: 591,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                lineNumber: 590,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search rooms...\",\n                                            className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500\",\n                                            onChange: (e)=>applyFilters({\n                                                    search: e.target.value\n                                                })\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                            lineNumber: 674,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                            lineNumber: 675,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                    lineNumber: 673,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"QR Options\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                    lineNumber: 678,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 660,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: selectAllRooms,\n                                    className: \"px-3 py-2 text-sm text-gray-600 hover:text-gray-900 transition-colors\",\n                                    children: \"Select All\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>loadRooms(),\n                                    disabled: state.isLoading,\n                                    className: \"flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50\",\n                                    children: [\n                                        state.isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_2__.LoadingSpinner, {\n                                            size: \"sm\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 34\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 65\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 684,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                    lineNumber: 659,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                lineNumber: 658,\n                columnNumber: 7\n            }, this),\n            state.exportProgress.isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-3 bg-blue-50 border-b border-blue-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_2__.LoadingSpinner, {\n                                    size: \"sm\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-blue-700\",\n                                    children: [\n                                        \"Processing QR codes... \",\n                                        state.exportProgress.current,\n                                        \"/\",\n                                        state.exportProgress.total\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 708,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 mx-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-blue-200 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                    style: {\n                                        width: `${state.exportProgress.current / state.exportProgress.total * 100}%`\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                    lineNumber: 717,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                                lineNumber: 716,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 715,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                    lineNumber: 707,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                lineNumber: 706,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-6\",\n                children: state.isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_2__.LoadingSpinner, {\n                            size: \"lg\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 733,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-3 text-gray-600\",\n                            children: \"Loading rooms...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 734,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                    lineNumber: 732,\n                    columnNumber: 11\n                }, this) : state.error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-12 h-12 text-red-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 738,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"Error Loading Rooms\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 739,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: state.error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 740,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>loadRooms(),\n                            className: \"px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors\",\n                            children: \"Try Again\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 741,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                    lineNumber: 737,\n                    columnNumber: 11\n                }, this) : state.rooms.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Clock_Download_Filter_Grid_List_QrCode_RefreshCw_Search_Settings_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 750,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No Rooms Found\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 751,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Get started by adding rooms to your tenant account.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 752,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors\",\n                            children: \"Add Room\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                            lineNumber: 755,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                    lineNumber: 749,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `\n            ${state.viewMode === \"grid\" ? \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\" : \"space-y-4\"}\n          `,\n                    children: state.rooms.map(renderRoom)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                    lineNumber: 760,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n                lineNumber: 730,\n                columnNumber: 7\n            }, this),\n            renderNotifications()\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/admin/qr/QRManagementDashboard.tsx\",\n        lineNumber: 588,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/admin/qr/QRManagementDashboard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/LoadingSpinner.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/LoadingSpinner.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n// Loading spinner component\n/* __next_internal_client_entry_do_not_use__ LoadingSpinner auto */ \n\n\nfunction LoadingSpinner({ size = \"md\", text, className = \"\", fullScreen = false }) {\n    const sizeClasses = {\n        sm: \"w-4 h-4\",\n        md: \"w-6 h-6\",\n        lg: \"w-8 h-8\",\n        xl: \"w-12 h-12\"\n    };\n    const textSizeClasses = {\n        sm: \"text-sm\",\n        md: \"text-base\",\n        lg: \"text-lg\",\n        xl: \"text-xl\"\n    };\n    const spinner = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center justify-center space-x-2 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: `${sizeClasses[size]} animate-spin text-orange-600`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/ui/LoadingSpinner.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: `${textSizeClasses[size]} text-gray-600 font-medium`,\n                children: text\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/ui/LoadingSpinner.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/ui/LoadingSpinner.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n    if (fullScreen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-6\",\n                children: spinner\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/ui/LoadingSpinner.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/components/ui/LoadingSpinner.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this);\n    }\n    return spinner;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useCrud.ts":
/*!******************************!*\
  !*** ./src/hooks/useCrud.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCrud: () => (/* binding */ useCrud)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n// Reusable CRUD hook for data operations with optimistic updates and error handling\n/* __next_internal_client_entry_do_not_use__ useCrud auto */ \nfunction useCrud(options) {\n    // =====================================================\n    // STATE MANAGEMENT\n    // =====================================================\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        items: options.initialData || [],\n        loading: false,\n        error: null,\n        selectedItems: new Set(),\n        isSubmitting: false,\n        lastAction: null\n    });\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // =====================================================\n    // UTILITY FUNCTIONS\n    // =====================================================\n    const updateState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((updates)=>{\n        setState((prev)=>({\n                ...prev,\n                ...updates\n            }));\n    }, []);\n    const handleError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((action, error)=>{\n        console.error(`CRUD ${action} error:`, error);\n        updateState({\n            error: error.message,\n            loading: false,\n            isSubmitting: false,\n            lastAction: action\n        });\n        options.onError?.(action, error);\n    }, [\n        options,\n        updateState\n    ]);\n    const handleSuccess = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((action, item)=>{\n        updateState({\n            error: null,\n            loading: false,\n            isSubmitting: false,\n            lastAction: action\n        });\n        if (item) {\n            options.onSuccess?.(action, item);\n        }\n    }, [\n        options,\n        updateState\n    ]);\n    const makeRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (url, options = {})=>{\n        // Cancel previous request if still pending\n        if (abortControllerRef.current) {\n            abortControllerRef.current.abort();\n        }\n        abortControllerRef.current = new AbortController();\n        const response = await fetch(url, {\n            ...options,\n            signal: abortControllerRef.current.signal,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...options.headers\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\n        }\n        return response;\n    }, []);\n    // =====================================================\n    // CRUD OPERATIONS\n    // =====================================================\n    const fetchItems = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (params)=>{\n        updateState({\n            loading: true,\n            error: null,\n            lastAction: \"read\"\n        });\n        try {\n            const url = new URL(options.apiEndpoint);\n            if (params) {\n                Object.entries(params).forEach(([key, value])=>{\n                    if (value !== undefined && value !== null) {\n                        url.searchParams.append(key, String(value));\n                    }\n                });\n            }\n            const response = await makeRequest(url.toString());\n            const data = await response.json();\n            const items = Array.isArray(data) ? data : data.items || data.data || [];\n            updateState({\n                items,\n                loading: false,\n                lastAction: \"read\"\n            });\n            handleSuccess(\"read\");\n            return items;\n        } catch (error) {\n            if (error instanceof Error && error.name !== \"AbortError\") {\n                handleError(\"read\", error);\n            }\n            return [];\n        }\n    }, [\n        options.apiEndpoint,\n        makeRequest,\n        updateState,\n        handleError,\n        handleSuccess\n    ]);\n    const createItem = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (data)=>{\n        updateState({\n            isSubmitting: true,\n            error: null,\n            lastAction: \"create\"\n        });\n        try {\n            const response = await makeRequest(options.apiEndpoint, {\n                method: \"POST\",\n                body: JSON.stringify(data)\n            });\n            const newItem = await response.json();\n            // Optimistic update\n            if (options.optimisticUpdates !== false) {\n                updateState({\n                    items: [\n                        ...state.items,\n                        newItem\n                    ],\n                    isSubmitting: false\n                });\n            }\n            handleSuccess(\"create\", newItem);\n            return newItem;\n        } catch (error) {\n            handleError(\"create\", error);\n            throw error;\n        }\n    }, [\n        options.apiEndpoint,\n        options.optimisticUpdates,\n        state.items,\n        makeRequest,\n        updateState,\n        handleError,\n        handleSuccess\n    ]);\n    const updateItem = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (id, data)=>{\n        updateState({\n            isSubmitting: true,\n            error: null,\n            lastAction: \"update\"\n        });\n        // Optimistic update\n        let originalItems = [];\n        if (options.optimisticUpdates !== false) {\n            originalItems = [\n                ...state.items\n            ];\n            const optimisticItems = state.items.map((item)=>item.id === id ? {\n                    ...item,\n                    ...data\n                } : item);\n            updateState({\n                items: optimisticItems\n            });\n        }\n        try {\n            const response = await makeRequest(`${options.apiEndpoint}/${id}`, {\n                method: \"PUT\",\n                body: JSON.stringify(data)\n            });\n            const updatedItem = await response.json();\n            // Update with server response\n            updateState({\n                items: state.items.map((item)=>item.id === id ? updatedItem : item),\n                isSubmitting: false\n            });\n            handleSuccess(\"update\", updatedItem);\n            return updatedItem;\n        } catch (error) {\n            // Revert optimistic update on error\n            if (options.optimisticUpdates !== false) {\n                updateState({\n                    items: originalItems\n                });\n            }\n            handleError(\"update\", error);\n            throw error;\n        }\n    }, [\n        options.apiEndpoint,\n        options.optimisticUpdates,\n        state.items,\n        makeRequest,\n        updateState,\n        handleError,\n        handleSuccess\n    ]);\n    const deleteItem = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (id)=>{\n        updateState({\n            isSubmitting: true,\n            error: null,\n            lastAction: \"delete\"\n        });\n        // Optimistic update\n        let originalItems = [];\n        if (options.optimisticUpdates !== false) {\n            originalItems = [\n                ...state.items\n            ];\n            updateState({\n                items: state.items.filter((item)=>item.id !== id)\n            });\n        }\n        try {\n            await makeRequest(`${options.apiEndpoint}/${id}`, {\n                method: \"DELETE\"\n            });\n            // Confirm deletion\n            if (options.optimisticUpdates === false) {\n                updateState({\n                    items: state.items.filter((item)=>item.id !== id),\n                    isSubmitting: false\n                });\n            } else {\n                updateState({\n                    isSubmitting: false\n                });\n            }\n            handleSuccess(\"delete\");\n        } catch (error) {\n            // Revert optimistic update on error\n            if (options.optimisticUpdates !== false) {\n                updateState({\n                    items: originalItems\n                });\n            }\n            handleError(\"delete\", error);\n            throw error;\n        }\n    }, [\n        options.apiEndpoint,\n        options.optimisticUpdates,\n        state.items,\n        makeRequest,\n        updateState,\n        handleError,\n        handleSuccess\n    ]);\n    // =====================================================\n    // BULK OPERATIONS\n    // =====================================================\n    const bulkUpdate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (ids, data)=>{\n        updateState({\n            isSubmitting: true,\n            error: null,\n            lastAction: \"bulkUpdate\"\n        });\n        try {\n            const response = await makeRequest(`${options.apiEndpoint}/bulk`, {\n                method: \"PUT\",\n                body: JSON.stringify({\n                    ids,\n                    data\n                })\n            });\n            const updatedItems = await response.json();\n            // Update items in state\n            updateState({\n                items: state.items.map((item)=>{\n                    const updated = updatedItems.find((u)=>u.id === item.id);\n                    return updated || item;\n                }),\n                isSubmitting: false,\n                selectedItems: new Set() // Clear selection after bulk operation\n            });\n            handleSuccess(\"bulkUpdate\");\n            return updatedItems;\n        } catch (error) {\n            handleError(\"bulkUpdate\", error);\n            throw error;\n        }\n    }, [\n        options.apiEndpoint,\n        state.items,\n        makeRequest,\n        updateState,\n        handleError,\n        handleSuccess\n    ]);\n    const bulkDelete = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (ids)=>{\n        updateState({\n            isSubmitting: true,\n            error: null,\n            lastAction: \"bulkDelete\"\n        });\n        try {\n            await makeRequest(`${options.apiEndpoint}/bulk`, {\n                method: \"DELETE\",\n                body: JSON.stringify({\n                    ids\n                })\n            });\n            // Remove deleted items\n            updateState({\n                items: state.items.filter((item)=>!ids.includes(item.id)),\n                isSubmitting: false,\n                selectedItems: new Set() // Clear selection after bulk operation\n            });\n            handleSuccess(\"bulkDelete\");\n        } catch (error) {\n            handleError(\"bulkDelete\", error);\n            throw error;\n        }\n    }, [\n        options.apiEndpoint,\n        state.items,\n        makeRequest,\n        updateState,\n        handleError,\n        handleSuccess\n    ]);\n    // =====================================================\n    // SELECTION MANAGEMENT\n    // =====================================================\n    const selectItem = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((id)=>{\n        const newSet = new Set(state.selectedItems);\n        newSet.add(id);\n        updateState({\n            selectedItems: newSet\n        });\n    }, [\n        state.selectedItems,\n        updateState\n    ]);\n    const selectAll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        updateState({\n            selectedItems: new Set(state.items.map((item)=>item.id))\n        });\n    }, [\n        state.items,\n        updateState\n    ]);\n    const clearSelection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        updateState({\n            selectedItems: new Set()\n        });\n    }, [\n        updateState\n    ]);\n    const toggleSelection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((id)=>{\n        const newSelection = new Set(state.selectedItems);\n        if (newSelection.has(id)) {\n            newSelection.delete(id);\n        } else {\n            newSelection.add(id);\n        }\n        updateState({\n            selectedItems: newSelection\n        });\n    }, [\n        state.selectedItems,\n        updateState\n    ]);\n    // =====================================================\n    // UTILITY ACTIONS\n    // =====================================================\n    const setItems = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((items)=>{\n        updateState({\n            items\n        });\n    }, [\n        updateState\n    ]);\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        updateState({\n            error: null\n        });\n    }, [\n        updateState\n    ]);\n    const refresh = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        return fetchItems();\n    }, [\n        fetchItems\n    ]);\n    // =====================================================\n    // RETURN STATE AND ACTIONS\n    // =====================================================\n    return [\n        state,\n        {\n            fetchItems,\n            createItem,\n            updateItem,\n            deleteItem,\n            bulkUpdate,\n            bulkDelete,\n            selectItem,\n            selectAll,\n            clearSelection,\n            toggleSelection,\n            setItems,\n            clearError,\n            refresh\n        }\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlQ3J1ZC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQSxvRkFBb0Y7NkRBRzlCO0FBaUQvQyxTQUFTRyxRQUNkQyxPQUF1QjtJQUd2Qix3REFBd0Q7SUFDeEQsbUJBQW1CO0lBQ25CLHdEQUF3RDtJQUV4RCxNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBR04sK0NBQVFBLENBQWU7UUFDL0NPLE9BQU9ILFFBQVFJLFdBQVcsSUFBSSxFQUFFO1FBQ2hDQyxTQUFTO1FBQ1RDLE9BQU87UUFDUEMsZUFBZSxJQUFJQztRQUNuQkMsY0FBYztRQUNkQyxZQUFZO0lBQ2Q7SUFFQSxNQUFNQyxxQkFBcUJiLDZDQUFNQSxDQUF5QjtJQUUxRCx3REFBd0Q7SUFDeEQsb0JBQW9CO0lBQ3BCLHdEQUF3RDtJQUV4RCxNQUFNYyxjQUFjZixrREFBV0EsQ0FBQyxDQUFDZ0I7UUFDL0JYLFNBQVNZLENBQUFBLE9BQVM7Z0JBQUUsR0FBR0EsSUFBSTtnQkFBRSxHQUFHRCxPQUFPO1lBQUM7SUFDMUMsR0FBRyxFQUFFO0lBRUwsTUFBTUUsY0FBY2xCLGtEQUFXQSxDQUFDLENBQUNtQixRQUFvQlY7UUFDbkRXLFFBQVFYLEtBQUssQ0FBQyxDQUFDLEtBQUssRUFBRVUsT0FBTyxPQUFPLENBQUMsRUFBRVY7UUFDdkNNLFlBQVk7WUFDVk4sT0FBT0EsTUFBTVksT0FBTztZQUNwQmIsU0FBUztZQUNUSSxjQUFjO1lBQ2RDLFlBQVlNO1FBQ2Q7UUFDQWhCLFFBQVFtQixPQUFPLEdBQUdILFFBQVFWO0lBQzVCLEdBQUc7UUFBQ047UUFBU1k7S0FBWTtJQUV6QixNQUFNUSxnQkFBZ0J2QixrREFBV0EsQ0FBQyxDQUFDbUIsUUFBb0JLO1FBQ3JEVCxZQUFZO1lBQ1ZOLE9BQU87WUFDUEQsU0FBUztZQUNUSSxjQUFjO1lBQ2RDLFlBQVlNO1FBQ2Q7UUFDQSxJQUFJSyxNQUFNO1lBQ1JyQixRQUFRc0IsU0FBUyxHQUFHTixRQUFRSztRQUM5QjtJQUNGLEdBQUc7UUFBQ3JCO1FBQVNZO0tBQVk7SUFFekIsTUFBTVcsY0FBYzFCLGtEQUFXQSxDQUFDLE9BQzlCMkIsS0FDQXhCLFVBQXVCLENBQUMsQ0FBQztRQUV6QiwyQ0FBMkM7UUFDM0MsSUFBSVcsbUJBQW1CYyxPQUFPLEVBQUU7WUFDOUJkLG1CQUFtQmMsT0FBTyxDQUFDQyxLQUFLO1FBQ2xDO1FBRUFmLG1CQUFtQmMsT0FBTyxHQUFHLElBQUlFO1FBRWpDLE1BQU1DLFdBQVcsTUFBTUMsTUFBTUwsS0FBSztZQUNoQyxHQUFHeEIsT0FBTztZQUNWOEIsUUFBUW5CLG1CQUFtQmMsT0FBTyxDQUFDSyxNQUFNO1lBQ3pDQyxTQUFTO2dCQUNQLGdCQUFnQjtnQkFDaEIsR0FBRy9CLFFBQVErQixPQUFPO1lBQ3BCO1FBQ0Y7UUFFQSxJQUFJLENBQUNILFNBQVNJLEVBQUUsRUFBRTtZQUNoQixNQUFNQyxZQUFZLE1BQU1MLFNBQVNNLElBQUksR0FBR0MsS0FBSyxDQUFDLElBQU8sRUFBQztZQUN0RCxNQUFNLElBQUlDLE1BQU1ILFVBQVVmLE9BQU8sSUFBSSxDQUFDLEtBQUssRUFBRVUsU0FBU1MsTUFBTSxDQUFDLEVBQUUsRUFBRVQsU0FBU1UsVUFBVSxDQUFDLENBQUM7UUFDeEY7UUFFQSxPQUFPVjtJQUNULEdBQUcsRUFBRTtJQUVMLHdEQUF3RDtJQUN4RCxrQkFBa0I7SUFDbEIsd0RBQXdEO0lBRXhELE1BQU1XLGFBQWExQyxrREFBV0EsQ0FBQyxPQUFPMkM7UUFDcEM1QixZQUFZO1lBQUVQLFNBQVM7WUFBTUMsT0FBTztZQUFNSSxZQUFZO1FBQU87UUFFN0QsSUFBSTtZQUNGLE1BQU1jLE1BQU0sSUFBSWlCLElBQUl6QyxRQUFRMEMsV0FBVztZQUN2QyxJQUFJRixRQUFRO2dCQUNWRyxPQUFPQyxPQUFPLENBQUNKLFFBQVFLLE9BQU8sQ0FBQyxDQUFDLENBQUNDLEtBQUtDLE1BQU07b0JBQzFDLElBQUlBLFVBQVVDLGFBQWFELFVBQVUsTUFBTTt3QkFDekN2QixJQUFJeUIsWUFBWSxDQUFDQyxNQUFNLENBQUNKLEtBQUtLLE9BQU9KO29CQUN0QztnQkFDRjtZQUNGO1lBRUEsTUFBTW5CLFdBQVcsTUFBTUwsWUFBWUMsSUFBSTRCLFFBQVE7WUFDL0MsTUFBTUMsT0FBTyxNQUFNekIsU0FBU00sSUFBSTtZQUNoQyxNQUFNL0IsUUFBUW1ELE1BQU1DLE9BQU8sQ0FBQ0YsUUFBUUEsT0FBT0EsS0FBS2xELEtBQUssSUFBSWtELEtBQUtBLElBQUksSUFBSSxFQUFFO1lBRXhFekMsWUFBWTtnQkFBRVQ7Z0JBQU9FLFNBQVM7Z0JBQU9LLFlBQVk7WUFBTztZQUN4RFUsY0FBYztZQUVkLE9BQU9qQjtRQUNULEVBQUUsT0FBT0csT0FBTztZQUNkLElBQUlBLGlCQUFpQjhCLFNBQVM5QixNQUFNa0QsSUFBSSxLQUFLLGNBQWM7Z0JBQ3pEekMsWUFBWSxRQUFRVDtZQUN0QjtZQUNBLE9BQU8sRUFBRTtRQUNYO0lBQ0YsR0FBRztRQUFDTixRQUFRMEMsV0FBVztRQUFFbkI7UUFBYVg7UUFBYUc7UUFBYUs7S0FBYztJQUU5RSxNQUFNcUMsYUFBYTVELGtEQUFXQSxDQUFDLE9BQU93RDtRQUNwQ3pDLFlBQVk7WUFBRUgsY0FBYztZQUFNSCxPQUFPO1lBQU1JLFlBQVk7UUFBUztRQUVwRSxJQUFJO1lBQ0YsTUFBTWtCLFdBQVcsTUFBTUwsWUFBWXZCLFFBQVEwQyxXQUFXLEVBQUU7Z0JBQ3REZ0IsUUFBUTtnQkFDUkMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDUjtZQUN2QjtZQUVBLE1BQU1TLFVBQVUsTUFBTWxDLFNBQVNNLElBQUk7WUFFbkMsb0JBQW9CO1lBQ3BCLElBQUlsQyxRQUFRK0QsaUJBQWlCLEtBQUssT0FBTztnQkFDdkNuRCxZQUFZO29CQUNWVCxPQUFPOzJCQUFJRixNQUFNRSxLQUFLO3dCQUFFMkQ7cUJBQVE7b0JBQ2hDckQsY0FBYztnQkFDaEI7WUFDRjtZQUVBVyxjQUFjLFVBQVUwQztZQUN4QixPQUFPQTtRQUNULEVBQUUsT0FBT3hELE9BQU87WUFDZFMsWUFBWSxVQUFVVDtZQUN0QixNQUFNQTtRQUNSO0lBQ0YsR0FBRztRQUFDTixRQUFRMEMsV0FBVztRQUFFMUMsUUFBUStELGlCQUFpQjtRQUFFOUQsTUFBTUUsS0FBSztRQUFFb0I7UUFBYVg7UUFBYUc7UUFBYUs7S0FBYztJQUV0SCxNQUFNNEMsYUFBYW5FLGtEQUFXQSxDQUFDLE9BQU9vRSxJQUFZWjtRQUNoRHpDLFlBQVk7WUFBRUgsY0FBYztZQUFNSCxPQUFPO1lBQU1JLFlBQVk7UUFBUztRQUVwRSxvQkFBb0I7UUFDcEIsSUFBSXdELGdCQUFxQixFQUFFO1FBQzNCLElBQUlsRSxRQUFRK0QsaUJBQWlCLEtBQUssT0FBTztZQUN2Q0csZ0JBQWdCO21CQUFJakUsTUFBTUUsS0FBSzthQUFDO1lBQ2hDLE1BQU1nRSxrQkFBa0JsRSxNQUFNRSxLQUFLLENBQUNpRSxHQUFHLENBQUMvQyxDQUFBQSxPQUN0Q0EsS0FBSzRDLEVBQUUsS0FBS0EsS0FBSztvQkFBRSxHQUFHNUMsSUFBSTtvQkFBRSxHQUFHZ0MsSUFBSTtnQkFBQyxJQUFJaEM7WUFFMUNULFlBQVk7Z0JBQUVULE9BQU9nRTtZQUFnQjtRQUN2QztRQUVBLElBQUk7WUFDRixNQUFNdkMsV0FBVyxNQUFNTCxZQUFZLENBQUMsRUFBRXZCLFFBQVEwQyxXQUFXLENBQUMsQ0FBQyxFQUFFdUIsR0FBRyxDQUFDLEVBQUU7Z0JBQ2pFUCxRQUFRO2dCQUNSQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNSO1lBQ3ZCO1lBRUEsTUFBTWdCLGNBQWMsTUFBTXpDLFNBQVNNLElBQUk7WUFFdkMsOEJBQThCO1lBQzlCdEIsWUFBWTtnQkFDVlQsT0FBT0YsTUFBTUUsS0FBSyxDQUFDaUUsR0FBRyxDQUFDL0MsQ0FBQUEsT0FDckJBLEtBQUs0QyxFQUFFLEtBQUtBLEtBQUtJLGNBQWNoRDtnQkFFakNaLGNBQWM7WUFDaEI7WUFFQVcsY0FBYyxVQUFVaUQ7WUFDeEIsT0FBT0E7UUFDVCxFQUFFLE9BQU8vRCxPQUFPO1lBQ2Qsb0NBQW9DO1lBQ3BDLElBQUlOLFFBQVErRCxpQkFBaUIsS0FBSyxPQUFPO2dCQUN2Q25ELFlBQVk7b0JBQUVULE9BQU8rRDtnQkFBYztZQUNyQztZQUNBbkQsWUFBWSxVQUFVVDtZQUN0QixNQUFNQTtRQUNSO0lBQ0YsR0FBRztRQUFDTixRQUFRMEMsV0FBVztRQUFFMUMsUUFBUStELGlCQUFpQjtRQUFFOUQsTUFBTUUsS0FBSztRQUFFb0I7UUFBYVg7UUFBYUc7UUFBYUs7S0FBYztJQUV0SCxNQUFNa0QsYUFBYXpFLGtEQUFXQSxDQUFDLE9BQU9vRTtRQUNwQ3JELFlBQVk7WUFBRUgsY0FBYztZQUFNSCxPQUFPO1lBQU1JLFlBQVk7UUFBUztRQUVwRSxvQkFBb0I7UUFDcEIsSUFBSXdELGdCQUFxQixFQUFFO1FBQzNCLElBQUlsRSxRQUFRK0QsaUJBQWlCLEtBQUssT0FBTztZQUN2Q0csZ0JBQWdCO21CQUFJakUsTUFBTUUsS0FBSzthQUFDO1lBQ2hDUyxZQUFZO2dCQUFFVCxPQUFPRixNQUFNRSxLQUFLLENBQUNvRSxNQUFNLENBQUNsRCxDQUFBQSxPQUFRQSxLQUFLNEMsRUFBRSxLQUFLQTtZQUFJO1FBQ2xFO1FBRUEsSUFBSTtZQUNGLE1BQU0xQyxZQUFZLENBQUMsRUFBRXZCLFFBQVEwQyxXQUFXLENBQUMsQ0FBQyxFQUFFdUIsR0FBRyxDQUFDLEVBQUU7Z0JBQ2hEUCxRQUFRO1lBQ1Y7WUFFQSxtQkFBbUI7WUFDbkIsSUFBSTFELFFBQVErRCxpQkFBaUIsS0FBSyxPQUFPO2dCQUN2Q25ELFlBQVk7b0JBQ1ZULE9BQU9GLE1BQU1FLEtBQUssQ0FBQ29FLE1BQU0sQ0FBQ2xELENBQUFBLE9BQVFBLEtBQUs0QyxFQUFFLEtBQUtBO29CQUM5Q3hELGNBQWM7Z0JBQ2hCO1lBQ0YsT0FBTztnQkFDTEcsWUFBWTtvQkFBRUgsY0FBYztnQkFBTTtZQUNwQztZQUVBVyxjQUFjO1FBQ2hCLEVBQUUsT0FBT2QsT0FBTztZQUNkLG9DQUFvQztZQUNwQyxJQUFJTixRQUFRK0QsaUJBQWlCLEtBQUssT0FBTztnQkFDdkNuRCxZQUFZO29CQUFFVCxPQUFPK0Q7Z0JBQWM7WUFDckM7WUFDQW5ELFlBQVksVUFBVVQ7WUFDdEIsTUFBTUE7UUFDUjtJQUNGLEdBQUc7UUFBQ04sUUFBUTBDLFdBQVc7UUFBRTFDLFFBQVErRCxpQkFBaUI7UUFBRTlELE1BQU1FLEtBQUs7UUFBRW9CO1FBQWFYO1FBQWFHO1FBQWFLO0tBQWM7SUFFdEgsd0RBQXdEO0lBQ3hELGtCQUFrQjtJQUNsQix3REFBd0Q7SUFFeEQsTUFBTW9ELGFBQWEzRSxrREFBV0EsQ0FBQyxPQUFPNEUsS0FBZXBCO1FBQ25EekMsWUFBWTtZQUFFSCxjQUFjO1lBQU1ILE9BQU87WUFBTUksWUFBWTtRQUFhO1FBRXhFLElBQUk7WUFDRixNQUFNa0IsV0FBVyxNQUFNTCxZQUFZLENBQUMsRUFBRXZCLFFBQVEwQyxXQUFXLENBQUMsS0FBSyxDQUFDLEVBQUU7Z0JBQ2hFZ0IsUUFBUTtnQkFDUkMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUFFWTtvQkFBS3BCO2dCQUFLO1lBQ25DO1lBRUEsTUFBTXFCLGVBQWUsTUFBTTlDLFNBQVNNLElBQUk7WUFFeEMsd0JBQXdCO1lBQ3hCdEIsWUFBWTtnQkFDVlQsT0FBT0YsTUFBTUUsS0FBSyxDQUFDaUUsR0FBRyxDQUFDL0MsQ0FBQUE7b0JBQ3JCLE1BQU1zRCxVQUFVRCxhQUFhRSxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVaLEVBQUUsS0FBSzVDLEtBQUs0QyxFQUFFO29CQUN2RCxPQUFPVSxXQUFXdEQ7Z0JBQ3BCO2dCQUNBWixjQUFjO2dCQUNkRixlQUFlLElBQUlDLE1BQU0sdUNBQXVDO1lBQ2xFO1lBRUFZLGNBQWM7WUFDZCxPQUFPc0Q7UUFDVCxFQUFFLE9BQU9wRSxPQUFPO1lBQ2RTLFlBQVksY0FBY1Q7WUFDMUIsTUFBTUE7UUFDUjtJQUNGLEdBQUc7UUFBQ04sUUFBUTBDLFdBQVc7UUFBRXpDLE1BQU1FLEtBQUs7UUFBRW9CO1FBQWFYO1FBQWFHO1FBQWFLO0tBQWM7SUFFM0YsTUFBTTBELGFBQWFqRixrREFBV0EsQ0FBQyxPQUFPNEU7UUFDcEM3RCxZQUFZO1lBQUVILGNBQWM7WUFBTUgsT0FBTztZQUFNSSxZQUFZO1FBQWE7UUFFeEUsSUFBSTtZQUNGLE1BQU1hLFlBQVksQ0FBQyxFQUFFdkIsUUFBUTBDLFdBQVcsQ0FBQyxLQUFLLENBQUMsRUFBRTtnQkFDL0NnQixRQUFRO2dCQUNSQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQUVZO2dCQUFJO1lBQzdCO1lBRUEsdUJBQXVCO1lBQ3ZCN0QsWUFBWTtnQkFDVlQsT0FBT0YsTUFBTUUsS0FBSyxDQUFDb0UsTUFBTSxDQUFDbEQsQ0FBQUEsT0FBUSxDQUFDb0QsSUFBSU0sUUFBUSxDQUFDMUQsS0FBSzRDLEVBQUU7Z0JBQ3ZEeEQsY0FBYztnQkFDZEYsZUFBZSxJQUFJQyxNQUFNLHVDQUF1QztZQUNsRTtZQUVBWSxjQUFjO1FBQ2hCLEVBQUUsT0FBT2QsT0FBTztZQUNkUyxZQUFZLGNBQWNUO1lBQzFCLE1BQU1BO1FBQ1I7SUFDRixHQUFHO1FBQUNOLFFBQVEwQyxXQUFXO1FBQUV6QyxNQUFNRSxLQUFLO1FBQUVvQjtRQUFhWDtRQUFhRztRQUFhSztLQUFjO0lBRTNGLHdEQUF3RDtJQUN4RCx1QkFBdUI7SUFDdkIsd0RBQXdEO0lBRXhELE1BQU00RCxhQUFhbkYsa0RBQVdBLENBQUMsQ0FBQ29FO1FBQzlCLE1BQU1nQixTQUFTLElBQUl6RSxJQUFJUCxNQUFNTSxhQUFhO1FBQzFDMEUsT0FBT0MsR0FBRyxDQUFDakI7UUFDWHJELFlBQVk7WUFDVkwsZUFBZTBFO1FBQ2pCO0lBQ0YsR0FBRztRQUFDaEYsTUFBTU0sYUFBYTtRQUFFSztLQUFZO0lBRXJDLE1BQU11RSxZQUFZdEYsa0RBQVdBLENBQUM7UUFDNUJlLFlBQVk7WUFDVkwsZUFBZSxJQUFJQyxJQUFJUCxNQUFNRSxLQUFLLENBQUNpRSxHQUFHLENBQUMvQyxDQUFBQSxPQUFRQSxLQUFLNEMsRUFBRTtRQUN4RDtJQUNGLEdBQUc7UUFBQ2hFLE1BQU1FLEtBQUs7UUFBRVM7S0FBWTtJQUU3QixNQUFNd0UsaUJBQWlCdkYsa0RBQVdBLENBQUM7UUFDakNlLFlBQVk7WUFBRUwsZUFBZSxJQUFJQztRQUFNO0lBQ3pDLEdBQUc7UUFBQ0k7S0FBWTtJQUVoQixNQUFNeUUsa0JBQWtCeEYsa0RBQVdBLENBQUMsQ0FBQ29FO1FBQ25DLE1BQU1xQixlQUFlLElBQUk5RSxJQUFJUCxNQUFNTSxhQUFhO1FBQ2hELElBQUkrRSxhQUFhQyxHQUFHLENBQUN0QixLQUFLO1lBQ3hCcUIsYUFBYUUsTUFBTSxDQUFDdkI7UUFDdEIsT0FBTztZQUNMcUIsYUFBYUosR0FBRyxDQUFDakI7UUFDbkI7UUFDQXJELFlBQVk7WUFBRUwsZUFBZStFO1FBQWE7SUFDNUMsR0FBRztRQUFDckYsTUFBTU0sYUFBYTtRQUFFSztLQUFZO0lBRXJDLHdEQUF3RDtJQUN4RCxrQkFBa0I7SUFDbEIsd0RBQXdEO0lBRXhELE1BQU02RSxXQUFXNUYsa0RBQVdBLENBQUMsQ0FBQ007UUFDNUJTLFlBQVk7WUFBRVQ7UUFBTTtJQUN0QixHQUFHO1FBQUNTO0tBQVk7SUFFaEIsTUFBTThFLGFBQWE3RixrREFBV0EsQ0FBQztRQUM3QmUsWUFBWTtZQUFFTixPQUFPO1FBQUs7SUFDNUIsR0FBRztRQUFDTTtLQUFZO0lBRWhCLE1BQU0rRSxVQUFVOUYsa0RBQVdBLENBQUM7UUFDMUIsT0FBTzBDO0lBQ1QsR0FBRztRQUFDQTtLQUFXO0lBRWYsd0RBQXdEO0lBQ3hELDJCQUEyQjtJQUMzQix3REFBd0Q7SUFFeEQsT0FBTztRQUNMdEM7UUFDQTtZQUNFc0M7WUFDQWtCO1lBQ0FPO1lBQ0FNO1lBQ0FFO1lBQ0FNO1lBQ0FFO1lBQ0FHO1lBQ0FDO1lBQ0FDO1lBQ0FJO1lBQ0FDO1lBQ0FDO1FBQ0Y7S0FDRDtBQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmhlZW1kaW5lLy4vc3JjL2hvb2tzL3VzZUNydWQudHM/YTMzNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBSZXVzYWJsZSBDUlVEIGhvb2sgZm9yIGRhdGEgb3BlcmF0aW9ucyB3aXRoIG9wdGltaXN0aWMgdXBkYXRlcyBhbmQgZXJyb3IgaGFuZGxpbmdcbid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUNhbGxiYWNrLCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ3J1ZEl0ZW0ge1xuICBpZDogc3RyaW5nO1xuICBba2V5OiBzdHJpbmddOiBhbnk7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ3J1ZE9wdGlvbnM8VCBleHRlbmRzIENydWRJdGVtPiB7XG4gIGFwaUVuZHBvaW50OiBzdHJpbmc7XG4gIG9uU3VjY2Vzcz86IChhY3Rpb246IENydWRBY3Rpb24sIGl0ZW06IFQpID0+IHZvaWQ7XG4gIG9uRXJyb3I/OiAoYWN0aW9uOiBDcnVkQWN0aW9uLCBlcnJvcjogRXJyb3IpID0+IHZvaWQ7XG4gIG9wdGltaXN0aWNVcGRhdGVzPzogYm9vbGVhbjtcbiAgaW5pdGlhbERhdGE/OiBUW107XG59XG5cbmV4cG9ydCB0eXBlIENydWRBY3Rpb24gPSAnY3JlYXRlJyB8ICdyZWFkJyB8ICd1cGRhdGUnIHwgJ2RlbGV0ZScgfCAnYnVsa1VwZGF0ZScgfCAnYnVsa0RlbGV0ZSc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ3J1ZFN0YXRlPFQgZXh0ZW5kcyBDcnVkSXRlbT4ge1xuICBpdGVtczogVFtdO1xuICBsb2FkaW5nOiBib29sZWFuO1xuICBlcnJvcjogc3RyaW5nIHwgbnVsbDtcbiAgc2VsZWN0ZWRJdGVtczogU2V0PHN0cmluZz47XG4gIGlzU3VibWl0dGluZzogYm9vbGVhbjtcbiAgbGFzdEFjdGlvbjogQ3J1ZEFjdGlvbiB8IG51bGw7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ3J1ZEFjdGlvbnM8VCBleHRlbmRzIENydWRJdGVtPiB7XG4gIC8vIEJhc2ljIENSVUQgb3BlcmF0aW9uc1xuICBmZXRjaEl0ZW1zOiAocGFyYW1zPzogUmVjb3JkPHN0cmluZywgYW55PikgPT4gUHJvbWlzZTxUW10+O1xuICBjcmVhdGVJdGVtOiAoZGF0YTogT21pdDxULCAnaWQnPikgPT4gUHJvbWlzZTxUPjtcbiAgdXBkYXRlSXRlbTogKGlkOiBzdHJpbmcsIGRhdGE6IFBhcnRpYWw8VD4pID0+IFByb21pc2U8VD47XG4gIGRlbGV0ZUl0ZW06IChpZDogc3RyaW5nKSA9PiBQcm9taXNlPHZvaWQ+O1xuICBcbiAgLy8gQnVsayBvcGVyYXRpb25zXG4gIGJ1bGtVcGRhdGU6IChpZHM6IHN0cmluZ1tdLCBkYXRhOiBQYXJ0aWFsPFQ+KSA9PiBQcm9taXNlPFRbXT47XG4gIGJ1bGtEZWxldGU6IChpZHM6IHN0cmluZ1tdKSA9PiBQcm9taXNlPHZvaWQ+O1xuICBcbiAgLy8gU2VsZWN0aW9uIG1hbmFnZW1lbnRcbiAgc2VsZWN0SXRlbTogKGlkOiBzdHJpbmcpID0+IHZvaWQ7XG4gIHNlbGVjdEFsbDogKCkgPT4gdm9pZDtcbiAgY2xlYXJTZWxlY3Rpb246ICgpID0+IHZvaWQ7XG4gIHRvZ2dsZVNlbGVjdGlvbjogKGlkOiBzdHJpbmcpID0+IHZvaWQ7XG4gIFxuICAvLyBTdGF0ZSBtYW5hZ2VtZW50XG4gIHNldEl0ZW1zOiAoaXRlbXM6IFRbXSkgPT4gdm9pZDtcbiAgY2xlYXJFcnJvcjogKCkgPT4gdm9pZDtcbiAgcmVmcmVzaDogKCkgPT4gUHJvbWlzZTxUW10+O1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQ3J1ZDxUIGV4dGVuZHMgQ3J1ZEl0ZW0+KFxuICBvcHRpb25zOiBDcnVkT3B0aW9uczxUPlxuKTogW0NydWRTdGF0ZTxUPiwgQ3J1ZEFjdGlvbnM8VD5dIHtcbiAgXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIC8vIFNUQVRFIE1BTkFHRU1FTlRcbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgXG4gIGNvbnN0IFtzdGF0ZSwgc2V0U3RhdGVdID0gdXNlU3RhdGU8Q3J1ZFN0YXRlPFQ+Pih7XG4gICAgaXRlbXM6IG9wdGlvbnMuaW5pdGlhbERhdGEgfHwgW10sXG4gICAgbG9hZGluZzogZmFsc2UsXG4gICAgZXJyb3I6IG51bGwsXG4gICAgc2VsZWN0ZWRJdGVtczogbmV3IFNldCgpLFxuICAgIGlzU3VibWl0dGluZzogZmFsc2UsXG4gICAgbGFzdEFjdGlvbjogbnVsbCxcbiAgfSk7XG4gIFxuICBjb25zdCBhYm9ydENvbnRyb2xsZXJSZWYgPSB1c2VSZWY8QWJvcnRDb250cm9sbGVyIHwgbnVsbD4obnVsbCk7XG4gIFxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICAvLyBVVElMSVRZIEZVTkNUSU9OU1xuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICBcbiAgY29uc3QgdXBkYXRlU3RhdGUgPSB1c2VDYWxsYmFjaygodXBkYXRlczogUGFydGlhbDxDcnVkU3RhdGU8VD4+KSA9PiB7XG4gICAgc2V0U3RhdGUocHJldiA9PiAoeyAuLi5wcmV2LCAuLi51cGRhdGVzIH0pKTtcbiAgfSwgW10pO1xuICBcbiAgY29uc3QgaGFuZGxlRXJyb3IgPSB1c2VDYWxsYmFjaygoYWN0aW9uOiBDcnVkQWN0aW9uLCBlcnJvcjogRXJyb3IpID0+IHtcbiAgICBjb25zb2xlLmVycm9yKGBDUlVEICR7YWN0aW9ufSBlcnJvcjpgLCBlcnJvcik7XG4gICAgdXBkYXRlU3RhdGUoeyBcbiAgICAgIGVycm9yOiBlcnJvci5tZXNzYWdlLCBcbiAgICAgIGxvYWRpbmc6IGZhbHNlLCBcbiAgICAgIGlzU3VibWl0dGluZzogZmFsc2UsXG4gICAgICBsYXN0QWN0aW9uOiBhY3Rpb24gXG4gICAgfSk7XG4gICAgb3B0aW9ucy5vbkVycm9yPy4oYWN0aW9uLCBlcnJvcik7XG4gIH0sIFtvcHRpb25zLCB1cGRhdGVTdGF0ZV0pO1xuICBcbiAgY29uc3QgaGFuZGxlU3VjY2VzcyA9IHVzZUNhbGxiYWNrKChhY3Rpb246IENydWRBY3Rpb24sIGl0ZW0/OiBUKSA9PiB7XG4gICAgdXBkYXRlU3RhdGUoeyBcbiAgICAgIGVycm9yOiBudWxsLCBcbiAgICAgIGxvYWRpbmc6IGZhbHNlLCBcbiAgICAgIGlzU3VibWl0dGluZzogZmFsc2UsXG4gICAgICBsYXN0QWN0aW9uOiBhY3Rpb24gXG4gICAgfSk7XG4gICAgaWYgKGl0ZW0pIHtcbiAgICAgIG9wdGlvbnMub25TdWNjZXNzPy4oYWN0aW9uLCBpdGVtKTtcbiAgICB9XG4gIH0sIFtvcHRpb25zLCB1cGRhdGVTdGF0ZV0pO1xuICBcbiAgY29uc3QgbWFrZVJlcXVlc3QgPSB1c2VDYWxsYmFjayhhc3luYyAoXG4gICAgdXJsOiBzdHJpbmcsXG4gICAgb3B0aW9uczogUmVxdWVzdEluaXQgPSB7fVxuICApOiBQcm9taXNlPFJlc3BvbnNlPiA9PiB7XG4gICAgLy8gQ2FuY2VsIHByZXZpb3VzIHJlcXVlc3QgaWYgc3RpbGwgcGVuZGluZ1xuICAgIGlmIChhYm9ydENvbnRyb2xsZXJSZWYuY3VycmVudCkge1xuICAgICAgYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQuYWJvcnQoKTtcbiAgICB9XG4gICAgXG4gICAgYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG4gICAgXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCh1cmwsIHtcbiAgICAgIC4uLm9wdGlvbnMsXG4gICAgICBzaWduYWw6IGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50LnNpZ25hbCxcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgLi4ub3B0aW9ucy5oZWFkZXJzLFxuICAgICAgfSxcbiAgICB9KTtcbiAgICBcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCkuY2F0Y2goKCkgPT4gKHt9KSk7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLm1lc3NhZ2UgfHwgYEhUVFAgJHtyZXNwb25zZS5zdGF0dXN9OiAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7XG4gICAgfVxuICAgIFxuICAgIHJldHVybiByZXNwb25zZTtcbiAgfSwgW10pO1xuICBcbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgLy8gQ1JVRCBPUEVSQVRJT05TXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIFxuICBjb25zdCBmZXRjaEl0ZW1zID0gdXNlQ2FsbGJhY2soYXN5bmMgKHBhcmFtcz86IFJlY29yZDxzdHJpbmcsIGFueT4pOiBQcm9taXNlPFRbXT4gPT4ge1xuICAgIHVwZGF0ZVN0YXRlKHsgbG9hZGluZzogdHJ1ZSwgZXJyb3I6IG51bGwsIGxhc3RBY3Rpb246ICdyZWFkJyB9KTtcbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3QgdXJsID0gbmV3IFVSTChvcHRpb25zLmFwaUVuZHBvaW50KTtcbiAgICAgIGlmIChwYXJhbXMpIHtcbiAgICAgICAgT2JqZWN0LmVudHJpZXMocGFyYW1zKS5mb3JFYWNoKChba2V5LCB2YWx1ZV0pID0+IHtcbiAgICAgICAgICBpZiAodmFsdWUgIT09IHVuZGVmaW5lZCAmJiB2YWx1ZSAhPT0gbnVsbCkge1xuICAgICAgICAgICAgdXJsLnNlYXJjaFBhcmFtcy5hcHBlbmQoa2V5LCBTdHJpbmcodmFsdWUpKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgICAgXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IG1ha2VSZXF1ZXN0KHVybC50b1N0cmluZygpKTtcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBjb25zdCBpdGVtcyA9IEFycmF5LmlzQXJyYXkoZGF0YSkgPyBkYXRhIDogZGF0YS5pdGVtcyB8fCBkYXRhLmRhdGEgfHwgW107XG4gICAgICBcbiAgICAgIHVwZGF0ZVN0YXRlKHsgaXRlbXMsIGxvYWRpbmc6IGZhbHNlLCBsYXN0QWN0aW9uOiAncmVhZCcgfSk7XG4gICAgICBoYW5kbGVTdWNjZXNzKCdyZWFkJyk7XG4gICAgICBcbiAgICAgIHJldHVybiBpdGVtcztcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IgJiYgZXJyb3IubmFtZSAhPT0gJ0Fib3J0RXJyb3InKSB7XG4gICAgICAgIGhhbmRsZUVycm9yKCdyZWFkJywgZXJyb3IpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIFtdO1xuICAgIH1cbiAgfSwgW29wdGlvbnMuYXBpRW5kcG9pbnQsIG1ha2VSZXF1ZXN0LCB1cGRhdGVTdGF0ZSwgaGFuZGxlRXJyb3IsIGhhbmRsZVN1Y2Nlc3NdKTtcbiAgXG4gIGNvbnN0IGNyZWF0ZUl0ZW0gPSB1c2VDYWxsYmFjayhhc3luYyAoZGF0YTogT21pdDxULCAnaWQnPik6IFByb21pc2U8VD4gPT4ge1xuICAgIHVwZGF0ZVN0YXRlKHsgaXNTdWJtaXR0aW5nOiB0cnVlLCBlcnJvcjogbnVsbCwgbGFzdEFjdGlvbjogJ2NyZWF0ZScgfSk7XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbWFrZVJlcXVlc3Qob3B0aW9ucy5hcGlFbmRwb2ludCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoZGF0YSksXG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgY29uc3QgbmV3SXRlbSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKSBhcyBUO1xuICAgICAgXG4gICAgICAvLyBPcHRpbWlzdGljIHVwZGF0ZVxuICAgICAgaWYgKG9wdGlvbnMub3B0aW1pc3RpY1VwZGF0ZXMgIT09IGZhbHNlKSB7XG4gICAgICAgIHVwZGF0ZVN0YXRlKHsgXG4gICAgICAgICAgaXRlbXM6IFsuLi5zdGF0ZS5pdGVtcywgbmV3SXRlbV0sXG4gICAgICAgICAgaXNTdWJtaXR0aW5nOiBmYWxzZSBcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIGhhbmRsZVN1Y2Nlc3MoJ2NyZWF0ZScsIG5ld0l0ZW0pO1xuICAgICAgcmV0dXJuIG5ld0l0ZW07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGhhbmRsZUVycm9yKCdjcmVhdGUnLCBlcnJvciBhcyBFcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH0sIFtvcHRpb25zLmFwaUVuZHBvaW50LCBvcHRpb25zLm9wdGltaXN0aWNVcGRhdGVzLCBzdGF0ZS5pdGVtcywgbWFrZVJlcXVlc3QsIHVwZGF0ZVN0YXRlLCBoYW5kbGVFcnJvciwgaGFuZGxlU3VjY2Vzc10pO1xuICBcbiAgY29uc3QgdXBkYXRlSXRlbSA9IHVzZUNhbGxiYWNrKGFzeW5jIChpZDogc3RyaW5nLCBkYXRhOiBQYXJ0aWFsPFQ+KTogUHJvbWlzZTxUPiA9PiB7XG4gICAgdXBkYXRlU3RhdGUoeyBpc1N1Ym1pdHRpbmc6IHRydWUsIGVycm9yOiBudWxsLCBsYXN0QWN0aW9uOiAndXBkYXRlJyB9KTtcbiAgICBcbiAgICAvLyBPcHRpbWlzdGljIHVwZGF0ZVxuICAgIGxldCBvcmlnaW5hbEl0ZW1zOiBUW10gPSBbXTtcbiAgICBpZiAob3B0aW9ucy5vcHRpbWlzdGljVXBkYXRlcyAhPT0gZmFsc2UpIHtcbiAgICAgIG9yaWdpbmFsSXRlbXMgPSBbLi4uc3RhdGUuaXRlbXNdO1xuICAgICAgY29uc3Qgb3B0aW1pc3RpY0l0ZW1zID0gc3RhdGUuaXRlbXMubWFwKGl0ZW0gPT4gXG4gICAgICAgIGl0ZW0uaWQgPT09IGlkID8geyAuLi5pdGVtLCAuLi5kYXRhIH0gOiBpdGVtXG4gICAgICApO1xuICAgICAgdXBkYXRlU3RhdGUoeyBpdGVtczogb3B0aW1pc3RpY0l0ZW1zIH0pO1xuICAgIH1cbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBtYWtlUmVxdWVzdChgJHtvcHRpb25zLmFwaUVuZHBvaW50fS8ke2lkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoZGF0YSksXG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgY29uc3QgdXBkYXRlZEl0ZW0gPSBhd2FpdCByZXNwb25zZS5qc29uKCkgYXMgVDtcbiAgICAgIFxuICAgICAgLy8gVXBkYXRlIHdpdGggc2VydmVyIHJlc3BvbnNlXG4gICAgICB1cGRhdGVTdGF0ZSh7IFxuICAgICAgICBpdGVtczogc3RhdGUuaXRlbXMubWFwKGl0ZW0gPT4gXG4gICAgICAgICAgaXRlbS5pZCA9PT0gaWQgPyB1cGRhdGVkSXRlbSA6IGl0ZW1cbiAgICAgICAgKSxcbiAgICAgICAgaXNTdWJtaXR0aW5nOiBmYWxzZSBcbiAgICAgIH0pO1xuICAgICAgXG4gICAgICBoYW5kbGVTdWNjZXNzKCd1cGRhdGUnLCB1cGRhdGVkSXRlbSk7XG4gICAgICByZXR1cm4gdXBkYXRlZEl0ZW07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIC8vIFJldmVydCBvcHRpbWlzdGljIHVwZGF0ZSBvbiBlcnJvclxuICAgICAgaWYgKG9wdGlvbnMub3B0aW1pc3RpY1VwZGF0ZXMgIT09IGZhbHNlKSB7XG4gICAgICAgIHVwZGF0ZVN0YXRlKHsgaXRlbXM6IG9yaWdpbmFsSXRlbXMgfSk7XG4gICAgICB9XG4gICAgICBoYW5kbGVFcnJvcigndXBkYXRlJywgZXJyb3IgYXMgRXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9LCBbb3B0aW9ucy5hcGlFbmRwb2ludCwgb3B0aW9ucy5vcHRpbWlzdGljVXBkYXRlcywgc3RhdGUuaXRlbXMsIG1ha2VSZXF1ZXN0LCB1cGRhdGVTdGF0ZSwgaGFuZGxlRXJyb3IsIGhhbmRsZVN1Y2Nlc3NdKTtcbiAgXG4gIGNvbnN0IGRlbGV0ZUl0ZW0gPSB1c2VDYWxsYmFjayhhc3luYyAoaWQ6IHN0cmluZyk6IFByb21pc2U8dm9pZD4gPT4ge1xuICAgIHVwZGF0ZVN0YXRlKHsgaXNTdWJtaXR0aW5nOiB0cnVlLCBlcnJvcjogbnVsbCwgbGFzdEFjdGlvbjogJ2RlbGV0ZScgfSk7XG4gICAgXG4gICAgLy8gT3B0aW1pc3RpYyB1cGRhdGVcbiAgICBsZXQgb3JpZ2luYWxJdGVtczogVFtdID0gW107XG4gICAgaWYgKG9wdGlvbnMub3B0aW1pc3RpY1VwZGF0ZXMgIT09IGZhbHNlKSB7XG4gICAgICBvcmlnaW5hbEl0ZW1zID0gWy4uLnN0YXRlLml0ZW1zXTtcbiAgICAgIHVwZGF0ZVN0YXRlKHsgaXRlbXM6IHN0YXRlLml0ZW1zLmZpbHRlcihpdGVtID0+IGl0ZW0uaWQgIT09IGlkKSB9KTtcbiAgICB9XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IG1ha2VSZXF1ZXN0KGAke29wdGlvbnMuYXBpRW5kcG9pbnR9LyR7aWR9YCwge1xuICAgICAgICBtZXRob2Q6ICdERUxFVEUnLFxuICAgICAgfSk7XG4gICAgICBcbiAgICAgIC8vIENvbmZpcm0gZGVsZXRpb25cbiAgICAgIGlmIChvcHRpb25zLm9wdGltaXN0aWNVcGRhdGVzID09PSBmYWxzZSkge1xuICAgICAgICB1cGRhdGVTdGF0ZSh7IFxuICAgICAgICAgIGl0ZW1zOiBzdGF0ZS5pdGVtcy5maWx0ZXIoaXRlbSA9PiBpdGVtLmlkICE9PSBpZCksXG4gICAgICAgICAgaXNTdWJtaXR0aW5nOiBmYWxzZSBcbiAgICAgICAgfSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB1cGRhdGVTdGF0ZSh7IGlzU3VibWl0dGluZzogZmFsc2UgfSk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIGhhbmRsZVN1Y2Nlc3MoJ2RlbGV0ZScpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAvLyBSZXZlcnQgb3B0aW1pc3RpYyB1cGRhdGUgb24gZXJyb3JcbiAgICAgIGlmIChvcHRpb25zLm9wdGltaXN0aWNVcGRhdGVzICE9PSBmYWxzZSkge1xuICAgICAgICB1cGRhdGVTdGF0ZSh7IGl0ZW1zOiBvcmlnaW5hbEl0ZW1zIH0pO1xuICAgICAgfVxuICAgICAgaGFuZGxlRXJyb3IoJ2RlbGV0ZScsIGVycm9yIGFzIEVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfSwgW29wdGlvbnMuYXBpRW5kcG9pbnQsIG9wdGlvbnMub3B0aW1pc3RpY1VwZGF0ZXMsIHN0YXRlLml0ZW1zLCBtYWtlUmVxdWVzdCwgdXBkYXRlU3RhdGUsIGhhbmRsZUVycm9yLCBoYW5kbGVTdWNjZXNzXSk7XG4gIFxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICAvLyBCVUxLIE9QRVJBVElPTlNcbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgXG4gIGNvbnN0IGJ1bGtVcGRhdGUgPSB1c2VDYWxsYmFjayhhc3luYyAoaWRzOiBzdHJpbmdbXSwgZGF0YTogUGFydGlhbDxUPik6IFByb21pc2U8VFtdPiA9PiB7XG4gICAgdXBkYXRlU3RhdGUoeyBpc1N1Ym1pdHRpbmc6IHRydWUsIGVycm9yOiBudWxsLCBsYXN0QWN0aW9uOiAnYnVsa1VwZGF0ZScgfSk7XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbWFrZVJlcXVlc3QoYCR7b3B0aW9ucy5hcGlFbmRwb2ludH0vYnVsa2AsIHtcbiAgICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBpZHMsIGRhdGEgfSksXG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgY29uc3QgdXBkYXRlZEl0ZW1zID0gYXdhaXQgcmVzcG9uc2UuanNvbigpIGFzIFRbXTtcbiAgICAgIFxuICAgICAgLy8gVXBkYXRlIGl0ZW1zIGluIHN0YXRlXG4gICAgICB1cGRhdGVTdGF0ZSh7IFxuICAgICAgICBpdGVtczogc3RhdGUuaXRlbXMubWFwKGl0ZW0gPT4ge1xuICAgICAgICAgIGNvbnN0IHVwZGF0ZWQgPSB1cGRhdGVkSXRlbXMuZmluZCh1ID0+IHUuaWQgPT09IGl0ZW0uaWQpO1xuICAgICAgICAgIHJldHVybiB1cGRhdGVkIHx8IGl0ZW07XG4gICAgICAgIH0pLFxuICAgICAgICBpc1N1Ym1pdHRpbmc6IGZhbHNlLFxuICAgICAgICBzZWxlY3RlZEl0ZW1zOiBuZXcgU2V0KCkgLy8gQ2xlYXIgc2VsZWN0aW9uIGFmdGVyIGJ1bGsgb3BlcmF0aW9uXG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgaGFuZGxlU3VjY2VzcygnYnVsa1VwZGF0ZScpO1xuICAgICAgcmV0dXJuIHVwZGF0ZWRJdGVtcztcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgaGFuZGxlRXJyb3IoJ2J1bGtVcGRhdGUnLCBlcnJvciBhcyBFcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH0sIFtvcHRpb25zLmFwaUVuZHBvaW50LCBzdGF0ZS5pdGVtcywgbWFrZVJlcXVlc3QsIHVwZGF0ZVN0YXRlLCBoYW5kbGVFcnJvciwgaGFuZGxlU3VjY2Vzc10pO1xuICBcbiAgY29uc3QgYnVsa0RlbGV0ZSA9IHVzZUNhbGxiYWNrKGFzeW5jIChpZHM6IHN0cmluZ1tdKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gICAgdXBkYXRlU3RhdGUoeyBpc1N1Ym1pdHRpbmc6IHRydWUsIGVycm9yOiBudWxsLCBsYXN0QWN0aW9uOiAnYnVsa0RlbGV0ZScgfSk7XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IG1ha2VSZXF1ZXN0KGAke29wdGlvbnMuYXBpRW5kcG9pbnR9L2J1bGtgLCB7XG4gICAgICAgIG1ldGhvZDogJ0RFTEVURScsXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgaWRzIH0pLFxuICAgICAgfSk7XG4gICAgICBcbiAgICAgIC8vIFJlbW92ZSBkZWxldGVkIGl0ZW1zXG4gICAgICB1cGRhdGVTdGF0ZSh7IFxuICAgICAgICBpdGVtczogc3RhdGUuaXRlbXMuZmlsdGVyKGl0ZW0gPT4gIWlkcy5pbmNsdWRlcyhpdGVtLmlkKSksXG4gICAgICAgIGlzU3VibWl0dGluZzogZmFsc2UsXG4gICAgICAgIHNlbGVjdGVkSXRlbXM6IG5ldyBTZXQoKSAvLyBDbGVhciBzZWxlY3Rpb24gYWZ0ZXIgYnVsayBvcGVyYXRpb25cbiAgICAgIH0pO1xuICAgICAgXG4gICAgICBoYW5kbGVTdWNjZXNzKCdidWxrRGVsZXRlJyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGhhbmRsZUVycm9yKCdidWxrRGVsZXRlJywgZXJyb3IgYXMgRXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9LCBbb3B0aW9ucy5hcGlFbmRwb2ludCwgc3RhdGUuaXRlbXMsIG1ha2VSZXF1ZXN0LCB1cGRhdGVTdGF0ZSwgaGFuZGxlRXJyb3IsIGhhbmRsZVN1Y2Nlc3NdKTtcbiAgXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIC8vIFNFTEVDVElPTiBNQU5BR0VNRU5UXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIFxuICBjb25zdCBzZWxlY3RJdGVtID0gdXNlQ2FsbGJhY2soKGlkOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBuZXdTZXQgPSBuZXcgU2V0KHN0YXRlLnNlbGVjdGVkSXRlbXMpO1xuICAgIG5ld1NldC5hZGQoaWQpO1xuICAgIHVwZGF0ZVN0YXRlKHsgXG4gICAgICBzZWxlY3RlZEl0ZW1zOiBuZXdTZXQgXG4gICAgfSk7XG4gIH0sIFtzdGF0ZS5zZWxlY3RlZEl0ZW1zLCB1cGRhdGVTdGF0ZV0pO1xuICBcbiAgY29uc3Qgc2VsZWN0QWxsID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIHVwZGF0ZVN0YXRlKHsgXG4gICAgICBzZWxlY3RlZEl0ZW1zOiBuZXcgU2V0KHN0YXRlLml0ZW1zLm1hcChpdGVtID0+IGl0ZW0uaWQpKSBcbiAgICB9KTtcbiAgfSwgW3N0YXRlLml0ZW1zLCB1cGRhdGVTdGF0ZV0pO1xuICBcbiAgY29uc3QgY2xlYXJTZWxlY3Rpb24gPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgdXBkYXRlU3RhdGUoeyBzZWxlY3RlZEl0ZW1zOiBuZXcgU2V0KCkgfSk7XG4gIH0sIFt1cGRhdGVTdGF0ZV0pO1xuICBcbiAgY29uc3QgdG9nZ2xlU2VsZWN0aW9uID0gdXNlQ2FsbGJhY2soKGlkOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBuZXdTZWxlY3Rpb24gPSBuZXcgU2V0KHN0YXRlLnNlbGVjdGVkSXRlbXMpO1xuICAgIGlmIChuZXdTZWxlY3Rpb24uaGFzKGlkKSkge1xuICAgICAgbmV3U2VsZWN0aW9uLmRlbGV0ZShpZCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIG5ld1NlbGVjdGlvbi5hZGQoaWQpO1xuICAgIH1cbiAgICB1cGRhdGVTdGF0ZSh7IHNlbGVjdGVkSXRlbXM6IG5ld1NlbGVjdGlvbiB9KTtcbiAgfSwgW3N0YXRlLnNlbGVjdGVkSXRlbXMsIHVwZGF0ZVN0YXRlXSk7XG4gIFxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICAvLyBVVElMSVRZIEFDVElPTlNcbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgXG4gIGNvbnN0IHNldEl0ZW1zID0gdXNlQ2FsbGJhY2soKGl0ZW1zOiBUW10pID0+IHtcbiAgICB1cGRhdGVTdGF0ZSh7IGl0ZW1zIH0pO1xuICB9LCBbdXBkYXRlU3RhdGVdKTtcbiAgXG4gIGNvbnN0IGNsZWFyRXJyb3IgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgdXBkYXRlU3RhdGUoeyBlcnJvcjogbnVsbCB9KTtcbiAgfSwgW3VwZGF0ZVN0YXRlXSk7XG4gIFxuICBjb25zdCByZWZyZXNoID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIHJldHVybiBmZXRjaEl0ZW1zKCk7XG4gIH0sIFtmZXRjaEl0ZW1zXSk7XG4gIFxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICAvLyBSRVRVUk4gU1RBVEUgQU5EIEFDVElPTlNcbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgXG4gIHJldHVybiBbXG4gICAgc3RhdGUsXG4gICAge1xuICAgICAgZmV0Y2hJdGVtcyxcbiAgICAgIGNyZWF0ZUl0ZW0sXG4gICAgICB1cGRhdGVJdGVtLFxuICAgICAgZGVsZXRlSXRlbSxcbiAgICAgIGJ1bGtVcGRhdGUsXG4gICAgICBidWxrRGVsZXRlLFxuICAgICAgc2VsZWN0SXRlbSxcbiAgICAgIHNlbGVjdEFsbCxcbiAgICAgIGNsZWFyU2VsZWN0aW9uLFxuICAgICAgdG9nZ2xlU2VsZWN0aW9uLFxuICAgICAgc2V0SXRlbXMsXG4gICAgICBjbGVhckVycm9yLFxuICAgICAgcmVmcmVzaCxcbiAgICB9XG4gIF07XG59Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJ1c2VSZWYiLCJ1c2VDcnVkIiwib3B0aW9ucyIsInN0YXRlIiwic2V0U3RhdGUiLCJpdGVtcyIsImluaXRpYWxEYXRhIiwibG9hZGluZyIsImVycm9yIiwic2VsZWN0ZWRJdGVtcyIsIlNldCIsImlzU3VibWl0dGluZyIsImxhc3RBY3Rpb24iLCJhYm9ydENvbnRyb2xsZXJSZWYiLCJ1cGRhdGVTdGF0ZSIsInVwZGF0ZXMiLCJwcmV2IiwiaGFuZGxlRXJyb3IiLCJhY3Rpb24iLCJjb25zb2xlIiwibWVzc2FnZSIsIm9uRXJyb3IiLCJoYW5kbGVTdWNjZXNzIiwiaXRlbSIsIm9uU3VjY2VzcyIsIm1ha2VSZXF1ZXN0IiwidXJsIiwiY3VycmVudCIsImFib3J0IiwiQWJvcnRDb250cm9sbGVyIiwicmVzcG9uc2UiLCJmZXRjaCIsInNpZ25hbCIsImhlYWRlcnMiLCJvayIsImVycm9yRGF0YSIsImpzb24iLCJjYXRjaCIsIkVycm9yIiwic3RhdHVzIiwic3RhdHVzVGV4dCIsImZldGNoSXRlbXMiLCJwYXJhbXMiLCJVUkwiLCJhcGlFbmRwb2ludCIsIk9iamVjdCIsImVudHJpZXMiLCJmb3JFYWNoIiwia2V5IiwidmFsdWUiLCJ1bmRlZmluZWQiLCJzZWFyY2hQYXJhbXMiLCJhcHBlbmQiLCJTdHJpbmciLCJ0b1N0cmluZyIsImRhdGEiLCJBcnJheSIsImlzQXJyYXkiLCJuYW1lIiwiY3JlYXRlSXRlbSIsIm1ldGhvZCIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwibmV3SXRlbSIsIm9wdGltaXN0aWNVcGRhdGVzIiwidXBkYXRlSXRlbSIsImlkIiwib3JpZ2luYWxJdGVtcyIsIm9wdGltaXN0aWNJdGVtcyIsIm1hcCIsInVwZGF0ZWRJdGVtIiwiZGVsZXRlSXRlbSIsImZpbHRlciIsImJ1bGtVcGRhdGUiLCJpZHMiLCJ1cGRhdGVkSXRlbXMiLCJ1cGRhdGVkIiwiZmluZCIsInUiLCJidWxrRGVsZXRlIiwiaW5jbHVkZXMiLCJzZWxlY3RJdGVtIiwibmV3U2V0IiwiYWRkIiwic2VsZWN0QWxsIiwiY2xlYXJTZWxlY3Rpb24iLCJ0b2dnbGVTZWxlY3Rpb24iLCJuZXdTZWxlY3Rpb24iLCJoYXMiLCJkZWxldGUiLCJzZXRJdGVtcyIsImNsZWFyRXJyb3IiLCJyZWZyZXNoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useCrud.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/qr-generation.ts":
/*!***************************************!*\
  !*** ./src/services/qr-generation.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QRCodeGenerationService: () => (/* binding */ QRCodeGenerationService)\n/* harmony export */ });\n/* harmony import */ var qrcode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! qrcode */ \"(ssr)/./node_modules/qrcode/lib/index.js\");\n/* harmony import */ var _types_qr_management__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types/qr-management */ \"(ssr)/./src/types/qr-management.ts\");\n// QR Code Generation Service with comprehensive customization options\n// Handles single and bulk QR code generation with various export formats\n\n\nclass QRCodeGenerationService {\n    // Dynamic import loaders for PDF libraries\n    async loadPDFLibraries() {\n        try {\n            const [jsPDF, html2canvas] = await Promise.all([\n                Promise.all(/*! import() */[__webpack_require__.e(\"jspdf\"), __webpack_require__.e(\"vendor-chunks/@babel\"), __webpack_require__.e(\"vendor-chunks/fflate\")]).then(__webpack_require__.bind(__webpack_require__, /*! jspdf */ \"(ssr)/./node_modules/jspdf/dist/jspdf.es.min.js\")),\n                __webpack_require__.e(/*! import() */ \"html2canvas\").then(__webpack_require__.bind(__webpack_require__, /*! html2canvas */ \"(ssr)/./node_modules/html2canvas/dist/html2canvas.esm.js\"))\n            ]);\n            return {\n                jsPDF: jsPDF.default,\n                html2canvas: html2canvas.default\n            };\n        } catch (error) {\n            console.error(\"Failed to load PDF libraries:\", error);\n            throw new Error(\"PDF generation libraries failed to load\");\n        }\n    }\n    // =====================================================\n    // SINGLE QR CODE GENERATION\n    // =====================================================\n    /**\n   * Generate a single QR code with custom options\n   * Returns both dataURL and SVG representations for flexibility\n   */ async generateQRCode(room, options) {\n        const startTime = performance.now();\n        try {\n            // Build the full URL for the QR code with proper validation\n            const baseUrl = options.baseUrl || \"http://localhost:3000\" || 0;\n            // Validate base URL\n            if (!baseUrl || typeof baseUrl !== \"string\" || baseUrl.trim() === \"\") {\n                throw new Error(\"Invalid base URL provided\");\n            }\n            // Additional validation for URL format\n            try {\n                new URL(baseUrl);\n            } catch  {\n                throw new Error(`Invalid base URL format: ${baseUrl}`);\n            }\n            const menuUrl = new URL(\"/menu\", baseUrl);\n            // Add query parameters safely\n            const params = new URLSearchParams();\n            if (options.qrCode) params.set(\"qr\", options.qrCode);\n            if (room.id) params.set(\"room\", room.id);\n            if (room.tenantId) params.set(\"tenant\", room.tenantId);\n            const fullUrl = `${menuUrl.toString()}?${params.toString()}`;\n            // Generate QR code configuration\n            const qrConfig = {\n                errorCorrectionLevel: options.errorCorrectionLevel,\n                margin: options.margin,\n                color: {\n                    dark: options.foregroundColor,\n                    light: options.backgroundColor\n                },\n                width: options.size\n            };\n            // Generate dataURL (PNG format)\n            const dataUrl = await qrcode__WEBPACK_IMPORTED_MODULE_0__.toDataURL(fullUrl, qrConfig);\n            // Generate SVG format for better scalability\n            const svgData = await qrcode__WEBPACK_IMPORTED_MODULE_0__.toString(fullUrl, {\n                ...qrConfig,\n                type: \"svg\",\n                width: options.size\n            });\n            // Apply logo overlay if requested\n            let finalDataUrl = dataUrl;\n            if (options.includelogo && options.logoUrl) {\n                finalDataUrl = await this.applyLogoOverlay(dataUrl, options);\n            }\n            // Apply border if requested\n            if (options.border) {\n                finalDataUrl = await this.applyBorder(finalDataUrl, options);\n            }\n            const generationTime = performance.now() - startTime;\n            // Build QR code data object\n            const qrCodeData = {\n                id: `qr_${room.id}_${Date.now()}`,\n                roomId: room.id,\n                qrCode: options.qrCode,\n                roomNumber: room.roomNumber,\n                floor: room.floor,\n                capacity: room.capacity,\n                url: fullUrl,\n                generatedAt: new Date().toISOString(),\n                generatedBy: \"admin\",\n                options,\n                exportCount: 0,\n                dataUrl: finalDataUrl,\n                svgData: svgData\n            };\n            return {\n                success: true,\n                qrCode: qrCodeData,\n                dataUrl: finalDataUrl,\n                svgData,\n                generationTime\n            };\n        } catch (error) {\n            console.error(\"QR generation error:\", error);\n            return {\n                success: false,\n                qrCode: {},\n                error: error instanceof Error ? error.message : \"Unknown error occurred\",\n                generationTime: performance.now() - startTime\n            };\n        }\n    }\n    /**\n   * Regenerate QR code for an existing room\n   * Useful when QR codes need to be refreshed for security\n   */ async regenerateQRCode(room, options) {\n        // Generate new unique QR code identifier\n        const newQRCode = `qr_${room.id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        const fullOptions = {\n            ..._types_qr_management__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_QR_OPTIONS,\n            ...options || {},\n            qrCode: newQRCode\n        };\n        return this.generateQRCode(room, fullOptions);\n    }\n    // =====================================================\n    // BULK QR CODE GENERATION\n    // =====================================================\n    /**\n   * Generate QR codes for multiple rooms in batch\n   * Provides progress tracking and error handling for individual items\n   */ async bulkGenerateQRCodes(rooms, options, onProgress) {\n        const startTime = performance.now();\n        const results = [];\n        const failed = [];\n        try {\n            for(let i = 0; i < rooms.length; i++){\n                const room = rooms[i];\n                try {\n                    // Generate unique QR code for each room\n                    const roomOptions = {\n                        ...options,\n                        qrCode: `qr_${room.id}_${Date.now()}_${i}`\n                    };\n                    const result = await this.generateQRCode(room, roomOptions);\n                    results.push(result);\n                    // Report progress\n                    if (onProgress) {\n                        onProgress(i + 1, rooms.length);\n                    }\n                    // Small delay to prevent overwhelming the system\n                    if (i < rooms.length - 1) {\n                        await new Promise((resolve)=>setTimeout(resolve, 10));\n                    }\n                } catch (error) {\n                    failed.push({\n                        roomId: room.id,\n                        error: error instanceof Error ? error.message : \"Unknown error\"\n                    });\n                }\n            }\n            const processingTime = performance.now() - startTime;\n            const successCount = results.filter((r)=>r.success).length;\n            const failureCount = failed.length;\n            return {\n                success: failureCount === 0,\n                results,\n                failed,\n                totalProcessed: rooms.length,\n                successCount,\n                failureCount,\n                processingTime\n            };\n        } catch (error) {\n            return {\n                success: false,\n                results: [],\n                failed: rooms.map((room)=>({\n                        roomId: room.id,\n                        error: error instanceof Error ? error.message : \"Bulk operation failed\"\n                    })),\n                totalProcessed: rooms.length,\n                successCount: 0,\n                failureCount: rooms.length,\n                processingTime: performance.now() - startTime\n            };\n        }\n    }\n    // =====================================================\n    // EXPORT FUNCTIONALITY\n    // =====================================================\n    /**\n   * Export single QR code in various formats\n   */ async exportQRCode(qrCodeData, exportOptions) {\n        try {\n            const { format, quality, size } = exportOptions;\n            switch(format){\n                case \"png\":\n                    return await this.exportAsPNG(qrCodeData, exportOptions);\n                case \"svg\":\n                    return await this.exportAsSVG(qrCodeData, exportOptions);\n                case \"pdf\":\n                    return await this.exportAsPDF([\n                        qrCodeData\n                    ], exportOptions);\n                default:\n                    throw new Error(`Unsupported export format: ${format}`);\n            }\n        } catch (error) {\n            return {\n                success: false,\n                format: exportOptions.format,\n                size: 0,\n                filename: \"\",\n                error: error instanceof Error ? error.message : \"Export failed\"\n            };\n        }\n    }\n    /**\n   * Export multiple QR codes as PDF with custom layout\n   */ async bulkExportQRCodes(qrCodes, exportOptions) {\n        try {\n            if (exportOptions.format === \"pdf\") {\n                return await this.exportAsPDF(qrCodes, exportOptions);\n            } else {\n                // For non-PDF formats, create a ZIP file\n                return await this.exportAsZip(qrCodes, exportOptions);\n            }\n        } catch (error) {\n            return {\n                success: false,\n                format: exportOptions.format,\n                size: 0,\n                filename: \"\",\n                error: error instanceof Error ? error.message : \"Bulk export failed\"\n            };\n        }\n    }\n    // =====================================================\n    // PRIVATE HELPER METHODS\n    // =====================================================\n    /**\n   * Apply logo overlay to QR code\n   */ async applyLogoOverlay(qrDataUrl, options) {\n        return new Promise((resolve, reject)=>{\n            const canvas = document.createElement(\"canvas\");\n            const ctx = canvas.getContext(\"2d\");\n            if (!ctx) {\n                reject(new Error(\"Canvas context not available\"));\n                return;\n            }\n            const qrImage = new Image();\n            qrImage.onload = ()=>{\n                canvas.width = qrImage.width;\n                canvas.height = qrImage.height;\n                // Draw QR code\n                ctx.drawImage(qrImage, 0, 0);\n                // Load and draw logo\n                const logoImage = new Image();\n                logoImage.onload = ()=>{\n                    const logoSize = (options.logoSize || 20) / 100 * qrImage.width;\n                    const logoX = (qrImage.width - logoSize) / 2;\n                    const logoY = (qrImage.height - logoSize) / 2;\n                    // Add white background for logo\n                    ctx.fillStyle = \"white\";\n                    ctx.fillRect(logoX - 4, logoY - 4, logoSize + 8, logoSize + 8);\n                    // Draw logo\n                    ctx.drawImage(logoImage, logoX, logoY, logoSize, logoSize);\n                    resolve(canvas.toDataURL(\"image/png\"));\n                };\n                logoImage.onerror = ()=>reject(new Error(\"Failed to load logo\"));\n                logoImage.src = options.logoUrl;\n            };\n            qrImage.onerror = ()=>reject(new Error(\"Failed to load QR code\"));\n            qrImage.src = qrDataUrl;\n        });\n    }\n    /**\n   * Apply decorative border to QR code\n   */ async applyBorder(qrDataUrl, options) {\n        return new Promise((resolve, reject)=>{\n            const canvas = document.createElement(\"canvas\");\n            const ctx = canvas.getContext(\"2d\");\n            if (!ctx) {\n                reject(new Error(\"Canvas context not available\"));\n                return;\n            }\n            const qrImage = new Image();\n            qrImage.onload = ()=>{\n                const borderWidth = options.borderWidth || 2;\n                canvas.width = qrImage.width + borderWidth * 2;\n                canvas.height = qrImage.height + borderWidth * 2;\n                // Draw border\n                ctx.fillStyle = options.borderColor || \"#000000\";\n                ctx.fillRect(0, 0, canvas.width, canvas.height);\n                // Draw QR code on top\n                ctx.drawImage(qrImage, borderWidth, borderWidth);\n                resolve(canvas.toDataURL(\"image/png\"));\n            };\n            qrImage.onerror = ()=>reject(new Error(\"Failed to load QR code for border\"));\n            qrImage.src = qrDataUrl;\n        });\n    }\n    /**\n   * Export as PNG format\n   */ async exportAsPNG(qrCodeData, options) {\n        const canvas = document.createElement(\"canvas\");\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) {\n            throw new Error(\"Canvas context not available\");\n        }\n        const image = new Image();\n        await new Promise((resolve, reject)=>{\n            image.onload = resolve;\n            image.onerror = reject;\n            image.src = qrCodeData.dataUrl;\n        });\n        // Resize if needed\n        const targetSize = options.size || 256;\n        canvas.width = targetSize;\n        canvas.height = targetSize;\n        ctx.drawImage(image, 0, 0, targetSize, targetSize);\n        // Convert to blob\n        const blob = await new Promise((resolve, reject)=>{\n            canvas.toBlob((blob)=>{\n                if (blob) resolve(blob);\n                else reject(new Error(\"Failed to create blob\"));\n            }, \"image/png\", options.quality / 100);\n        });\n        const filename = `qr_${qrCodeData.roomNumber}_${Date.now()}.png`;\n        return {\n            success: true,\n            format: \"png\",\n            size: blob.size,\n            blob,\n            filename,\n            downloadUrl: URL.createObjectURL(blob)\n        };\n    }\n    /**\n   * Export as SVG format\n   */ async exportAsSVG(qrCodeData, options) {\n        const svgContent = qrCodeData.svgData;\n        if (!svgContent) {\n            throw new Error(\"SVG data not available\");\n        }\n        const blob = new Blob([\n            svgContent\n        ], {\n            type: \"image/svg+xml\"\n        });\n        const filename = `qr_${qrCodeData.roomNumber}_${Date.now()}.svg`;\n        return {\n            success: true,\n            format: \"svg\",\n            size: blob.size,\n            blob,\n            filename,\n            downloadUrl: URL.createObjectURL(blob)\n        };\n    }\n    /**\n   * Export as PDF with custom layout\n   */ async exportAsPDF(qrCodes, options) {\n        const { jsPDF } = await this.loadPDFLibraries();\n        const layout = options.pdfOptions?.layout || this.getDefaultLayout();\n        const pdf = new jsPDF({\n            orientation: layout.orientation,\n            unit: \"mm\",\n            format: layout.pageSize.toLowerCase()\n        });\n        const pageWidth = pdf.internal.pageSize.getWidth();\n        const pageHeight = pdf.internal.pageSize.getHeight();\n        // Calculate grid dimensions\n        const contentWidth = pageWidth - layout.margin * 2;\n        const contentHeight = pageHeight - layout.margin * 2;\n        const cellWidth = contentWidth / layout.columns;\n        const cellHeight = contentHeight / layout.rows;\n        const qrSize = Math.min(cellWidth, cellHeight) - layout.spacing;\n        let currentPage = 0;\n        let currentRow = 0;\n        let currentCol = 0;\n        for(let i = 0; i < qrCodes.length; i++){\n            const qrCode = qrCodes[i];\n            // Add new page if needed\n            if (i > 0 && currentRow === 0 && currentCol === 0) {\n                pdf.addPage();\n                currentPage++;\n            }\n            // Calculate position\n            const x = layout.margin + currentCol * cellWidth + (cellWidth - qrSize) / 2;\n            const y = layout.margin + currentRow * cellHeight + (cellHeight - qrSize) / 2;\n            // Add QR code image\n            if (qrCode.dataUrl) {\n                pdf.addImage(qrCode.dataUrl, \"PNG\", x, y, qrSize, qrSize);\n            }\n            // Add room information if enabled\n            if (layout.includeRoomNumber || layout.includeFloor) {\n                const textY = y + qrSize + 5;\n                pdf.setFontSize(layout.fontSize);\n                let text = \"\";\n                if (layout.includeRoomNumber) {\n                    text += `Room ${qrCode.roomNumber}`;\n                }\n                if (layout.includeFloor && qrCode.floor) {\n                    text += layout.includeRoomNumber ? ` - Floor ${qrCode.floor}` : `Floor ${qrCode.floor}`;\n                }\n                const textWidth = pdf.getTextWidth(text);\n                const textX = x + (qrSize - textWidth) / 2;\n                pdf.text(text, textX, textY);\n            }\n            // Update grid position\n            currentCol++;\n            if (currentCol >= layout.columns) {\n                currentCol = 0;\n                currentRow++;\n                if (currentRow >= layout.rows) {\n                    currentRow = 0;\n                }\n            }\n        }\n        // Add headers and footers if enabled\n        if (layout.title) {\n            pdf.setPage(1);\n            pdf.setFontSize(layout.titleFontSize);\n            pdf.text(layout.title, pageWidth / 2, layout.margin / 2, {\n                align: \"center\"\n            });\n        }\n        const pdfBlob = pdf.output(\"blob\");\n        const filename = `qr_codes_bulk_${Date.now()}.pdf`;\n        return {\n            success: true,\n            format: \"pdf\",\n            size: pdfBlob.size,\n            blob: pdfBlob,\n            filename,\n            downloadUrl: URL.createObjectURL(pdfBlob)\n        };\n    }\n    /**\n   * Export multiple files as ZIP\n   */ async exportAsZip(qrCodes, options) {\n        // This would require a ZIP library like JSZip\n        // For now, we'll return an error suggesting PDF export for multiple files\n        throw new Error(\"ZIP export not implemented. Please use PDF format for bulk export.\");\n    }\n    /**\n   * Get default layout options\n   */ getDefaultLayout() {\n        return {\n            pageSize: \"A4\",\n            orientation: \"portrait\",\n            margin: 20,\n            columns: 3,\n            rows: 4,\n            spacing: 10,\n            includeRoomNumber: true,\n            includeFloor: true,\n            includeQRCodeText: true,\n            includeInstructions: true,\n            fontSize: 12,\n            fontFamily: \"Arial, sans-serif\",\n            titleFontSize: 16,\n            includeGenerationDate: true,\n            includeTenantInfo: true\n        };\n    }\n    // =====================================================\n    // UTILITY METHODS\n    // =====================================================\n    /**\n   * Validate QR code by scanning it\n   */ async validateQRCode(dataUrl) {\n        try {\n            // This would require a QR code reader library\n            // For now, we'll return a simple validation\n            return {\n                isValid: true,\n                url: \"Validation would require QR reader library\"\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                error: error instanceof Error ? error.message : \"Validation failed\"\n            };\n        }\n    }\n    /**\n   * Get optimal QR code size based on content length\n   */ getOptimalSize(content) {\n        if (content.length < 50) return 128;\n        if (content.length < 100) return 256;\n        if (content.length < 200) return 384;\n        return 512;\n    }\n    /**\n   * Preview QR code options before generation\n   */ async previewOptions(options) {\n        const baseUrl = options.baseUrl || \"http://localhost:3000\";\n        // Validate base URL\n        if (!baseUrl || typeof baseUrl !== \"string\" || baseUrl.trim() === \"\") {\n            throw new Error(\"Invalid base URL provided for preview\");\n        }\n        // Additional validation for URL format\n        try {\n            new URL(baseUrl);\n        } catch  {\n            throw new Error(`Invalid base URL format for preview: ${baseUrl}`);\n        }\n        const menuUrl = new URL(\"/menu\", baseUrl);\n        const params = new URLSearchParams();\n        params.set(\"qr\", \"sample_preview\");\n        const sampleUrl = `${menuUrl.toString()}?${params.toString()}`;\n        try {\n            return await qrcode__WEBPACK_IMPORTED_MODULE_0__.toDataURL(sampleUrl, {\n                errorCorrectionLevel: options.errorCorrectionLevel,\n                margin: options.margin,\n                color: {\n                    dark: options.foregroundColor,\n                    light: options.backgroundColor\n                },\n                width: options.size\n            });\n        } catch (error) {\n            throw new Error(`Preview generation failed: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/qr-generation.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/qr-management.ts":
/*!************************************!*\
  !*** ./src/types/qr-management.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COMMON_AMENITIES: () => (/* binding */ COMMON_AMENITIES),\n/* harmony export */   DEFAULT_LAYOUT_OPTIONS: () => (/* binding */ DEFAULT_LAYOUT_OPTIONS),\n/* harmony export */   DEFAULT_QR_OPTIONS: () => (/* binding */ DEFAULT_QR_OPTIONS),\n/* harmony export */   ROOM_STATUSES: () => (/* binding */ ROOM_STATUSES)\n/* harmony export */ });\n// Types and interfaces for QR code management system\n// Extends the existing room management with QR code generation and export functionality\n// Constants and defaults\nconst DEFAULT_QR_OPTIONS = {\n    baseUrl: \"http://localhost:3000\",\n    qrCode: \"\",\n    size: 256,\n    foregroundColor: \"#000000\",\n    backgroundColor: \"#FFFFFF\",\n    errorCorrectionLevel: \"M\",\n    includelogo: true,\n    logoSize: 20,\n    margin: 4,\n    border: false,\n    borderWidth: 1,\n    borderColor: \"#000000\"\n};\nconst DEFAULT_LAYOUT_OPTIONS = {\n    pageSize: \"A4\",\n    orientation: \"portrait\",\n    margin: 20,\n    columns: 3,\n    rows: 4,\n    spacing: 10,\n    includeRoomNumber: true,\n    includeFloor: true,\n    includeQRCodeText: true,\n    includeInstructions: true,\n    fontSize: 12,\n    fontFamily: \"Arial, sans-serif\",\n    titleFontSize: 16,\n    includeGenerationDate: true,\n    includeTenantInfo: true\n};\nconst ROOM_STATUSES = [\n    {\n        value: \"AVAILABLE\",\n        label: \"Available\",\n        color: \"bg-green-100 text-green-800\"\n    },\n    {\n        value: \"OCCUPIED\",\n        label: \"Occupied\",\n        color: \"bg-blue-100 text-blue-800\"\n    },\n    {\n        value: \"RESERVED\",\n        label: \"Reserved\",\n        color: \"bg-yellow-100 text-yellow-800\"\n    },\n    {\n        value: \"MAINTENANCE\",\n        label: \"Maintenance\",\n        color: \"bg-orange-100 text-orange-800\"\n    },\n    {\n        value: \"OUT_OF_ORDER\",\n        label: \"Out of Order\",\n        color: \"bg-red-100 text-red-800\"\n    }\n];\nconst COMMON_AMENITIES = [\n    \"WiFi\",\n    \"TV\",\n    \"Air Conditioning\",\n    \"Balcony\",\n    \"Mini Bar\",\n    \"Room Service\",\n    \"Ocean View\",\n    \"City View\",\n    \"Bathtub\",\n    \"Shower\",\n    \"Safe\",\n    \"Refrigerator\"\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/qr-management.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dfe9ddaa74e5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmhlZW1kaW5lLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz80Nzc3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZGZlOWRkYWE3NGU1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/admin/qr/page.tsx":
/*!***********************************!*\
  !*** ./src/app/admin/qr/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/BHEEMDINE/src/app/admin/qr/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"BHEEMDINE - Digital Restaurant Management\",\n    description: \"QR-based digital menu and order management system\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCwrSkFBZTtzQkFBR0s7Ozs7Ozs7Ozs7O0FBR3pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmhlZW1kaW5lLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdCSEVFTURJTkUgLSBEaWdpdGFsIFJlc3RhdXJhbnQgTWFuYWdlbWVudCcsXG4gIGRlc2NyaXB0aW9uOiAnUVItYmFzZWQgZGlnaXRhbCBtZW51IGFuZCBvcmRlciBtYW5hZ2VtZW50IHN5c3RlbScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PntjaGlsZHJlbn08L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59Il0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/qrcode","vendor-chunks/pngjs","vendor-chunks/encode-utf8","vendor-chunks/dijkstrajs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fqr%2Fpage&page=%2Fadmin%2Fqr%2Fpage&appPaths=%2Fadmin%2Fqr%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fqr%2Fpage.tsx&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();