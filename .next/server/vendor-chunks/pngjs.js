"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pngjs";
exports.ids = ["vendor-chunks/pngjs"];
exports.modules = {

/***/ "(ssr)/./node_modules/pngjs/lib/bitmapper.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/bitmapper.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nlet interlaceUtils = __webpack_require__(/*! ./interlace */ \"(ssr)/./node_modules/pngjs/lib/interlace.js\");\nlet pixelBppMapper = [\n    // 0 - dummy entry\n    function() {},\n    // 1 - L\n    // 0: 0, 1: 0, 2: 0, 3: 0xff\n    function(pxData, data, pxPos, rawPos) {\n        if (rawPos === data.length) {\n            throw new Error(\"Ran out of data\");\n        }\n        let pixel = data[rawPos];\n        pxData[pxPos] = pixel;\n        pxData[pxPos + 1] = pixel;\n        pxData[pxPos + 2] = pixel;\n        pxData[pxPos + 3] = 0xff;\n    },\n    // 2 - LA\n    // 0: 0, 1: 0, 2: 0, 3: 1\n    function(pxData, data, pxPos, rawPos) {\n        if (rawPos + 1 >= data.length) {\n            throw new Error(\"Ran out of data\");\n        }\n        let pixel = data[rawPos];\n        pxData[pxPos] = pixel;\n        pxData[pxPos + 1] = pixel;\n        pxData[pxPos + 2] = pixel;\n        pxData[pxPos + 3] = data[rawPos + 1];\n    },\n    // 3 - RGB\n    // 0: 0, 1: 1, 2: 2, 3: 0xff\n    function(pxData, data, pxPos, rawPos) {\n        if (rawPos + 2 >= data.length) {\n            throw new Error(\"Ran out of data\");\n        }\n        pxData[pxPos] = data[rawPos];\n        pxData[pxPos + 1] = data[rawPos + 1];\n        pxData[pxPos + 2] = data[rawPos + 2];\n        pxData[pxPos + 3] = 0xff;\n    },\n    // 4 - RGBA\n    // 0: 0, 1: 1, 2: 2, 3: 3\n    function(pxData, data, pxPos, rawPos) {\n        if (rawPos + 3 >= data.length) {\n            throw new Error(\"Ran out of data\");\n        }\n        pxData[pxPos] = data[rawPos];\n        pxData[pxPos + 1] = data[rawPos + 1];\n        pxData[pxPos + 2] = data[rawPos + 2];\n        pxData[pxPos + 3] = data[rawPos + 3];\n    }\n];\nlet pixelBppCustomMapper = [\n    // 0 - dummy entry\n    function() {},\n    // 1 - L\n    // 0: 0, 1: 0, 2: 0, 3: 0xff\n    function(pxData, pixelData, pxPos, maxBit) {\n        let pixel = pixelData[0];\n        pxData[pxPos] = pixel;\n        pxData[pxPos + 1] = pixel;\n        pxData[pxPos + 2] = pixel;\n        pxData[pxPos + 3] = maxBit;\n    },\n    // 2 - LA\n    // 0: 0, 1: 0, 2: 0, 3: 1\n    function(pxData, pixelData, pxPos) {\n        let pixel = pixelData[0];\n        pxData[pxPos] = pixel;\n        pxData[pxPos + 1] = pixel;\n        pxData[pxPos + 2] = pixel;\n        pxData[pxPos + 3] = pixelData[1];\n    },\n    // 3 - RGB\n    // 0: 0, 1: 1, 2: 2, 3: 0xff\n    function(pxData, pixelData, pxPos, maxBit) {\n        pxData[pxPos] = pixelData[0];\n        pxData[pxPos + 1] = pixelData[1];\n        pxData[pxPos + 2] = pixelData[2];\n        pxData[pxPos + 3] = maxBit;\n    },\n    // 4 - RGBA\n    // 0: 0, 1: 1, 2: 2, 3: 3\n    function(pxData, pixelData, pxPos) {\n        pxData[pxPos] = pixelData[0];\n        pxData[pxPos + 1] = pixelData[1];\n        pxData[pxPos + 2] = pixelData[2];\n        pxData[pxPos + 3] = pixelData[3];\n    }\n];\nfunction bitRetriever(data, depth) {\n    let leftOver = [];\n    let i = 0;\n    function split() {\n        if (i === data.length) {\n            throw new Error(\"Ran out of data\");\n        }\n        let byte = data[i];\n        i++;\n        let byte8, byte7, byte6, byte5, byte4, byte3, byte2, byte1;\n        switch(depth){\n            default:\n                throw new Error(\"unrecognised depth\");\n            case 16:\n                byte2 = data[i];\n                i++;\n                leftOver.push((byte << 8) + byte2);\n                break;\n            case 4:\n                byte2 = byte & 0x0f;\n                byte1 = byte >> 4;\n                leftOver.push(byte1, byte2);\n                break;\n            case 2:\n                byte4 = byte & 3;\n                byte3 = byte >> 2 & 3;\n                byte2 = byte >> 4 & 3;\n                byte1 = byte >> 6 & 3;\n                leftOver.push(byte1, byte2, byte3, byte4);\n                break;\n            case 1:\n                byte8 = byte & 1;\n                byte7 = byte >> 1 & 1;\n                byte6 = byte >> 2 & 1;\n                byte5 = byte >> 3 & 1;\n                byte4 = byte >> 4 & 1;\n                byte3 = byte >> 5 & 1;\n                byte2 = byte >> 6 & 1;\n                byte1 = byte >> 7 & 1;\n                leftOver.push(byte1, byte2, byte3, byte4, byte5, byte6, byte7, byte8);\n                break;\n        }\n    }\n    return {\n        get: function(count) {\n            while(leftOver.length < count){\n                split();\n            }\n            let returner = leftOver.slice(0, count);\n            leftOver = leftOver.slice(count);\n            return returner;\n        },\n        resetAfterLine: function() {\n            leftOver.length = 0;\n        },\n        end: function() {\n            if (i !== data.length) {\n                throw new Error(\"extra data found\");\n            }\n        }\n    };\n}\nfunction mapImage8Bit(image, pxData, getPxPos, bpp, data, rawPos) {\n    // eslint-disable-line max-params\n    let imageWidth = image.width;\n    let imageHeight = image.height;\n    let imagePass = image.index;\n    for(let y = 0; y < imageHeight; y++){\n        for(let x = 0; x < imageWidth; x++){\n            let pxPos = getPxPos(x, y, imagePass);\n            pixelBppMapper[bpp](pxData, data, pxPos, rawPos);\n            rawPos += bpp; //eslint-disable-line no-param-reassign\n        }\n    }\n    return rawPos;\n}\nfunction mapImageCustomBit(image, pxData, getPxPos, bpp, bits, maxBit) {\n    // eslint-disable-line max-params\n    let imageWidth = image.width;\n    let imageHeight = image.height;\n    let imagePass = image.index;\n    for(let y = 0; y < imageHeight; y++){\n        for(let x = 0; x < imageWidth; x++){\n            let pixelData = bits.get(bpp);\n            let pxPos = getPxPos(x, y, imagePass);\n            pixelBppCustomMapper[bpp](pxData, pixelData, pxPos, maxBit);\n        }\n        bits.resetAfterLine();\n    }\n}\nexports.dataToBitMap = function(data, bitmapInfo) {\n    let width = bitmapInfo.width;\n    let height = bitmapInfo.height;\n    let depth = bitmapInfo.depth;\n    let bpp = bitmapInfo.bpp;\n    let interlace = bitmapInfo.interlace;\n    let bits;\n    if (depth !== 8) {\n        bits = bitRetriever(data, depth);\n    }\n    let pxData;\n    if (depth <= 8) {\n        pxData = Buffer.alloc(width * height * 4);\n    } else {\n        pxData = new Uint16Array(width * height * 4);\n    }\n    let maxBit = Math.pow(2, depth) - 1;\n    let rawPos = 0;\n    let images;\n    let getPxPos;\n    if (interlace) {\n        images = interlaceUtils.getImagePasses(width, height);\n        getPxPos = interlaceUtils.getInterlaceIterator(width, height);\n    } else {\n        let nonInterlacedPxPos = 0;\n        getPxPos = function() {\n            let returner = nonInterlacedPxPos;\n            nonInterlacedPxPos += 4;\n            return returner;\n        };\n        images = [\n            {\n                width: width,\n                height: height\n            }\n        ];\n    }\n    for(let imageIndex = 0; imageIndex < images.length; imageIndex++){\n        if (depth === 8) {\n            rawPos = mapImage8Bit(images[imageIndex], pxData, getPxPos, bpp, data, rawPos);\n        } else {\n            mapImageCustomBit(images[imageIndex], pxData, getPxPos, bpp, bits, maxBit);\n        }\n    }\n    if (depth === 8) {\n        if (rawPos !== data.length) {\n            throw new Error(\"extra data found\");\n        }\n    } else {\n        bits.end();\n    }\n    return pxData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/bitmapper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/bitpacker.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/bitpacker.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\nmodule.exports = function(dataIn, width, height, options) {\n    let outHasAlpha = [\n        constants.COLORTYPE_COLOR_ALPHA,\n        constants.COLORTYPE_ALPHA\n    ].indexOf(options.colorType) !== -1;\n    if (options.colorType === options.inputColorType) {\n        let bigEndian = function() {\n            let buffer = new ArrayBuffer(2);\n            new DataView(buffer).setInt16(0, 256, true);\n            // Int16Array uses the platform's endianness.\n            return new Int16Array(buffer)[0] !== 256;\n        }();\n        // If no need to convert to grayscale and alpha is present/absent in both, take a fast route\n        if (options.bitDepth === 8 || options.bitDepth === 16 && bigEndian) {\n            return dataIn;\n        }\n    }\n    // map to a UInt16 array if data is 16bit, fix endianness below\n    let data = options.bitDepth !== 16 ? dataIn : new Uint16Array(dataIn.buffer);\n    let maxValue = 255;\n    let inBpp = constants.COLORTYPE_TO_BPP_MAP[options.inputColorType];\n    if (inBpp === 4 && !options.inputHasAlpha) {\n        inBpp = 3;\n    }\n    let outBpp = constants.COLORTYPE_TO_BPP_MAP[options.colorType];\n    if (options.bitDepth === 16) {\n        maxValue = 65535;\n        outBpp *= 2;\n    }\n    let outData = Buffer.alloc(width * height * outBpp);\n    let inIndex = 0;\n    let outIndex = 0;\n    let bgColor = options.bgColor || {};\n    if (bgColor.red === undefined) {\n        bgColor.red = maxValue;\n    }\n    if (bgColor.green === undefined) {\n        bgColor.green = maxValue;\n    }\n    if (bgColor.blue === undefined) {\n        bgColor.blue = maxValue;\n    }\n    function getRGBA() {\n        let red;\n        let green;\n        let blue;\n        let alpha = maxValue;\n        switch(options.inputColorType){\n            case constants.COLORTYPE_COLOR_ALPHA:\n                alpha = data[inIndex + 3];\n                red = data[inIndex];\n                green = data[inIndex + 1];\n                blue = data[inIndex + 2];\n                break;\n            case constants.COLORTYPE_COLOR:\n                red = data[inIndex];\n                green = data[inIndex + 1];\n                blue = data[inIndex + 2];\n                break;\n            case constants.COLORTYPE_ALPHA:\n                alpha = data[inIndex + 1];\n                red = data[inIndex];\n                green = red;\n                blue = red;\n                break;\n            case constants.COLORTYPE_GRAYSCALE:\n                red = data[inIndex];\n                green = red;\n                blue = red;\n                break;\n            default:\n                throw new Error(\"input color type:\" + options.inputColorType + \" is not supported at present\");\n        }\n        if (options.inputHasAlpha) {\n            if (!outHasAlpha) {\n                alpha /= maxValue;\n                red = Math.min(Math.max(Math.round((1 - alpha) * bgColor.red + alpha * red), 0), maxValue);\n                green = Math.min(Math.max(Math.round((1 - alpha) * bgColor.green + alpha * green), 0), maxValue);\n                blue = Math.min(Math.max(Math.round((1 - alpha) * bgColor.blue + alpha * blue), 0), maxValue);\n            }\n        }\n        return {\n            red: red,\n            green: green,\n            blue: blue,\n            alpha: alpha\n        };\n    }\n    for(let y = 0; y < height; y++){\n        for(let x = 0; x < width; x++){\n            let rgba = getRGBA(data, inIndex);\n            switch(options.colorType){\n                case constants.COLORTYPE_COLOR_ALPHA:\n                case constants.COLORTYPE_COLOR:\n                    if (options.bitDepth === 8) {\n                        outData[outIndex] = rgba.red;\n                        outData[outIndex + 1] = rgba.green;\n                        outData[outIndex + 2] = rgba.blue;\n                        if (outHasAlpha) {\n                            outData[outIndex + 3] = rgba.alpha;\n                        }\n                    } else {\n                        outData.writeUInt16BE(rgba.red, outIndex);\n                        outData.writeUInt16BE(rgba.green, outIndex + 2);\n                        outData.writeUInt16BE(rgba.blue, outIndex + 4);\n                        if (outHasAlpha) {\n                            outData.writeUInt16BE(rgba.alpha, outIndex + 6);\n                        }\n                    }\n                    break;\n                case constants.COLORTYPE_ALPHA:\n                case constants.COLORTYPE_GRAYSCALE:\n                    {\n                        // Convert to grayscale and alpha\n                        let grayscale = (rgba.red + rgba.green + rgba.blue) / 3;\n                        if (options.bitDepth === 8) {\n                            outData[outIndex] = grayscale;\n                            if (outHasAlpha) {\n                                outData[outIndex + 1] = rgba.alpha;\n                            }\n                        } else {\n                            outData.writeUInt16BE(grayscale, outIndex);\n                            if (outHasAlpha) {\n                                outData.writeUInt16BE(rgba.alpha, outIndex + 2);\n                            }\n                        }\n                        break;\n                    }\n                default:\n                    throw new Error(\"unrecognised color Type \" + options.colorType);\n            }\n            inIndex += inBpp;\n            outIndex += outBpp;\n        }\n    }\n    return outData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/bitpacker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/chunkstream.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/chunkstream.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet util = __webpack_require__(/*! util */ \"util\");\nlet Stream = __webpack_require__(/*! stream */ \"stream\");\nlet ChunkStream = module.exports = function() {\n    Stream.call(this);\n    this._buffers = [];\n    this._buffered = 0;\n    this._reads = [];\n    this._paused = false;\n    this._encoding = \"utf8\";\n    this.writable = true;\n};\nutil.inherits(ChunkStream, Stream);\nChunkStream.prototype.read = function(length, callback) {\n    this._reads.push({\n        length: Math.abs(length),\n        allowLess: length < 0,\n        func: callback\n    });\n    process.nextTick((function() {\n        this._process();\n        // its paused and there is not enought data then ask for more\n        if (this._paused && this._reads && this._reads.length > 0) {\n            this._paused = false;\n            this.emit(\"drain\");\n        }\n    }).bind(this));\n};\nChunkStream.prototype.write = function(data, encoding) {\n    if (!this.writable) {\n        this.emit(\"error\", new Error(\"Stream not writable\"));\n        return false;\n    }\n    let dataBuffer;\n    if (Buffer.isBuffer(data)) {\n        dataBuffer = data;\n    } else {\n        dataBuffer = Buffer.from(data, encoding || this._encoding);\n    }\n    this._buffers.push(dataBuffer);\n    this._buffered += dataBuffer.length;\n    this._process();\n    // ok if there are no more read requests\n    if (this._reads && this._reads.length === 0) {\n        this._paused = true;\n    }\n    return this.writable && !this._paused;\n};\nChunkStream.prototype.end = function(data, encoding) {\n    if (data) {\n        this.write(data, encoding);\n    }\n    this.writable = false;\n    // already destroyed\n    if (!this._buffers) {\n        return;\n    }\n    // enqueue or handle end\n    if (this._buffers.length === 0) {\n        this._end();\n    } else {\n        this._buffers.push(null);\n        this._process();\n    }\n};\nChunkStream.prototype.destroySoon = ChunkStream.prototype.end;\nChunkStream.prototype._end = function() {\n    if (this._reads.length > 0) {\n        this.emit(\"error\", new Error(\"Unexpected end of input\"));\n    }\n    this.destroy();\n};\nChunkStream.prototype.destroy = function() {\n    if (!this._buffers) {\n        return;\n    }\n    this.writable = false;\n    this._reads = null;\n    this._buffers = null;\n    this.emit(\"close\");\n};\nChunkStream.prototype._processReadAllowingLess = function(read) {\n    // ok there is any data so that we can satisfy this request\n    this._reads.shift(); // == read\n    // first we need to peek into first buffer\n    let smallerBuf = this._buffers[0];\n    // ok there is more data than we need\n    if (smallerBuf.length > read.length) {\n        this._buffered -= read.length;\n        this._buffers[0] = smallerBuf.slice(read.length);\n        read.func.call(this, smallerBuf.slice(0, read.length));\n    } else {\n        // ok this is less than maximum length so use it all\n        this._buffered -= smallerBuf.length;\n        this._buffers.shift(); // == smallerBuf\n        read.func.call(this, smallerBuf);\n    }\n};\nChunkStream.prototype._processRead = function(read) {\n    this._reads.shift(); // == read\n    let pos = 0;\n    let count = 0;\n    let data = Buffer.alloc(read.length);\n    // create buffer for all data\n    while(pos < read.length){\n        let buf = this._buffers[count++];\n        let len = Math.min(buf.length, read.length - pos);\n        buf.copy(data, pos, 0, len);\n        pos += len;\n        // last buffer wasn't used all so just slice it and leave\n        if (len !== buf.length) {\n            this._buffers[--count] = buf.slice(len);\n        }\n    }\n    // remove all used buffers\n    if (count > 0) {\n        this._buffers.splice(0, count);\n    }\n    this._buffered -= read.length;\n    read.func.call(this, data);\n};\nChunkStream.prototype._process = function() {\n    try {\n        // as long as there is any data and read requests\n        while(this._buffered > 0 && this._reads && this._reads.length > 0){\n            let read = this._reads[0];\n            // read any data (but no more than length)\n            if (read.allowLess) {\n                this._processReadAllowingLess(read);\n            } else if (this._buffered >= read.length) {\n                // ok we can meet some expectations\n                this._processRead(read);\n            } else {\n                break;\n            }\n        }\n        if (this._buffers && !this.writable) {\n            this._end();\n        }\n    } catch (ex) {\n        this.emit(\"error\", ex);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/chunkstream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/constants.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/constants.js ***!
  \*********************************************/
/***/ ((module) => {

eval("\nmodule.exports = {\n    PNG_SIGNATURE: [\n        0x89,\n        0x50,\n        0x4e,\n        0x47,\n        0x0d,\n        0x0a,\n        0x1a,\n        0x0a\n    ],\n    TYPE_IHDR: 0x49484452,\n    TYPE_IEND: 0x49454e44,\n    TYPE_IDAT: 0x49444154,\n    TYPE_PLTE: 0x504c5445,\n    TYPE_tRNS: 0x74524e53,\n    TYPE_gAMA: 0x67414d41,\n    // color-type bits\n    COLORTYPE_GRAYSCALE: 0,\n    COLORTYPE_PALETTE: 1,\n    COLORTYPE_COLOR: 2,\n    COLORTYPE_ALPHA: 4,\n    // color-type combinations\n    COLORTYPE_PALETTE_COLOR: 3,\n    COLORTYPE_COLOR_ALPHA: 6,\n    COLORTYPE_TO_BPP_MAP: {\n        0: 1,\n        2: 3,\n        3: 1,\n        4: 2,\n        6: 4\n    },\n    GAMMA_DIVISION: 100000\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/crc.js":
/*!***************************************!*\
  !*** ./node_modules/pngjs/lib/crc.js ***!
  \***************************************/
/***/ ((module) => {

eval("\nlet crcTable = [];\n(function() {\n    for(let i = 0; i < 256; i++){\n        let currentCrc = i;\n        for(let j = 0; j < 8; j++){\n            if (currentCrc & 1) {\n                currentCrc = 0xedb88320 ^ currentCrc >>> 1;\n            } else {\n                currentCrc = currentCrc >>> 1;\n            }\n        }\n        crcTable[i] = currentCrc;\n    }\n})();\nlet CrcCalculator = module.exports = function() {\n    this._crc = -1;\n};\nCrcCalculator.prototype.write = function(data) {\n    for(let i = 0; i < data.length; i++){\n        this._crc = crcTable[(this._crc ^ data[i]) & 0xff] ^ this._crc >>> 8;\n    }\n    return true;\n};\nCrcCalculator.prototype.crc32 = function() {\n    return this._crc ^ -1;\n};\nCrcCalculator.crc32 = function(buf) {\n    let crc = -1;\n    for(let i = 0; i < buf.length; i++){\n        crc = crcTable[(crc ^ buf[i]) & 0xff] ^ crc >>> 8;\n    }\n    return crc ^ -1;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/crc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/filter-pack.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/filter-pack.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet paethPredictor = __webpack_require__(/*! ./paeth-predictor */ \"(ssr)/./node_modules/pngjs/lib/paeth-predictor.js\");\nfunction filterNone(pxData, pxPos, byteWidth, rawData, rawPos) {\n    for(let x = 0; x < byteWidth; x++){\n        rawData[rawPos + x] = pxData[pxPos + x];\n    }\n}\nfunction filterSumNone(pxData, pxPos, byteWidth) {\n    let sum = 0;\n    let length = pxPos + byteWidth;\n    for(let i = pxPos; i < length; i++){\n        sum += Math.abs(pxData[i]);\n    }\n    return sum;\n}\nfunction filterSub(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n    for(let x = 0; x < byteWidth; x++){\n        let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n        let val = pxData[pxPos + x] - left;\n        rawData[rawPos + x] = val;\n    }\n}\nfunction filterSumSub(pxData, pxPos, byteWidth, bpp) {\n    let sum = 0;\n    for(let x = 0; x < byteWidth; x++){\n        let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n        let val = pxData[pxPos + x] - left;\n        sum += Math.abs(val);\n    }\n    return sum;\n}\nfunction filterUp(pxData, pxPos, byteWidth, rawData, rawPos) {\n    for(let x = 0; x < byteWidth; x++){\n        let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n        let val = pxData[pxPos + x] - up;\n        rawData[rawPos + x] = val;\n    }\n}\nfunction filterSumUp(pxData, pxPos, byteWidth) {\n    let sum = 0;\n    let length = pxPos + byteWidth;\n    for(let x = pxPos; x < length; x++){\n        let up = pxPos > 0 ? pxData[x - byteWidth] : 0;\n        let val = pxData[x] - up;\n        sum += Math.abs(val);\n    }\n    return sum;\n}\nfunction filterAvg(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n    for(let x = 0; x < byteWidth; x++){\n        let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n        let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n        let val = pxData[pxPos + x] - (left + up >> 1);\n        rawData[rawPos + x] = val;\n    }\n}\nfunction filterSumAvg(pxData, pxPos, byteWidth, bpp) {\n    let sum = 0;\n    for(let x = 0; x < byteWidth; x++){\n        let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n        let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n        let val = pxData[pxPos + x] - (left + up >> 1);\n        sum += Math.abs(val);\n    }\n    return sum;\n}\nfunction filterPaeth(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n    for(let x = 0; x < byteWidth; x++){\n        let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n        let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n        let upleft = pxPos > 0 && x >= bpp ? pxData[pxPos + x - (byteWidth + bpp)] : 0;\n        let val = pxData[pxPos + x] - paethPredictor(left, up, upleft);\n        rawData[rawPos + x] = val;\n    }\n}\nfunction filterSumPaeth(pxData, pxPos, byteWidth, bpp) {\n    let sum = 0;\n    for(let x = 0; x < byteWidth; x++){\n        let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n        let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n        let upleft = pxPos > 0 && x >= bpp ? pxData[pxPos + x - (byteWidth + bpp)] : 0;\n        let val = pxData[pxPos + x] - paethPredictor(left, up, upleft);\n        sum += Math.abs(val);\n    }\n    return sum;\n}\nlet filters = {\n    0: filterNone,\n    1: filterSub,\n    2: filterUp,\n    3: filterAvg,\n    4: filterPaeth\n};\nlet filterSums = {\n    0: filterSumNone,\n    1: filterSumSub,\n    2: filterSumUp,\n    3: filterSumAvg,\n    4: filterSumPaeth\n};\nmodule.exports = function(pxData, width, height, options, bpp) {\n    let filterTypes;\n    if (!(\"filterType\" in options) || options.filterType === -1) {\n        filterTypes = [\n            0,\n            1,\n            2,\n            3,\n            4\n        ];\n    } else if (typeof options.filterType === \"number\") {\n        filterTypes = [\n            options.filterType\n        ];\n    } else {\n        throw new Error(\"unrecognised filter types\");\n    }\n    if (options.bitDepth === 16) {\n        bpp *= 2;\n    }\n    let byteWidth = width * bpp;\n    let rawPos = 0;\n    let pxPos = 0;\n    let rawData = Buffer.alloc((byteWidth + 1) * height);\n    let sel = filterTypes[0];\n    for(let y = 0; y < height; y++){\n        if (filterTypes.length > 1) {\n            // find best filter for this line (with lowest sum of values)\n            let min = Infinity;\n            for(let i = 0; i < filterTypes.length; i++){\n                let sum = filterSums[filterTypes[i]](pxData, pxPos, byteWidth, bpp);\n                if (sum < min) {\n                    sel = filterTypes[i];\n                    min = sum;\n                }\n            }\n        }\n        rawData[rawPos] = sel;\n        rawPos++;\n        filters[sel](pxData, pxPos, byteWidth, rawData, rawPos, bpp);\n        rawPos += byteWidth;\n        pxPos += byteWidth;\n    }\n    return rawData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/filter-pack.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/filter-parse-async.js":
/*!******************************************************!*\
  !*** ./node_modules/pngjs/lib/filter-parse-async.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet util = __webpack_require__(/*! util */ \"util\");\nlet ChunkStream = __webpack_require__(/*! ./chunkstream */ \"(ssr)/./node_modules/pngjs/lib/chunkstream.js\");\nlet Filter = __webpack_require__(/*! ./filter-parse */ \"(ssr)/./node_modules/pngjs/lib/filter-parse.js\");\nlet FilterAsync = module.exports = function(bitmapInfo) {\n    ChunkStream.call(this);\n    let buffers = [];\n    let that = this;\n    this._filter = new Filter(bitmapInfo, {\n        read: this.read.bind(this),\n        write: function(buffer) {\n            buffers.push(buffer);\n        },\n        complete: function() {\n            that.emit(\"complete\", Buffer.concat(buffers));\n        }\n    });\n    this._filter.start();\n};\nutil.inherits(FilterAsync, ChunkStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/filter-parse-async.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/filter-parse-sync.js":
/*!*****************************************************!*\
  !*** ./node_modules/pngjs/lib/filter-parse-sync.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nlet SyncReader = __webpack_require__(/*! ./sync-reader */ \"(ssr)/./node_modules/pngjs/lib/sync-reader.js\");\nlet Filter = __webpack_require__(/*! ./filter-parse */ \"(ssr)/./node_modules/pngjs/lib/filter-parse.js\");\nexports.process = function(inBuffer, bitmapInfo) {\n    let outBuffers = [];\n    let reader = new SyncReader(inBuffer);\n    let filter = new Filter(bitmapInfo, {\n        read: reader.read.bind(reader),\n        write: function(bufferPart) {\n            outBuffers.push(bufferPart);\n        },\n        complete: function() {}\n    });\n    filter.start();\n    reader.process();\n    return Buffer.concat(outBuffers);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2ZpbHRlci1wYXJzZS1zeW5jLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBRWIsSUFBSUEsYUFBYUMsbUJBQU9BLENBQUMsb0VBQWU7QUFDeEMsSUFBSUMsU0FBU0QsbUJBQU9BLENBQUMsc0VBQWdCO0FBRXJDRSxlQUFlLEdBQUcsU0FBVUUsUUFBUSxFQUFFQyxVQUFVO0lBQzlDLElBQUlDLGFBQWEsRUFBRTtJQUNuQixJQUFJQyxTQUFTLElBQUlSLFdBQVdLO0lBQzVCLElBQUlJLFNBQVMsSUFBSVAsT0FBT0ksWUFBWTtRQUNsQ0ksTUFBTUYsT0FBT0UsSUFBSSxDQUFDQyxJQUFJLENBQUNIO1FBQ3ZCSSxPQUFPLFNBQVVDLFVBQVU7WUFDekJOLFdBQVdPLElBQUksQ0FBQ0Q7UUFDbEI7UUFDQUUsVUFBVSxZQUFhO0lBQ3pCO0lBRUFOLE9BQU9PLEtBQUs7SUFDWlIsT0FBT0osT0FBTztJQUVkLE9BQU9hLE9BQU9DLE1BQU0sQ0FBQ1g7QUFDdkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iaGVlbWRpbmUvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2ZpbHRlci1wYXJzZS1zeW5jLmpzPzQxZGMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbmxldCBTeW5jUmVhZGVyID0gcmVxdWlyZShcIi4vc3luYy1yZWFkZXJcIik7XG5sZXQgRmlsdGVyID0gcmVxdWlyZShcIi4vZmlsdGVyLXBhcnNlXCIpO1xuXG5leHBvcnRzLnByb2Nlc3MgPSBmdW5jdGlvbiAoaW5CdWZmZXIsIGJpdG1hcEluZm8pIHtcbiAgbGV0IG91dEJ1ZmZlcnMgPSBbXTtcbiAgbGV0IHJlYWRlciA9IG5ldyBTeW5jUmVhZGVyKGluQnVmZmVyKTtcbiAgbGV0IGZpbHRlciA9IG5ldyBGaWx0ZXIoYml0bWFwSW5mbywge1xuICAgIHJlYWQ6IHJlYWRlci5yZWFkLmJpbmQocmVhZGVyKSxcbiAgICB3cml0ZTogZnVuY3Rpb24gKGJ1ZmZlclBhcnQpIHtcbiAgICAgIG91dEJ1ZmZlcnMucHVzaChidWZmZXJQYXJ0KTtcbiAgICB9LFxuICAgIGNvbXBsZXRlOiBmdW5jdGlvbiAoKSB7fSxcbiAgfSk7XG5cbiAgZmlsdGVyLnN0YXJ0KCk7XG4gIHJlYWRlci5wcm9jZXNzKCk7XG5cbiAgcmV0dXJuIEJ1ZmZlci5jb25jYXQob3V0QnVmZmVycyk7XG59O1xuIl0sIm5hbWVzIjpbIlN5bmNSZWFkZXIiLCJyZXF1aXJlIiwiRmlsdGVyIiwiZXhwb3J0cyIsInByb2Nlc3MiLCJpbkJ1ZmZlciIsImJpdG1hcEluZm8iLCJvdXRCdWZmZXJzIiwicmVhZGVyIiwiZmlsdGVyIiwicmVhZCIsImJpbmQiLCJ3cml0ZSIsImJ1ZmZlclBhcnQiLCJwdXNoIiwiY29tcGxldGUiLCJzdGFydCIsIkJ1ZmZlciIsImNvbmNhdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/filter-parse-sync.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/filter-parse.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/filter-parse.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet interlaceUtils = __webpack_require__(/*! ./interlace */ \"(ssr)/./node_modules/pngjs/lib/interlace.js\");\nlet paethPredictor = __webpack_require__(/*! ./paeth-predictor */ \"(ssr)/./node_modules/pngjs/lib/paeth-predictor.js\");\nfunction getByteWidth(width, bpp, depth) {\n    let byteWidth = width * bpp;\n    if (depth !== 8) {\n        byteWidth = Math.ceil(byteWidth / (8 / depth));\n    }\n    return byteWidth;\n}\nlet Filter = module.exports = function(bitmapInfo, dependencies) {\n    let width = bitmapInfo.width;\n    let height = bitmapInfo.height;\n    let interlace = bitmapInfo.interlace;\n    let bpp = bitmapInfo.bpp;\n    let depth = bitmapInfo.depth;\n    this.read = dependencies.read;\n    this.write = dependencies.write;\n    this.complete = dependencies.complete;\n    this._imageIndex = 0;\n    this._images = [];\n    if (interlace) {\n        let passes = interlaceUtils.getImagePasses(width, height);\n        for(let i = 0; i < passes.length; i++){\n            this._images.push({\n                byteWidth: getByteWidth(passes[i].width, bpp, depth),\n                height: passes[i].height,\n                lineIndex: 0\n            });\n        }\n    } else {\n        this._images.push({\n            byteWidth: getByteWidth(width, bpp, depth),\n            height: height,\n            lineIndex: 0\n        });\n    }\n    // when filtering the line we look at the pixel to the left\n    // the spec also says it is done on a byte level regardless of the number of pixels\n    // so if the depth is byte compatible (8 or 16) we subtract the bpp in order to compare back\n    // a pixel rather than just a different byte part. However if we are sub byte, we ignore.\n    if (depth === 8) {\n        this._xComparison = bpp;\n    } else if (depth === 16) {\n        this._xComparison = bpp * 2;\n    } else {\n        this._xComparison = 1;\n    }\n};\nFilter.prototype.start = function() {\n    this.read(this._images[this._imageIndex].byteWidth + 1, this._reverseFilterLine.bind(this));\n};\nFilter.prototype._unFilterType1 = function(rawData, unfilteredLine, byteWidth) {\n    let xComparison = this._xComparison;\n    let xBiggerThan = xComparison - 1;\n    for(let x = 0; x < byteWidth; x++){\n        let rawByte = rawData[1 + x];\n        let f1Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n        unfilteredLine[x] = rawByte + f1Left;\n    }\n};\nFilter.prototype._unFilterType2 = function(rawData, unfilteredLine, byteWidth) {\n    let lastLine = this._lastLine;\n    for(let x = 0; x < byteWidth; x++){\n        let rawByte = rawData[1 + x];\n        let f2Up = lastLine ? lastLine[x] : 0;\n        unfilteredLine[x] = rawByte + f2Up;\n    }\n};\nFilter.prototype._unFilterType3 = function(rawData, unfilteredLine, byteWidth) {\n    let xComparison = this._xComparison;\n    let xBiggerThan = xComparison - 1;\n    let lastLine = this._lastLine;\n    for(let x = 0; x < byteWidth; x++){\n        let rawByte = rawData[1 + x];\n        let f3Up = lastLine ? lastLine[x] : 0;\n        let f3Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n        let f3Add = Math.floor((f3Left + f3Up) / 2);\n        unfilteredLine[x] = rawByte + f3Add;\n    }\n};\nFilter.prototype._unFilterType4 = function(rawData, unfilteredLine, byteWidth) {\n    let xComparison = this._xComparison;\n    let xBiggerThan = xComparison - 1;\n    let lastLine = this._lastLine;\n    for(let x = 0; x < byteWidth; x++){\n        let rawByte = rawData[1 + x];\n        let f4Up = lastLine ? lastLine[x] : 0;\n        let f4Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n        let f4UpLeft = x > xBiggerThan && lastLine ? lastLine[x - xComparison] : 0;\n        let f4Add = paethPredictor(f4Left, f4Up, f4UpLeft);\n        unfilteredLine[x] = rawByte + f4Add;\n    }\n};\nFilter.prototype._reverseFilterLine = function(rawData) {\n    let filter = rawData[0];\n    let unfilteredLine;\n    let currentImage = this._images[this._imageIndex];\n    let byteWidth = currentImage.byteWidth;\n    if (filter === 0) {\n        unfilteredLine = rawData.slice(1, byteWidth + 1);\n    } else {\n        unfilteredLine = Buffer.alloc(byteWidth);\n        switch(filter){\n            case 1:\n                this._unFilterType1(rawData, unfilteredLine, byteWidth);\n                break;\n            case 2:\n                this._unFilterType2(rawData, unfilteredLine, byteWidth);\n                break;\n            case 3:\n                this._unFilterType3(rawData, unfilteredLine, byteWidth);\n                break;\n            case 4:\n                this._unFilterType4(rawData, unfilteredLine, byteWidth);\n                break;\n            default:\n                throw new Error(\"Unrecognised filter type - \" + filter);\n        }\n    }\n    this.write(unfilteredLine);\n    currentImage.lineIndex++;\n    if (currentImage.lineIndex >= currentImage.height) {\n        this._lastLine = null;\n        this._imageIndex++;\n        currentImage = this._images[this._imageIndex];\n    } else {\n        this._lastLine = unfilteredLine;\n    }\n    if (currentImage) {\n        // read, using the byte width that may be from the new current image\n        this.read(currentImage.byteWidth + 1, this._reverseFilterLine.bind(this));\n    } else {\n        this._lastLine = null;\n        this.complete();\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/filter-parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/format-normaliser.js":
/*!*****************************************************!*\
  !*** ./node_modules/pngjs/lib/format-normaliser.js ***!
  \*****************************************************/
/***/ ((module) => {

eval("\nfunction dePalette(indata, outdata, width, height, palette) {\n    let pxPos = 0;\n    // use values from palette\n    for(let y = 0; y < height; y++){\n        for(let x = 0; x < width; x++){\n            let color = palette[indata[pxPos]];\n            if (!color) {\n                throw new Error(\"index \" + indata[pxPos] + \" not in palette\");\n            }\n            for(let i = 0; i < 4; i++){\n                outdata[pxPos + i] = color[i];\n            }\n            pxPos += 4;\n        }\n    }\n}\nfunction replaceTransparentColor(indata, outdata, width, height, transColor) {\n    let pxPos = 0;\n    for(let y = 0; y < height; y++){\n        for(let x = 0; x < width; x++){\n            let makeTrans = false;\n            if (transColor.length === 1) {\n                if (transColor[0] === indata[pxPos]) {\n                    makeTrans = true;\n                }\n            } else if (transColor[0] === indata[pxPos] && transColor[1] === indata[pxPos + 1] && transColor[2] === indata[pxPos + 2]) {\n                makeTrans = true;\n            }\n            if (makeTrans) {\n                for(let i = 0; i < 4; i++){\n                    outdata[pxPos + i] = 0;\n                }\n            }\n            pxPos += 4;\n        }\n    }\n}\nfunction scaleDepth(indata, outdata, width, height, depth) {\n    let maxOutSample = 255;\n    let maxInSample = Math.pow(2, depth) - 1;\n    let pxPos = 0;\n    for(let y = 0; y < height; y++){\n        for(let x = 0; x < width; x++){\n            for(let i = 0; i < 4; i++){\n                outdata[pxPos + i] = Math.floor(indata[pxPos + i] * maxOutSample / maxInSample + 0.5);\n            }\n            pxPos += 4;\n        }\n    }\n}\nmodule.exports = function(indata, imageData) {\n    let depth = imageData.depth;\n    let width = imageData.width;\n    let height = imageData.height;\n    let colorType = imageData.colorType;\n    let transColor = imageData.transColor;\n    let palette = imageData.palette;\n    let outdata = indata; // only different for 16 bits\n    if (colorType === 3) {\n        // paletted\n        dePalette(indata, outdata, width, height, palette);\n    } else {\n        if (transColor) {\n            replaceTransparentColor(indata, outdata, width, height, transColor);\n        }\n        // if it needs scaling\n        if (depth !== 8) {\n            // if we need to change the buffer size\n            if (depth === 16) {\n                outdata = Buffer.alloc(width * height * 4);\n            }\n            scaleDepth(indata, outdata, width, height, depth);\n        }\n    }\n    return outdata;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/format-normaliser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/interlace.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/interlace.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Adam 7\n//   0 1 2 3 4 5 6 7\n// 0 x 6 4 6 x 6 4 6\n// 1 7 7 7 7 7 7 7 7\n// 2 5 6 5 6 5 6 5 6\n// 3 7 7 7 7 7 7 7 7\n// 4 3 6 4 6 3 6 4 6\n// 5 7 7 7 7 7 7 7 7\n// 6 5 6 5 6 5 6 5 6\n// 7 7 7 7 7 7 7 7 7\nlet imagePasses = [\n    {\n        // pass 1 - 1px\n        x: [\n            0\n        ],\n        y: [\n            0\n        ]\n    },\n    {\n        // pass 2 - 1px\n        x: [\n            4\n        ],\n        y: [\n            0\n        ]\n    },\n    {\n        // pass 3 - 2px\n        x: [\n            0,\n            4\n        ],\n        y: [\n            4\n        ]\n    },\n    {\n        // pass 4 - 4px\n        x: [\n            2,\n            6\n        ],\n        y: [\n            0,\n            4\n        ]\n    },\n    {\n        // pass 5 - 8px\n        x: [\n            0,\n            2,\n            4,\n            6\n        ],\n        y: [\n            2,\n            6\n        ]\n    },\n    {\n        // pass 6 - 16px\n        x: [\n            1,\n            3,\n            5,\n            7\n        ],\n        y: [\n            0,\n            2,\n            4,\n            6\n        ]\n    },\n    {\n        // pass 7 - 32px\n        x: [\n            0,\n            1,\n            2,\n            3,\n            4,\n            5,\n            6,\n            7\n        ],\n        y: [\n            1,\n            3,\n            5,\n            7\n        ]\n    }\n];\nexports.getImagePasses = function(width, height) {\n    let images = [];\n    let xLeftOver = width % 8;\n    let yLeftOver = height % 8;\n    let xRepeats = (width - xLeftOver) / 8;\n    let yRepeats = (height - yLeftOver) / 8;\n    for(let i = 0; i < imagePasses.length; i++){\n        let pass = imagePasses[i];\n        let passWidth = xRepeats * pass.x.length;\n        let passHeight = yRepeats * pass.y.length;\n        for(let j = 0; j < pass.x.length; j++){\n            if (pass.x[j] < xLeftOver) {\n                passWidth++;\n            } else {\n                break;\n            }\n        }\n        for(let j = 0; j < pass.y.length; j++){\n            if (pass.y[j] < yLeftOver) {\n                passHeight++;\n            } else {\n                break;\n            }\n        }\n        if (passWidth > 0 && passHeight > 0) {\n            images.push({\n                width: passWidth,\n                height: passHeight,\n                index: i\n            });\n        }\n    }\n    return images;\n};\nexports.getInterlaceIterator = function(width) {\n    return function(x, y, pass) {\n        let outerXLeftOver = x % imagePasses[pass].x.length;\n        let outerX = (x - outerXLeftOver) / imagePasses[pass].x.length * 8 + imagePasses[pass].x[outerXLeftOver];\n        let outerYLeftOver = y % imagePasses[pass].y.length;\n        let outerY = (y - outerYLeftOver) / imagePasses[pass].y.length * 8 + imagePasses[pass].y[outerYLeftOver];\n        return outerX * 4 + outerY * width * 4;\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/interlace.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/packer-async.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/packer-async.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet util = __webpack_require__(/*! util */ \"util\");\nlet Stream = __webpack_require__(/*! stream */ \"stream\");\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\nlet Packer = __webpack_require__(/*! ./packer */ \"(ssr)/./node_modules/pngjs/lib/packer.js\");\nlet PackerAsync = module.exports = function(opt) {\n    Stream.call(this);\n    let options = opt || {};\n    this._packer = new Packer(options);\n    this._deflate = this._packer.createDeflate();\n    this.readable = true;\n};\nutil.inherits(PackerAsync, Stream);\nPackerAsync.prototype.pack = function(data, width, height, gamma) {\n    // Signature\n    this.emit(\"data\", Buffer.from(constants.PNG_SIGNATURE));\n    this.emit(\"data\", this._packer.packIHDR(width, height));\n    if (gamma) {\n        this.emit(\"data\", this._packer.packGAMA(gamma));\n    }\n    let filteredData = this._packer.filterData(data, width, height);\n    // compress it\n    this._deflate.on(\"error\", this.emit.bind(this, \"error\"));\n    this._deflate.on(\"data\", (function(compressedData) {\n        this.emit(\"data\", this._packer.packIDAT(compressedData));\n    }).bind(this));\n    this._deflate.on(\"end\", (function() {\n        this.emit(\"data\", this._packer.packIEND());\n        this.emit(\"end\");\n    }).bind(this));\n    this._deflate.end(filteredData);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/packer-async.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/packer-sync.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/packer-sync.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet hasSyncZlib = true;\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nif (!zlib.deflateSync) {\n    hasSyncZlib = false;\n}\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\nlet Packer = __webpack_require__(/*! ./packer */ \"(ssr)/./node_modules/pngjs/lib/packer.js\");\nmodule.exports = function(metaData, opt) {\n    if (!hasSyncZlib) {\n        throw new Error(\"To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0\");\n    }\n    let options = opt || {};\n    let packer = new Packer(options);\n    let chunks = [];\n    // Signature\n    chunks.push(Buffer.from(constants.PNG_SIGNATURE));\n    // Header\n    chunks.push(packer.packIHDR(metaData.width, metaData.height));\n    if (metaData.gamma) {\n        chunks.push(packer.packGAMA(metaData.gamma));\n    }\n    let filteredData = packer.filterData(metaData.data, metaData.width, metaData.height);\n    // compress it\n    let compressedData = zlib.deflateSync(filteredData, packer.getDeflateOptions());\n    filteredData = null;\n    if (!compressedData || !compressedData.length) {\n        throw new Error(\"bad png - invalid compressed data response\");\n    }\n    chunks.push(packer.packIDAT(compressedData));\n    // End\n    chunks.push(packer.packIEND());\n    return Buffer.concat(chunks);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/packer-sync.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/packer.js":
/*!******************************************!*\
  !*** ./node_modules/pngjs/lib/packer.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\nlet CrcStream = __webpack_require__(/*! ./crc */ \"(ssr)/./node_modules/pngjs/lib/crc.js\");\nlet bitPacker = __webpack_require__(/*! ./bitpacker */ \"(ssr)/./node_modules/pngjs/lib/bitpacker.js\");\nlet filter = __webpack_require__(/*! ./filter-pack */ \"(ssr)/./node_modules/pngjs/lib/filter-pack.js\");\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nlet Packer = module.exports = function(options) {\n    this._options = options;\n    options.deflateChunkSize = options.deflateChunkSize || 32 * 1024;\n    options.deflateLevel = options.deflateLevel != null ? options.deflateLevel : 9;\n    options.deflateStrategy = options.deflateStrategy != null ? options.deflateStrategy : 3;\n    options.inputHasAlpha = options.inputHasAlpha != null ? options.inputHasAlpha : true;\n    options.deflateFactory = options.deflateFactory || zlib.createDeflate;\n    options.bitDepth = options.bitDepth || 8;\n    // This is outputColorType\n    options.colorType = typeof options.colorType === \"number\" ? options.colorType : constants.COLORTYPE_COLOR_ALPHA;\n    options.inputColorType = typeof options.inputColorType === \"number\" ? options.inputColorType : constants.COLORTYPE_COLOR_ALPHA;\n    if ([\n        constants.COLORTYPE_GRAYSCALE,\n        constants.COLORTYPE_COLOR,\n        constants.COLORTYPE_COLOR_ALPHA,\n        constants.COLORTYPE_ALPHA\n    ].indexOf(options.colorType) === -1) {\n        throw new Error(\"option color type:\" + options.colorType + \" is not supported at present\");\n    }\n    if ([\n        constants.COLORTYPE_GRAYSCALE,\n        constants.COLORTYPE_COLOR,\n        constants.COLORTYPE_COLOR_ALPHA,\n        constants.COLORTYPE_ALPHA\n    ].indexOf(options.inputColorType) === -1) {\n        throw new Error(\"option input color type:\" + options.inputColorType + \" is not supported at present\");\n    }\n    if (options.bitDepth !== 8 && options.bitDepth !== 16) {\n        throw new Error(\"option bit depth:\" + options.bitDepth + \" is not supported at present\");\n    }\n};\nPacker.prototype.getDeflateOptions = function() {\n    return {\n        chunkSize: this._options.deflateChunkSize,\n        level: this._options.deflateLevel,\n        strategy: this._options.deflateStrategy\n    };\n};\nPacker.prototype.createDeflate = function() {\n    return this._options.deflateFactory(this.getDeflateOptions());\n};\nPacker.prototype.filterData = function(data, width, height) {\n    // convert to correct format for filtering (e.g. right bpp and bit depth)\n    let packedData = bitPacker(data, width, height, this._options);\n    // filter pixel data\n    let bpp = constants.COLORTYPE_TO_BPP_MAP[this._options.colorType];\n    let filteredData = filter(packedData, width, height, this._options, bpp);\n    return filteredData;\n};\nPacker.prototype._packChunk = function(type, data) {\n    let len = data ? data.length : 0;\n    let buf = Buffer.alloc(len + 12);\n    buf.writeUInt32BE(len, 0);\n    buf.writeUInt32BE(type, 4);\n    if (data) {\n        data.copy(buf, 8);\n    }\n    buf.writeInt32BE(CrcStream.crc32(buf.slice(4, buf.length - 4)), buf.length - 4);\n    return buf;\n};\nPacker.prototype.packGAMA = function(gamma) {\n    let buf = Buffer.alloc(4);\n    buf.writeUInt32BE(Math.floor(gamma * constants.GAMMA_DIVISION), 0);\n    return this._packChunk(constants.TYPE_gAMA, buf);\n};\nPacker.prototype.packIHDR = function(width, height) {\n    let buf = Buffer.alloc(13);\n    buf.writeUInt32BE(width, 0);\n    buf.writeUInt32BE(height, 4);\n    buf[8] = this._options.bitDepth; // Bit depth\n    buf[9] = this._options.colorType; // colorType\n    buf[10] = 0; // compression\n    buf[11] = 0; // filter\n    buf[12] = 0; // interlace\n    return this._packChunk(constants.TYPE_IHDR, buf);\n};\nPacker.prototype.packIDAT = function(data) {\n    return this._packChunk(constants.TYPE_IDAT, data);\n};\nPacker.prototype.packIEND = function() {\n    return this._packChunk(constants.TYPE_IEND, null);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/packer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/paeth-predictor.js":
/*!***************************************************!*\
  !*** ./node_modules/pngjs/lib/paeth-predictor.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\nmodule.exports = function paethPredictor(left, above, upLeft) {\n    let paeth = left + above - upLeft;\n    let pLeft = Math.abs(paeth - left);\n    let pAbove = Math.abs(paeth - above);\n    let pUpLeft = Math.abs(paeth - upLeft);\n    if (pLeft <= pAbove && pLeft <= pUpLeft) {\n        return left;\n    }\n    if (pAbove <= pUpLeft) {\n        return above;\n    }\n    return upLeft;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL3BhZXRoLXByZWRpY3Rvci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUViQSxPQUFPQyxPQUFPLEdBQUcsU0FBU0MsZUFBZUMsSUFBSSxFQUFFQyxLQUFLLEVBQUVDLE1BQU07SUFDMUQsSUFBSUMsUUFBUUgsT0FBT0MsUUFBUUM7SUFDM0IsSUFBSUUsUUFBUUMsS0FBS0MsR0FBRyxDQUFDSCxRQUFRSDtJQUM3QixJQUFJTyxTQUFTRixLQUFLQyxHQUFHLENBQUNILFFBQVFGO0lBQzlCLElBQUlPLFVBQVVILEtBQUtDLEdBQUcsQ0FBQ0gsUUFBUUQ7SUFFL0IsSUFBSUUsU0FBU0csVUFBVUgsU0FBU0ksU0FBUztRQUN2QyxPQUFPUjtJQUNUO0lBQ0EsSUFBSU8sVUFBVUMsU0FBUztRQUNyQixPQUFPUDtJQUNUO0lBQ0EsT0FBT0M7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2JoZWVtZGluZS8uL25vZGVfbW9kdWxlcy9wbmdqcy9saWIvcGFldGgtcHJlZGljdG9yLmpzP2Y0YzEiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gcGFldGhQcmVkaWN0b3IobGVmdCwgYWJvdmUsIHVwTGVmdCkge1xuICBsZXQgcGFldGggPSBsZWZ0ICsgYWJvdmUgLSB1cExlZnQ7XG4gIGxldCBwTGVmdCA9IE1hdGguYWJzKHBhZXRoIC0gbGVmdCk7XG4gIGxldCBwQWJvdmUgPSBNYXRoLmFicyhwYWV0aCAtIGFib3ZlKTtcbiAgbGV0IHBVcExlZnQgPSBNYXRoLmFicyhwYWV0aCAtIHVwTGVmdCk7XG5cbiAgaWYgKHBMZWZ0IDw9IHBBYm92ZSAmJiBwTGVmdCA8PSBwVXBMZWZ0KSB7XG4gICAgcmV0dXJuIGxlZnQ7XG4gIH1cbiAgaWYgKHBBYm92ZSA8PSBwVXBMZWZ0KSB7XG4gICAgcmV0dXJuIGFib3ZlO1xuICB9XG4gIHJldHVybiB1cExlZnQ7XG59O1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJwYWV0aFByZWRpY3RvciIsImxlZnQiLCJhYm92ZSIsInVwTGVmdCIsInBhZXRoIiwicExlZnQiLCJNYXRoIiwiYWJzIiwicEFib3ZlIiwicFVwTGVmdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/paeth-predictor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/parser-async.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/parser-async.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet util = __webpack_require__(/*! util */ \"util\");\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nlet ChunkStream = __webpack_require__(/*! ./chunkstream */ \"(ssr)/./node_modules/pngjs/lib/chunkstream.js\");\nlet FilterAsync = __webpack_require__(/*! ./filter-parse-async */ \"(ssr)/./node_modules/pngjs/lib/filter-parse-async.js\");\nlet Parser = __webpack_require__(/*! ./parser */ \"(ssr)/./node_modules/pngjs/lib/parser.js\");\nlet bitmapper = __webpack_require__(/*! ./bitmapper */ \"(ssr)/./node_modules/pngjs/lib/bitmapper.js\");\nlet formatNormaliser = __webpack_require__(/*! ./format-normaliser */ \"(ssr)/./node_modules/pngjs/lib/format-normaliser.js\");\nlet ParserAsync = module.exports = function(options) {\n    ChunkStream.call(this);\n    this._parser = new Parser(options, {\n        read: this.read.bind(this),\n        error: this._handleError.bind(this),\n        metadata: this._handleMetaData.bind(this),\n        gamma: this.emit.bind(this, \"gamma\"),\n        palette: this._handlePalette.bind(this),\n        transColor: this._handleTransColor.bind(this),\n        finished: this._finished.bind(this),\n        inflateData: this._inflateData.bind(this),\n        simpleTransparency: this._simpleTransparency.bind(this),\n        headersFinished: this._headersFinished.bind(this)\n    });\n    this._options = options;\n    this.writable = true;\n    this._parser.start();\n};\nutil.inherits(ParserAsync, ChunkStream);\nParserAsync.prototype._handleError = function(err) {\n    this.emit(\"error\", err);\n    this.writable = false;\n    this.destroy();\n    if (this._inflate && this._inflate.destroy) {\n        this._inflate.destroy();\n    }\n    if (this._filter) {\n        this._filter.destroy();\n        // For backward compatibility with Node 7 and below.\n        // Suppress errors due to _inflate calling write() even after\n        // it's destroy()'ed.\n        this._filter.on(\"error\", function() {});\n    }\n    this.errord = true;\n};\nParserAsync.prototype._inflateData = function(data) {\n    if (!this._inflate) {\n        if (this._bitmapInfo.interlace) {\n            this._inflate = zlib.createInflate();\n            this._inflate.on(\"error\", this.emit.bind(this, \"error\"));\n            this._filter.on(\"complete\", this._complete.bind(this));\n            this._inflate.pipe(this._filter);\n        } else {\n            let rowSize = (this._bitmapInfo.width * this._bitmapInfo.bpp * this._bitmapInfo.depth + 7 >> 3) + 1;\n            let imageSize = rowSize * this._bitmapInfo.height;\n            let chunkSize = Math.max(imageSize, zlib.Z_MIN_CHUNK);\n            this._inflate = zlib.createInflate({\n                chunkSize: chunkSize\n            });\n            let leftToInflate = imageSize;\n            let emitError = this.emit.bind(this, \"error\");\n            this._inflate.on(\"error\", function(err) {\n                if (!leftToInflate) {\n                    return;\n                }\n                emitError(err);\n            });\n            this._filter.on(\"complete\", this._complete.bind(this));\n            let filterWrite = this._filter.write.bind(this._filter);\n            this._inflate.on(\"data\", function(chunk) {\n                if (!leftToInflate) {\n                    return;\n                }\n                if (chunk.length > leftToInflate) {\n                    chunk = chunk.slice(0, leftToInflate);\n                }\n                leftToInflate -= chunk.length;\n                filterWrite(chunk);\n            });\n            this._inflate.on(\"end\", this._filter.end.bind(this._filter));\n        }\n    }\n    this._inflate.write(data);\n};\nParserAsync.prototype._handleMetaData = function(metaData) {\n    this._metaData = metaData;\n    this._bitmapInfo = Object.create(metaData);\n    this._filter = new FilterAsync(this._bitmapInfo);\n};\nParserAsync.prototype._handleTransColor = function(transColor) {\n    this._bitmapInfo.transColor = transColor;\n};\nParserAsync.prototype._handlePalette = function(palette) {\n    this._bitmapInfo.palette = palette;\n};\nParserAsync.prototype._simpleTransparency = function() {\n    this._metaData.alpha = true;\n};\nParserAsync.prototype._headersFinished = function() {\n    // Up until this point, we don't know if we have a tRNS chunk (alpha)\n    // so we can't emit metadata any earlier\n    this.emit(\"metadata\", this._metaData);\n};\nParserAsync.prototype._finished = function() {\n    if (this.errord) {\n        return;\n    }\n    if (!this._inflate) {\n        this.emit(\"error\", \"No Inflate block\");\n    } else {\n        // no more data to inflate\n        this._inflate.end();\n    }\n};\nParserAsync.prototype._complete = function(filteredData) {\n    if (this.errord) {\n        return;\n    }\n    let normalisedBitmapData;\n    try {\n        let bitmapData = bitmapper.dataToBitMap(filteredData, this._bitmapInfo);\n        normalisedBitmapData = formatNormaliser(bitmapData, this._bitmapInfo);\n        bitmapData = null;\n    } catch (ex) {\n        this._handleError(ex);\n        return;\n    }\n    this.emit(\"parsed\", normalisedBitmapData);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/parser-async.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/parser-sync.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/parser-sync.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet hasSyncZlib = true;\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nlet inflateSync = __webpack_require__(/*! ./sync-inflate */ \"(ssr)/./node_modules/pngjs/lib/sync-inflate.js\");\nif (!zlib.deflateSync) {\n    hasSyncZlib = false;\n}\nlet SyncReader = __webpack_require__(/*! ./sync-reader */ \"(ssr)/./node_modules/pngjs/lib/sync-reader.js\");\nlet FilterSync = __webpack_require__(/*! ./filter-parse-sync */ \"(ssr)/./node_modules/pngjs/lib/filter-parse-sync.js\");\nlet Parser = __webpack_require__(/*! ./parser */ \"(ssr)/./node_modules/pngjs/lib/parser.js\");\nlet bitmapper = __webpack_require__(/*! ./bitmapper */ \"(ssr)/./node_modules/pngjs/lib/bitmapper.js\");\nlet formatNormaliser = __webpack_require__(/*! ./format-normaliser */ \"(ssr)/./node_modules/pngjs/lib/format-normaliser.js\");\nmodule.exports = function(buffer, options) {\n    if (!hasSyncZlib) {\n        throw new Error(\"To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0\");\n    }\n    let err;\n    function handleError(_err_) {\n        err = _err_;\n    }\n    let metaData;\n    function handleMetaData(_metaData_) {\n        metaData = _metaData_;\n    }\n    function handleTransColor(transColor) {\n        metaData.transColor = transColor;\n    }\n    function handlePalette(palette) {\n        metaData.palette = palette;\n    }\n    function handleSimpleTransparency() {\n        metaData.alpha = true;\n    }\n    let gamma;\n    function handleGamma(_gamma_) {\n        gamma = _gamma_;\n    }\n    let inflateDataList = [];\n    function handleInflateData(inflatedData) {\n        inflateDataList.push(inflatedData);\n    }\n    let reader = new SyncReader(buffer);\n    let parser = new Parser(options, {\n        read: reader.read.bind(reader),\n        error: handleError,\n        metadata: handleMetaData,\n        gamma: handleGamma,\n        palette: handlePalette,\n        transColor: handleTransColor,\n        inflateData: handleInflateData,\n        simpleTransparency: handleSimpleTransparency\n    });\n    parser.start();\n    reader.process();\n    if (err) {\n        throw err;\n    }\n    //join together the inflate datas\n    let inflateData = Buffer.concat(inflateDataList);\n    inflateDataList.length = 0;\n    let inflatedData;\n    if (metaData.interlace) {\n        inflatedData = zlib.inflateSync(inflateData);\n    } else {\n        let rowSize = (metaData.width * metaData.bpp * metaData.depth + 7 >> 3) + 1;\n        let imageSize = rowSize * metaData.height;\n        inflatedData = inflateSync(inflateData, {\n            chunkSize: imageSize,\n            maxLength: imageSize\n        });\n    }\n    inflateData = null;\n    if (!inflatedData || !inflatedData.length) {\n        throw new Error(\"bad png - invalid inflate data response\");\n    }\n    let unfilteredData = FilterSync.process(inflatedData, metaData);\n    inflateData = null;\n    let bitmapData = bitmapper.dataToBitMap(unfilteredData, metaData);\n    unfilteredData = null;\n    let normalisedBitmapData = formatNormaliser(bitmapData, metaData);\n    metaData.data = normalisedBitmapData;\n    metaData.gamma = gamma || 0;\n    return metaData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/parser-sync.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/parser.js":
/*!******************************************!*\
  !*** ./node_modules/pngjs/lib/parser.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\nlet CrcCalculator = __webpack_require__(/*! ./crc */ \"(ssr)/./node_modules/pngjs/lib/crc.js\");\nlet Parser = module.exports = function(options, dependencies) {\n    this._options = options;\n    options.checkCRC = options.checkCRC !== false;\n    this._hasIHDR = false;\n    this._hasIEND = false;\n    this._emittedHeadersFinished = false;\n    // input flags/metadata\n    this._palette = [];\n    this._colorType = 0;\n    this._chunks = {};\n    this._chunks[constants.TYPE_IHDR] = this._handleIHDR.bind(this);\n    this._chunks[constants.TYPE_IEND] = this._handleIEND.bind(this);\n    this._chunks[constants.TYPE_IDAT] = this._handleIDAT.bind(this);\n    this._chunks[constants.TYPE_PLTE] = this._handlePLTE.bind(this);\n    this._chunks[constants.TYPE_tRNS] = this._handleTRNS.bind(this);\n    this._chunks[constants.TYPE_gAMA] = this._handleGAMA.bind(this);\n    this.read = dependencies.read;\n    this.error = dependencies.error;\n    this.metadata = dependencies.metadata;\n    this.gamma = dependencies.gamma;\n    this.transColor = dependencies.transColor;\n    this.palette = dependencies.palette;\n    this.parsed = dependencies.parsed;\n    this.inflateData = dependencies.inflateData;\n    this.finished = dependencies.finished;\n    this.simpleTransparency = dependencies.simpleTransparency;\n    this.headersFinished = dependencies.headersFinished || function() {};\n};\nParser.prototype.start = function() {\n    this.read(constants.PNG_SIGNATURE.length, this._parseSignature.bind(this));\n};\nParser.prototype._parseSignature = function(data) {\n    let signature = constants.PNG_SIGNATURE;\n    for(let i = 0; i < signature.length; i++){\n        if (data[i] !== signature[i]) {\n            this.error(new Error(\"Invalid file signature\"));\n            return;\n        }\n    }\n    this.read(8, this._parseChunkBegin.bind(this));\n};\nParser.prototype._parseChunkBegin = function(data) {\n    // chunk content length\n    let length = data.readUInt32BE(0);\n    // chunk type\n    let type = data.readUInt32BE(4);\n    let name = \"\";\n    for(let i = 4; i < 8; i++){\n        name += String.fromCharCode(data[i]);\n    }\n    //console.log('chunk ', name, length);\n    // chunk flags\n    let ancillary = Boolean(data[4] & 0x20); // or critical\n    //    priv = Boolean(data[5] & 0x20), // or public\n    //    safeToCopy = Boolean(data[7] & 0x20); // or unsafe\n    if (!this._hasIHDR && type !== constants.TYPE_IHDR) {\n        this.error(new Error(\"Expected IHDR on beggining\"));\n        return;\n    }\n    this._crc = new CrcCalculator();\n    this._crc.write(Buffer.from(name));\n    if (this._chunks[type]) {\n        return this._chunks[type](length);\n    }\n    if (!ancillary) {\n        this.error(new Error(\"Unsupported critical chunk type \" + name));\n        return;\n    }\n    this.read(length + 4, this._skipChunk.bind(this));\n};\nParser.prototype._skipChunk = function() {\n    this.read(8, this._parseChunkBegin.bind(this));\n};\nParser.prototype._handleChunkEnd = function() {\n    this.read(4, this._parseChunkEnd.bind(this));\n};\nParser.prototype._parseChunkEnd = function(data) {\n    let fileCrc = data.readInt32BE(0);\n    let calcCrc = this._crc.crc32();\n    // check CRC\n    if (this._options.checkCRC && calcCrc !== fileCrc) {\n        this.error(new Error(\"Crc error - \" + fileCrc + \" - \" + calcCrc));\n        return;\n    }\n    if (!this._hasIEND) {\n        this.read(8, this._parseChunkBegin.bind(this));\n    }\n};\nParser.prototype._handleIHDR = function(length) {\n    this.read(length, this._parseIHDR.bind(this));\n};\nParser.prototype._parseIHDR = function(data) {\n    this._crc.write(data);\n    let width = data.readUInt32BE(0);\n    let height = data.readUInt32BE(4);\n    let depth = data[8];\n    let colorType = data[9]; // bits: 1 palette, 2 color, 4 alpha\n    let compr = data[10];\n    let filter = data[11];\n    let interlace = data[12];\n    // console.log('    width', width, 'height', height,\n    //     'depth', depth, 'colorType', colorType,\n    //     'compr', compr, 'filter', filter, 'interlace', interlace\n    // );\n    if (depth !== 8 && depth !== 4 && depth !== 2 && depth !== 1 && depth !== 16) {\n        this.error(new Error(\"Unsupported bit depth \" + depth));\n        return;\n    }\n    if (!(colorType in constants.COLORTYPE_TO_BPP_MAP)) {\n        this.error(new Error(\"Unsupported color type\"));\n        return;\n    }\n    if (compr !== 0) {\n        this.error(new Error(\"Unsupported compression method\"));\n        return;\n    }\n    if (filter !== 0) {\n        this.error(new Error(\"Unsupported filter method\"));\n        return;\n    }\n    if (interlace !== 0 && interlace !== 1) {\n        this.error(new Error(\"Unsupported interlace method\"));\n        return;\n    }\n    this._colorType = colorType;\n    let bpp = constants.COLORTYPE_TO_BPP_MAP[this._colorType];\n    this._hasIHDR = true;\n    this.metadata({\n        width: width,\n        height: height,\n        depth: depth,\n        interlace: Boolean(interlace),\n        palette: Boolean(colorType & constants.COLORTYPE_PALETTE),\n        color: Boolean(colorType & constants.COLORTYPE_COLOR),\n        alpha: Boolean(colorType & constants.COLORTYPE_ALPHA),\n        bpp: bpp,\n        colorType: colorType\n    });\n    this._handleChunkEnd();\n};\nParser.prototype._handlePLTE = function(length) {\n    this.read(length, this._parsePLTE.bind(this));\n};\nParser.prototype._parsePLTE = function(data) {\n    this._crc.write(data);\n    let entries = Math.floor(data.length / 3);\n    // console.log('Palette:', entries);\n    for(let i = 0; i < entries; i++){\n        this._palette.push([\n            data[i * 3],\n            data[i * 3 + 1],\n            data[i * 3 + 2],\n            0xff\n        ]);\n    }\n    this.palette(this._palette);\n    this._handleChunkEnd();\n};\nParser.prototype._handleTRNS = function(length) {\n    this.simpleTransparency();\n    this.read(length, this._parseTRNS.bind(this));\n};\nParser.prototype._parseTRNS = function(data) {\n    this._crc.write(data);\n    // palette\n    if (this._colorType === constants.COLORTYPE_PALETTE_COLOR) {\n        if (this._palette.length === 0) {\n            this.error(new Error(\"Transparency chunk must be after palette\"));\n            return;\n        }\n        if (data.length > this._palette.length) {\n            this.error(new Error(\"More transparent colors than palette size\"));\n            return;\n        }\n        for(let i = 0; i < data.length; i++){\n            this._palette[i][3] = data[i];\n        }\n        this.palette(this._palette);\n    }\n    // for colorType 0 (grayscale) and 2 (rgb)\n    // there might be one gray/color defined as transparent\n    if (this._colorType === constants.COLORTYPE_GRAYSCALE) {\n        // grey, 2 bytes\n        this.transColor([\n            data.readUInt16BE(0)\n        ]);\n    }\n    if (this._colorType === constants.COLORTYPE_COLOR) {\n        this.transColor([\n            data.readUInt16BE(0),\n            data.readUInt16BE(2),\n            data.readUInt16BE(4)\n        ]);\n    }\n    this._handleChunkEnd();\n};\nParser.prototype._handleGAMA = function(length) {\n    this.read(length, this._parseGAMA.bind(this));\n};\nParser.prototype._parseGAMA = function(data) {\n    this._crc.write(data);\n    this.gamma(data.readUInt32BE(0) / constants.GAMMA_DIVISION);\n    this._handleChunkEnd();\n};\nParser.prototype._handleIDAT = function(length) {\n    if (!this._emittedHeadersFinished) {\n        this._emittedHeadersFinished = true;\n        this.headersFinished();\n    }\n    this.read(-length, this._parseIDAT.bind(this, length));\n};\nParser.prototype._parseIDAT = function(length, data) {\n    this._crc.write(data);\n    if (this._colorType === constants.COLORTYPE_PALETTE_COLOR && this._palette.length === 0) {\n        throw new Error(\"Expected palette not found\");\n    }\n    this.inflateData(data);\n    let leftOverLength = length - data.length;\n    if (leftOverLength > 0) {\n        this._handleIDAT(leftOverLength);\n    } else {\n        this._handleChunkEnd();\n    }\n};\nParser.prototype._handleIEND = function(length) {\n    this.read(length, this._parseIEND.bind(this));\n};\nParser.prototype._parseIEND = function(data) {\n    this._crc.write(data);\n    this._hasIEND = true;\n    this._handleChunkEnd();\n    if (this.finished) {\n        this.finished();\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/png-sync.js":
/*!********************************************!*\
  !*** ./node_modules/pngjs/lib/png-sync.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nlet parse = __webpack_require__(/*! ./parser-sync */ \"(ssr)/./node_modules/pngjs/lib/parser-sync.js\");\nlet pack = __webpack_require__(/*! ./packer-sync */ \"(ssr)/./node_modules/pngjs/lib/packer-sync.js\");\nexports.read = function(buffer, options) {\n    return parse(buffer, options || {});\n};\nexports.write = function(png, options) {\n    return pack(png, options);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL3BuZy1zeW5jLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBRWIsSUFBSUEsUUFBUUMsbUJBQU9BLENBQUMsb0VBQWU7QUFDbkMsSUFBSUMsT0FBT0QsbUJBQU9BLENBQUMsb0VBQWU7QUFFbENFLFlBQVksR0FBRyxTQUFVRSxNQUFNLEVBQUVDLE9BQU87SUFDdEMsT0FBT04sTUFBTUssUUFBUUMsV0FBVyxDQUFDO0FBQ25DO0FBRUFILGFBQWEsR0FBRyxTQUFVSyxHQUFHLEVBQUVGLE9BQU87SUFDcEMsT0FBT0osS0FBS00sS0FBS0Y7QUFDbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iaGVlbWRpbmUvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL3BuZy1zeW5jLmpzPzhhZWQiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbmxldCBwYXJzZSA9IHJlcXVpcmUoXCIuL3BhcnNlci1zeW5jXCIpO1xubGV0IHBhY2sgPSByZXF1aXJlKFwiLi9wYWNrZXItc3luY1wiKTtcblxuZXhwb3J0cy5yZWFkID0gZnVuY3Rpb24gKGJ1ZmZlciwgb3B0aW9ucykge1xuICByZXR1cm4gcGFyc2UoYnVmZmVyLCBvcHRpb25zIHx8IHt9KTtcbn07XG5cbmV4cG9ydHMud3JpdGUgPSBmdW5jdGlvbiAocG5nLCBvcHRpb25zKSB7XG4gIHJldHVybiBwYWNrKHBuZywgb3B0aW9ucyk7XG59O1xuIl0sIm5hbWVzIjpbInBhcnNlIiwicmVxdWlyZSIsInBhY2siLCJleHBvcnRzIiwicmVhZCIsImJ1ZmZlciIsIm9wdGlvbnMiLCJ3cml0ZSIsInBuZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/png-sync.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/png.js":
/*!***************************************!*\
  !*** ./node_modules/pngjs/lib/png.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nlet util = __webpack_require__(/*! util */ \"util\");\nlet Stream = __webpack_require__(/*! stream */ \"stream\");\nlet Parser = __webpack_require__(/*! ./parser-async */ \"(ssr)/./node_modules/pngjs/lib/parser-async.js\");\nlet Packer = __webpack_require__(/*! ./packer-async */ \"(ssr)/./node_modules/pngjs/lib/packer-async.js\");\nlet PNGSync = __webpack_require__(/*! ./png-sync */ \"(ssr)/./node_modules/pngjs/lib/png-sync.js\");\nlet PNG = exports.PNG = function(options) {\n    Stream.call(this);\n    options = options || {}; // eslint-disable-line no-param-reassign\n    // coerce pixel dimensions to integers (also coerces undefined -> 0):\n    this.width = options.width | 0;\n    this.height = options.height | 0;\n    this.data = this.width > 0 && this.height > 0 ? Buffer.alloc(4 * this.width * this.height) : null;\n    if (options.fill && this.data) {\n        this.data.fill(0);\n    }\n    this.gamma = 0;\n    this.readable = this.writable = true;\n    this._parser = new Parser(options);\n    this._parser.on(\"error\", this.emit.bind(this, \"error\"));\n    this._parser.on(\"close\", this._handleClose.bind(this));\n    this._parser.on(\"metadata\", this._metadata.bind(this));\n    this._parser.on(\"gamma\", this._gamma.bind(this));\n    this._parser.on(\"parsed\", (function(data) {\n        this.data = data;\n        this.emit(\"parsed\", data);\n    }).bind(this));\n    this._packer = new Packer(options);\n    this._packer.on(\"data\", this.emit.bind(this, \"data\"));\n    this._packer.on(\"end\", this.emit.bind(this, \"end\"));\n    this._parser.on(\"close\", this._handleClose.bind(this));\n    this._packer.on(\"error\", this.emit.bind(this, \"error\"));\n};\nutil.inherits(PNG, Stream);\nPNG.sync = PNGSync;\nPNG.prototype.pack = function() {\n    if (!this.data || !this.data.length) {\n        this.emit(\"error\", \"No data provided\");\n        return this;\n    }\n    process.nextTick((function() {\n        this._packer.pack(this.data, this.width, this.height, this.gamma);\n    }).bind(this));\n    return this;\n};\nPNG.prototype.parse = function(data, callback) {\n    if (callback) {\n        let onParsed, onError;\n        onParsed = (function(parsedData) {\n            this.removeListener(\"error\", onError);\n            this.data = parsedData;\n            callback(null, this);\n        }).bind(this);\n        onError = (function(err) {\n            this.removeListener(\"parsed\", onParsed);\n            callback(err, null);\n        }).bind(this);\n        this.once(\"parsed\", onParsed);\n        this.once(\"error\", onError);\n    }\n    this.end(data);\n    return this;\n};\nPNG.prototype.write = function(data) {\n    this._parser.write(data);\n    return true;\n};\nPNG.prototype.end = function(data) {\n    this._parser.end(data);\n};\nPNG.prototype._metadata = function(metadata) {\n    this.width = metadata.width;\n    this.height = metadata.height;\n    this.emit(\"metadata\", metadata);\n};\nPNG.prototype._gamma = function(gamma) {\n    this.gamma = gamma;\n};\nPNG.prototype._handleClose = function() {\n    if (!this._parser.writable && !this._packer.readable) {\n        this.emit(\"close\");\n    }\n};\nPNG.bitblt = function(src, dst, srcX, srcY, width, height, deltaX, deltaY) {\n    // eslint-disable-line max-params\n    // coerce pixel dimensions to integers (also coerces undefined -> 0):\n    /* eslint-disable no-param-reassign */ srcX |= 0;\n    srcY |= 0;\n    width |= 0;\n    height |= 0;\n    deltaX |= 0;\n    deltaY |= 0;\n    /* eslint-enable no-param-reassign */ if (srcX > src.width || srcY > src.height || srcX + width > src.width || srcY + height > src.height) {\n        throw new Error(\"bitblt reading outside image\");\n    }\n    if (deltaX > dst.width || deltaY > dst.height || deltaX + width > dst.width || deltaY + height > dst.height) {\n        throw new Error(\"bitblt writing outside image\");\n    }\n    for(let y = 0; y < height; y++){\n        src.data.copy(dst.data, (deltaY + y) * dst.width + deltaX << 2, (srcY + y) * src.width + srcX << 2, (srcY + y) * src.width + srcX + width << 2);\n    }\n};\nPNG.prototype.bitblt = function(dst, srcX, srcY, width, height, deltaX, deltaY) {\n    // eslint-disable-line max-params\n    PNG.bitblt(this, dst, srcX, srcY, width, height, deltaX, deltaY);\n    return this;\n};\nPNG.adjustGamma = function(src) {\n    if (src.gamma) {\n        for(let y = 0; y < src.height; y++){\n            for(let x = 0; x < src.width; x++){\n                let idx = src.width * y + x << 2;\n                for(let i = 0; i < 3; i++){\n                    let sample = src.data[idx + i] / 255;\n                    sample = Math.pow(sample, 1 / 2.2 / src.gamma);\n                    src.data[idx + i] = Math.round(sample * 255);\n                }\n            }\n        }\n        src.gamma = 0;\n    }\n};\nPNG.prototype.adjustGamma = function() {\n    PNG.adjustGamma(this);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/png.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/sync-inflate.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/sync-inflate.js ***!
  \************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\nlet assert = (__webpack_require__(/*! assert */ \"assert\").ok);\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nlet util = __webpack_require__(/*! util */ \"util\");\nlet kMaxLength = (__webpack_require__(/*! buffer */ \"buffer\").kMaxLength);\nfunction Inflate(opts) {\n    if (!(this instanceof Inflate)) {\n        return new Inflate(opts);\n    }\n    if (opts && opts.chunkSize < zlib.Z_MIN_CHUNK) {\n        opts.chunkSize = zlib.Z_MIN_CHUNK;\n    }\n    zlib.Inflate.call(this, opts);\n    // Node 8 --> 9 compatibility check\n    this._offset = this._offset === undefined ? this._outOffset : this._offset;\n    this._buffer = this._buffer || this._outBuffer;\n    if (opts && opts.maxLength != null) {\n        this._maxLength = opts.maxLength;\n    }\n}\nfunction createInflate(opts) {\n    return new Inflate(opts);\n}\nfunction _close(engine, callback) {\n    if (callback) {\n        process.nextTick(callback);\n    }\n    // Caller may invoke .close after a zlib error (which will null _handle).\n    if (!engine._handle) {\n        return;\n    }\n    engine._handle.close();\n    engine._handle = null;\n}\nInflate.prototype._processChunk = function(chunk, flushFlag, asyncCb) {\n    if (typeof asyncCb === \"function\") {\n        return zlib.Inflate._processChunk.call(this, chunk, flushFlag, asyncCb);\n    }\n    let self = this;\n    let availInBefore = chunk && chunk.length;\n    let availOutBefore = this._chunkSize - this._offset;\n    let leftToInflate = this._maxLength;\n    let inOff = 0;\n    let buffers = [];\n    let nread = 0;\n    let error;\n    this.on(\"error\", function(err) {\n        error = err;\n    });\n    function handleChunk(availInAfter, availOutAfter) {\n        if (self._hadError) {\n            return;\n        }\n        let have = availOutBefore - availOutAfter;\n        assert(have >= 0, \"have should not go down\");\n        if (have > 0) {\n            let out = self._buffer.slice(self._offset, self._offset + have);\n            self._offset += have;\n            if (out.length > leftToInflate) {\n                out = out.slice(0, leftToInflate);\n            }\n            buffers.push(out);\n            nread += out.length;\n            leftToInflate -= out.length;\n            if (leftToInflate === 0) {\n                return false;\n            }\n        }\n        if (availOutAfter === 0 || self._offset >= self._chunkSize) {\n            availOutBefore = self._chunkSize;\n            self._offset = 0;\n            self._buffer = Buffer.allocUnsafe(self._chunkSize);\n        }\n        if (availOutAfter === 0) {\n            inOff += availInBefore - availInAfter;\n            availInBefore = availInAfter;\n            return true;\n        }\n        return false;\n    }\n    assert(this._handle, \"zlib binding closed\");\n    let res;\n    do {\n        res = this._handle.writeSync(flushFlag, chunk, inOff, availInBefore, this._buffer, this._offset, availOutBefore); // out_len\n        // Node 8 --> 9 compatibility check\n        res = res || this._writeState;\n    }while (!this._hadError && handleChunk(res[0], res[1]));\n    if (this._hadError) {\n        throw error;\n    }\n    if (nread >= kMaxLength) {\n        _close(this);\n        throw new RangeError(\"Cannot create final Buffer. It would be larger than 0x\" + kMaxLength.toString(16) + \" bytes\");\n    }\n    let buf = Buffer.concat(buffers, nread);\n    _close(this);\n    return buf;\n};\nutil.inherits(Inflate, zlib.Inflate);\nfunction zlibBufferSync(engine, buffer) {\n    if (typeof buffer === \"string\") {\n        buffer = Buffer.from(buffer);\n    }\n    if (!(buffer instanceof Buffer)) {\n        throw new TypeError(\"Not a string or buffer\");\n    }\n    let flushFlag = engine._finishFlushFlag;\n    if (flushFlag == null) {\n        flushFlag = zlib.Z_FINISH;\n    }\n    return engine._processChunk(buffer, flushFlag);\n}\nfunction inflateSync(buffer, opts) {\n    return zlibBufferSync(new Inflate(opts), buffer);\n}\nmodule.exports = exports = inflateSync;\nexports.Inflate = Inflate;\nexports.createInflate = createInflate;\nexports.inflateSync = inflateSync;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/sync-inflate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/sync-reader.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/sync-reader.js ***!
  \***********************************************/
/***/ ((module) => {

eval("\nlet SyncReader = module.exports = function(buffer) {\n    this._buffer = buffer;\n    this._reads = [];\n};\nSyncReader.prototype.read = function(length, callback) {\n    this._reads.push({\n        length: Math.abs(length),\n        allowLess: length < 0,\n        func: callback\n    });\n};\nSyncReader.prototype.process = function() {\n    // as long as there is any data and read requests\n    while(this._reads.length > 0 && this._buffer.length){\n        let read = this._reads[0];\n        if (this._buffer.length && (this._buffer.length >= read.length || read.allowLess)) {\n            // ok there is any data so that we can satisfy this request\n            this._reads.shift(); // == read\n            let buf = this._buffer;\n            this._buffer = buf.slice(read.length);\n            read.func.call(this, buf.slice(0, read.length));\n        } else {\n            break;\n        }\n    }\n    if (this._reads.length > 0) {\n        return new Error(\"There are some read requests waitng on finished stream\");\n    }\n    if (this._buffer.length > 0) {\n        return new Error(\"unrecognised content at end of stream\");\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/sync-reader.js\n");

/***/ })

};
;