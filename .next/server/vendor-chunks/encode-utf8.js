"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/encode-utf8";
exports.ids = ["vendor-chunks/encode-utf8"];
exports.modules = {

/***/ "(ssr)/./node_modules/encode-utf8/index.js":
/*!*******************************************!*\
  !*** ./node_modules/encode-utf8/index.js ***!
  \*******************************************/
/***/ ((module) => {

eval("\nmodule.exports = function encodeUtf8(input) {\n    var result = [];\n    var size = input.length;\n    for(var index = 0; index < size; index++){\n        var point = input.charCodeAt(index);\n        if (point >= 0xD800 && point <= 0xDBFF && size > index + 1) {\n            var second = input.charCodeAt(index + 1);\n            if (second >= 0xDC00 && second <= 0xDFFF) {\n                // https://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\n                point = (point - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\n                index += 1;\n            }\n        }\n        // US-ASCII\n        if (point < 0x80) {\n            result.push(point);\n            continue;\n        }\n        // 2-byte UTF-8\n        if (point < 0x800) {\n            result.push(point >> 6 | 192);\n            result.push(point & 63 | 128);\n            continue;\n        }\n        // 3-byte UTF-8\n        if (point < 0xD800 || point >= 0xE000 && point < 0x10000) {\n            result.push(point >> 12 | 224);\n            result.push(point >> 6 & 63 | 128);\n            result.push(point & 63 | 128);\n            continue;\n        }\n        // 4-byte UTF-8\n        if (point >= 0x10000 && point <= 0x10FFFF) {\n            result.push(point >> 18 | 240);\n            result.push(point >> 12 & 63 | 128);\n            result.push(point >> 6 & 63 | 128);\n            result.push(point & 63 | 128);\n            continue;\n        }\n        // Invalid character\n        result.push(0xEF, 0xBF, 0xBD);\n    }\n    return new Uint8Array(result).buffer;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZW5jb2RlLXV0ZjgvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFFQUEsT0FBT0MsT0FBTyxHQUFHLFNBQVNDLFdBQVlDLEtBQUs7SUFDekMsSUFBSUMsU0FBUyxFQUFFO0lBQ2YsSUFBSUMsT0FBT0YsTUFBTUcsTUFBTTtJQUV2QixJQUFLLElBQUlDLFFBQVEsR0FBR0EsUUFBUUYsTUFBTUUsUUFBUztRQUN6QyxJQUFJQyxRQUFRTCxNQUFNTSxVQUFVLENBQUNGO1FBRTdCLElBQUlDLFNBQVMsVUFBVUEsU0FBUyxVQUFVSCxPQUFPRSxRQUFRLEdBQUc7WUFDMUQsSUFBSUcsU0FBU1AsTUFBTU0sVUFBVSxDQUFDRixRQUFRO1lBRXRDLElBQUlHLFVBQVUsVUFBVUEsVUFBVSxRQUFRO2dCQUN4Qyx3RUFBd0U7Z0JBQ3hFRixRQUFRLENBQUNBLFFBQVEsTUFBSyxJQUFLLFFBQVFFLFNBQVMsU0FBUztnQkFDckRILFNBQVM7WUFDWDtRQUNGO1FBRUEsV0FBVztRQUNYLElBQUlDLFFBQVEsTUFBTTtZQUNoQkosT0FBT08sSUFBSSxDQUFDSDtZQUNaO1FBQ0Y7UUFFQSxlQUFlO1FBQ2YsSUFBSUEsUUFBUSxPQUFPO1lBQ2pCSixPQUFPTyxJQUFJLENBQUMsU0FBVSxJQUFLO1lBQzNCUCxPQUFPTyxJQUFJLENBQUMsUUFBUyxLQUFNO1lBQzNCO1FBQ0Y7UUFFQSxlQUFlO1FBQ2YsSUFBSUgsUUFBUSxVQUFXQSxTQUFTLFVBQVVBLFFBQVEsU0FBVTtZQUMxREosT0FBT08sSUFBSSxDQUFDLFNBQVUsS0FBTTtZQUM1QlAsT0FBT08sSUFBSSxDQUFDLFNBQVcsSUFBSyxLQUFNO1lBQ2xDUCxPQUFPTyxJQUFJLENBQUMsUUFBUyxLQUFNO1lBQzNCO1FBQ0Y7UUFFQSxlQUFlO1FBQ2YsSUFBSUgsU0FBUyxXQUFXQSxTQUFTLFVBQVU7WUFDekNKLE9BQU9PLElBQUksQ0FBQyxTQUFVLEtBQU07WUFDNUJQLE9BQU9PLElBQUksQ0FBQyxTQUFXLEtBQU0sS0FBTTtZQUNuQ1AsT0FBT08sSUFBSSxDQUFDLFNBQVcsSUFBSyxLQUFNO1lBQ2xDUCxPQUFPTyxJQUFJLENBQUMsUUFBUyxLQUFNO1lBQzNCO1FBQ0Y7UUFFQSxvQkFBb0I7UUFDcEJQLE9BQU9PLElBQUksQ0FBQyxNQUFNLE1BQU07SUFDMUI7SUFFQSxPQUFPLElBQUlDLFdBQVdSLFFBQVFTLE1BQU07QUFDdEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iaGVlbWRpbmUvLi9ub2RlX21vZHVsZXMvZW5jb2RlLXV0ZjgvaW5kZXguanM/OWQ4MCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBlbmNvZGVVdGY4IChpbnB1dCkge1xuICB2YXIgcmVzdWx0ID0gW11cbiAgdmFyIHNpemUgPSBpbnB1dC5sZW5ndGhcblxuICBmb3IgKHZhciBpbmRleCA9IDA7IGluZGV4IDwgc2l6ZTsgaW5kZXgrKykge1xuICAgIHZhciBwb2ludCA9IGlucHV0LmNoYXJDb2RlQXQoaW5kZXgpXG5cbiAgICBpZiAocG9pbnQgPj0gMHhEODAwICYmIHBvaW50IDw9IDB4REJGRiAmJiBzaXplID4gaW5kZXggKyAxKSB7XG4gICAgICB2YXIgc2Vjb25kID0gaW5wdXQuY2hhckNvZGVBdChpbmRleCArIDEpXG5cbiAgICAgIGlmIChzZWNvbmQgPj0gMHhEQzAwICYmIHNlY29uZCA8PSAweERGRkYpIHtcbiAgICAgICAgLy8gaHR0cHM6Ly9tYXRoaWFzYnluZW5zLmJlL25vdGVzL2phdmFzY3JpcHQtZW5jb2Rpbmcjc3Vycm9nYXRlLWZvcm11bGFlXG4gICAgICAgIHBvaW50ID0gKHBvaW50IC0gMHhEODAwKSAqIDB4NDAwICsgc2Vjb25kIC0gMHhEQzAwICsgMHgxMDAwMFxuICAgICAgICBpbmRleCArPSAxXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gVVMtQVNDSUlcbiAgICBpZiAocG9pbnQgPCAweDgwKSB7XG4gICAgICByZXN1bHQucHVzaChwb2ludClcbiAgICAgIGNvbnRpbnVlXG4gICAgfVxuXG4gICAgLy8gMi1ieXRlIFVURi04XG4gICAgaWYgKHBvaW50IDwgMHg4MDApIHtcbiAgICAgIHJlc3VsdC5wdXNoKChwb2ludCA+PiA2KSB8IDE5MilcbiAgICAgIHJlc3VsdC5wdXNoKChwb2ludCAmIDYzKSB8IDEyOClcbiAgICAgIGNvbnRpbnVlXG4gICAgfVxuXG4gICAgLy8gMy1ieXRlIFVURi04XG4gICAgaWYgKHBvaW50IDwgMHhEODAwIHx8IChwb2ludCA+PSAweEUwMDAgJiYgcG9pbnQgPCAweDEwMDAwKSkge1xuICAgICAgcmVzdWx0LnB1c2goKHBvaW50ID4+IDEyKSB8IDIyNClcbiAgICAgIHJlc3VsdC5wdXNoKCgocG9pbnQgPj4gNikgJiA2MykgfCAxMjgpXG4gICAgICByZXN1bHQucHVzaCgocG9pbnQgJiA2MykgfCAxMjgpXG4gICAgICBjb250aW51ZVxuICAgIH1cblxuICAgIC8vIDQtYnl0ZSBVVEYtOFxuICAgIGlmIChwb2ludCA+PSAweDEwMDAwICYmIHBvaW50IDw9IDB4MTBGRkZGKSB7XG4gICAgICByZXN1bHQucHVzaCgocG9pbnQgPj4gMTgpIHwgMjQwKVxuICAgICAgcmVzdWx0LnB1c2goKChwb2ludCA+PiAxMikgJiA2MykgfCAxMjgpXG4gICAgICByZXN1bHQucHVzaCgoKHBvaW50ID4+IDYpICYgNjMpIHwgMTI4KVxuICAgICAgcmVzdWx0LnB1c2goKHBvaW50ICYgNjMpIHwgMTI4KVxuICAgICAgY29udGludWVcbiAgICB9XG5cbiAgICAvLyBJbnZhbGlkIGNoYXJhY3RlclxuICAgIHJlc3VsdC5wdXNoKDB4RUYsIDB4QkYsIDB4QkQpXG4gIH1cblxuICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkocmVzdWx0KS5idWZmZXJcbn1cbiJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwiZW5jb2RlVXRmOCIsImlucHV0IiwicmVzdWx0Iiwic2l6ZSIsImxlbmd0aCIsImluZGV4IiwicG9pbnQiLCJjaGFyQ29kZUF0Iiwic2Vjb25kIiwicHVzaCIsIlVpbnQ4QXJyYXkiLCJidWZmZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/encode-utf8/index.js\n");

/***/ })

};
;