"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dompurify";
exports.ids = ["vendor-chunks/dompurify"];
exports.modules = {

/***/ "(ssr)/./node_modules/dompurify/dist/purify.es.js":
/*!**************************************************!*\
  !*** ./node_modules/dompurify/dist/purify.es.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ purify)\n/* harmony export */ });\n/*! @license DOMPurify 2.5.8 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/2.5.8/LICENSE */ function _typeof(obj) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(obj) {\n        return typeof obj;\n    } : function(obj) {\n        return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    }, _typeof(obj);\n}\nfunction _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n        o.__proto__ = p;\n        return o;\n    };\n    return _setPrototypeOf(o, p);\n}\nfunction _isNativeReflectConstruct() {\n    if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n    if (Reflect.construct.sham) return false;\n    if (typeof Proxy === \"function\") return true;\n    try {\n        Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));\n        return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction _construct(Parent, args, Class) {\n    if (_isNativeReflectConstruct()) {\n        _construct = Reflect.construct;\n    } else {\n        _construct = function _construct(Parent, args, Class) {\n            var a = [\n                null\n            ];\n            a.push.apply(a, args);\n            var Constructor = Function.bind.apply(Parent, a);\n            var instance = new Constructor();\n            if (Class) _setPrototypeOf(instance, Class.prototype);\n            return instance;\n        };\n    }\n    return _construct.apply(null, arguments);\n}\nfunction _toConsumableArray(arr) {\n    return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n    if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n    if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];\n    return arr2;\n}\nfunction _nonIterableSpread() {\n    throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nvar hasOwnProperty = Object.hasOwnProperty, setPrototypeOf = Object.setPrototypeOf, isFrozen = Object.isFrozen, getPrototypeOf = Object.getPrototypeOf, getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar freeze = Object.freeze, seal = Object.seal, create = Object.create; // eslint-disable-line import/no-mutable-exports\nvar _ref = typeof Reflect !== \"undefined\" && Reflect, apply = _ref.apply, construct = _ref.construct;\nif (!apply) {\n    apply = function apply(fun, thisValue, args) {\n        return fun.apply(thisValue, args);\n    };\n}\nif (!freeze) {\n    freeze = function freeze(x) {\n        return x;\n    };\n}\nif (!seal) {\n    seal = function seal(x) {\n        return x;\n    };\n}\nif (!construct) {\n    construct = function construct(Func, args) {\n        return _construct(Func, _toConsumableArray(args));\n    };\n}\nvar arrayForEach = unapply(Array.prototype.forEach);\nvar arrayPop = unapply(Array.prototype.pop);\nvar arrayPush = unapply(Array.prototype.push);\nvar stringToLowerCase = unapply(String.prototype.toLowerCase);\nvar stringToString = unapply(String.prototype.toString);\nvar stringMatch = unapply(String.prototype.match);\nvar stringReplace = unapply(String.prototype.replace);\nvar stringIndexOf = unapply(String.prototype.indexOf);\nvar stringTrim = unapply(String.prototype.trim);\nvar regExpTest = unapply(RegExp.prototype.test);\nvar typeErrorCreate = unconstruct(TypeError);\nfunction unapply(func) {\n    return function(thisArg) {\n        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n            args[_key - 1] = arguments[_key];\n        }\n        return apply(func, thisArg, args);\n    };\n}\nfunction unconstruct(func) {\n    return function() {\n        for(var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++){\n            args[_key2] = arguments[_key2];\n        }\n        return construct(func, args);\n    };\n}\n/* Add properties to a lookup table */ function addToSet(set, array, transformCaseFunc) {\n    var _transformCaseFunc;\n    transformCaseFunc = (_transformCaseFunc = transformCaseFunc) !== null && _transformCaseFunc !== void 0 ? _transformCaseFunc : stringToLowerCase;\n    if (setPrototypeOf) {\n        // Make 'in' and truthy checks like Boolean(set.constructor)\n        // independent of any properties defined on Object.prototype.\n        // Prevent prototype setters from intercepting set as a this value.\n        setPrototypeOf(set, null);\n    }\n    var l = array.length;\n    while(l--){\n        var element = array[l];\n        if (typeof element === \"string\") {\n            var lcElement = transformCaseFunc(element);\n            if (lcElement !== element) {\n                // Config presets (e.g. tags.js, attrs.js) are immutable.\n                if (!isFrozen(array)) {\n                    array[l] = lcElement;\n                }\n                element = lcElement;\n            }\n        }\n        set[element] = true;\n    }\n    return set;\n}\n/* Shallow clone an object */ function clone(object) {\n    var newObject = create(null);\n    var property;\n    for(property in object){\n        if (apply(hasOwnProperty, object, [\n            property\n        ]) === true) {\n            newObject[property] = object[property];\n        }\n    }\n    return newObject;\n}\n/* IE10 doesn't support __lookupGetter__ so lets'\n * simulate it. It also automatically checks\n * if the prop is function or getter and behaves\n * accordingly. */ function lookupGetter(object, prop) {\n    while(object !== null){\n        var desc = getOwnPropertyDescriptor(object, prop);\n        if (desc) {\n            if (desc.get) {\n                return unapply(desc.get);\n            }\n            if (typeof desc.value === \"function\") {\n                return unapply(desc.value);\n            }\n        }\n        object = getPrototypeOf(object);\n    }\n    function fallbackValue(element) {\n        console.warn(\"fallback value for\", element);\n        return null;\n    }\n    return fallbackValue;\n}\nvar html$1 = freeze([\n    \"a\",\n    \"abbr\",\n    \"acronym\",\n    \"address\",\n    \"area\",\n    \"article\",\n    \"aside\",\n    \"audio\",\n    \"b\",\n    \"bdi\",\n    \"bdo\",\n    \"big\",\n    \"blink\",\n    \"blockquote\",\n    \"body\",\n    \"br\",\n    \"button\",\n    \"canvas\",\n    \"caption\",\n    \"center\",\n    \"cite\",\n    \"code\",\n    \"col\",\n    \"colgroup\",\n    \"content\",\n    \"data\",\n    \"datalist\",\n    \"dd\",\n    \"decorator\",\n    \"del\",\n    \"details\",\n    \"dfn\",\n    \"dialog\",\n    \"dir\",\n    \"div\",\n    \"dl\",\n    \"dt\",\n    \"element\",\n    \"em\",\n    \"fieldset\",\n    \"figcaption\",\n    \"figure\",\n    \"font\",\n    \"footer\",\n    \"form\",\n    \"h1\",\n    \"h2\",\n    \"h3\",\n    \"h4\",\n    \"h5\",\n    \"h6\",\n    \"head\",\n    \"header\",\n    \"hgroup\",\n    \"hr\",\n    \"html\",\n    \"i\",\n    \"img\",\n    \"input\",\n    \"ins\",\n    \"kbd\",\n    \"label\",\n    \"legend\",\n    \"li\",\n    \"main\",\n    \"map\",\n    \"mark\",\n    \"marquee\",\n    \"menu\",\n    \"menuitem\",\n    \"meter\",\n    \"nav\",\n    \"nobr\",\n    \"ol\",\n    \"optgroup\",\n    \"option\",\n    \"output\",\n    \"p\",\n    \"picture\",\n    \"pre\",\n    \"progress\",\n    \"q\",\n    \"rp\",\n    \"rt\",\n    \"ruby\",\n    \"s\",\n    \"samp\",\n    \"section\",\n    \"select\",\n    \"shadow\",\n    \"small\",\n    \"source\",\n    \"spacer\",\n    \"span\",\n    \"strike\",\n    \"strong\",\n    \"style\",\n    \"sub\",\n    \"summary\",\n    \"sup\",\n    \"table\",\n    \"tbody\",\n    \"td\",\n    \"template\",\n    \"textarea\",\n    \"tfoot\",\n    \"th\",\n    \"thead\",\n    \"time\",\n    \"tr\",\n    \"track\",\n    \"tt\",\n    \"u\",\n    \"ul\",\n    \"var\",\n    \"video\",\n    \"wbr\"\n]);\n// SVG\nvar svg$1 = freeze([\n    \"svg\",\n    \"a\",\n    \"altglyph\",\n    \"altglyphdef\",\n    \"altglyphitem\",\n    \"animatecolor\",\n    \"animatemotion\",\n    \"animatetransform\",\n    \"circle\",\n    \"clippath\",\n    \"defs\",\n    \"desc\",\n    \"ellipse\",\n    \"filter\",\n    \"font\",\n    \"g\",\n    \"glyph\",\n    \"glyphref\",\n    \"hkern\",\n    \"image\",\n    \"line\",\n    \"lineargradient\",\n    \"marker\",\n    \"mask\",\n    \"metadata\",\n    \"mpath\",\n    \"path\",\n    \"pattern\",\n    \"polygon\",\n    \"polyline\",\n    \"radialgradient\",\n    \"rect\",\n    \"stop\",\n    \"style\",\n    \"switch\",\n    \"symbol\",\n    \"text\",\n    \"textpath\",\n    \"title\",\n    \"tref\",\n    \"tspan\",\n    \"view\",\n    \"vkern\"\n]);\nvar svgFilters = freeze([\n    \"feBlend\",\n    \"feColorMatrix\",\n    \"feComponentTransfer\",\n    \"feComposite\",\n    \"feConvolveMatrix\",\n    \"feDiffuseLighting\",\n    \"feDisplacementMap\",\n    \"feDistantLight\",\n    \"feFlood\",\n    \"feFuncA\",\n    \"feFuncB\",\n    \"feFuncG\",\n    \"feFuncR\",\n    \"feGaussianBlur\",\n    \"feImage\",\n    \"feMerge\",\n    \"feMergeNode\",\n    \"feMorphology\",\n    \"feOffset\",\n    \"fePointLight\",\n    \"feSpecularLighting\",\n    \"feSpotLight\",\n    \"feTile\",\n    \"feTurbulence\"\n]);\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nvar svgDisallowed = freeze([\n    \"animate\",\n    \"color-profile\",\n    \"cursor\",\n    \"discard\",\n    \"fedropshadow\",\n    \"font-face\",\n    \"font-face-format\",\n    \"font-face-name\",\n    \"font-face-src\",\n    \"font-face-uri\",\n    \"foreignobject\",\n    \"hatch\",\n    \"hatchpath\",\n    \"mesh\",\n    \"meshgradient\",\n    \"meshpatch\",\n    \"meshrow\",\n    \"missing-glyph\",\n    \"script\",\n    \"set\",\n    \"solidcolor\",\n    \"unknown\",\n    \"use\"\n]);\nvar mathMl$1 = freeze([\n    \"math\",\n    \"menclose\",\n    \"merror\",\n    \"mfenced\",\n    \"mfrac\",\n    \"mglyph\",\n    \"mi\",\n    \"mlabeledtr\",\n    \"mmultiscripts\",\n    \"mn\",\n    \"mo\",\n    \"mover\",\n    \"mpadded\",\n    \"mphantom\",\n    \"mroot\",\n    \"mrow\",\n    \"ms\",\n    \"mspace\",\n    \"msqrt\",\n    \"mstyle\",\n    \"msub\",\n    \"msup\",\n    \"msubsup\",\n    \"mtable\",\n    \"mtd\",\n    \"mtext\",\n    \"mtr\",\n    \"munder\",\n    \"munderover\"\n]);\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nvar mathMlDisallowed = freeze([\n    \"maction\",\n    \"maligngroup\",\n    \"malignmark\",\n    \"mlongdiv\",\n    \"mscarries\",\n    \"mscarry\",\n    \"msgroup\",\n    \"mstack\",\n    \"msline\",\n    \"msrow\",\n    \"semantics\",\n    \"annotation\",\n    \"annotation-xml\",\n    \"mprescripts\",\n    \"none\"\n]);\nvar text = freeze([\n    \"#text\"\n]);\nvar html = freeze([\n    \"accept\",\n    \"action\",\n    \"align\",\n    \"alt\",\n    \"autocapitalize\",\n    \"autocomplete\",\n    \"autopictureinpicture\",\n    \"autoplay\",\n    \"background\",\n    \"bgcolor\",\n    \"border\",\n    \"capture\",\n    \"cellpadding\",\n    \"cellspacing\",\n    \"checked\",\n    \"cite\",\n    \"class\",\n    \"clear\",\n    \"color\",\n    \"cols\",\n    \"colspan\",\n    \"controls\",\n    \"controlslist\",\n    \"coords\",\n    \"crossorigin\",\n    \"datetime\",\n    \"decoding\",\n    \"default\",\n    \"dir\",\n    \"disabled\",\n    \"disablepictureinpicture\",\n    \"disableremoteplayback\",\n    \"download\",\n    \"draggable\",\n    \"enctype\",\n    \"enterkeyhint\",\n    \"face\",\n    \"for\",\n    \"headers\",\n    \"height\",\n    \"hidden\",\n    \"high\",\n    \"href\",\n    \"hreflang\",\n    \"id\",\n    \"inputmode\",\n    \"integrity\",\n    \"ismap\",\n    \"kind\",\n    \"label\",\n    \"lang\",\n    \"list\",\n    \"loading\",\n    \"loop\",\n    \"low\",\n    \"max\",\n    \"maxlength\",\n    \"media\",\n    \"method\",\n    \"min\",\n    \"minlength\",\n    \"multiple\",\n    \"muted\",\n    \"name\",\n    \"nonce\",\n    \"noshade\",\n    \"novalidate\",\n    \"nowrap\",\n    \"open\",\n    \"optimum\",\n    \"pattern\",\n    \"placeholder\",\n    \"playsinline\",\n    \"poster\",\n    \"preload\",\n    \"pubdate\",\n    \"radiogroup\",\n    \"readonly\",\n    \"rel\",\n    \"required\",\n    \"rev\",\n    \"reversed\",\n    \"role\",\n    \"rows\",\n    \"rowspan\",\n    \"spellcheck\",\n    \"scope\",\n    \"selected\",\n    \"shape\",\n    \"size\",\n    \"sizes\",\n    \"span\",\n    \"srclang\",\n    \"start\",\n    \"src\",\n    \"srcset\",\n    \"step\",\n    \"style\",\n    \"summary\",\n    \"tabindex\",\n    \"title\",\n    \"translate\",\n    \"type\",\n    \"usemap\",\n    \"valign\",\n    \"value\",\n    \"width\",\n    \"xmlns\",\n    \"slot\"\n]);\nvar svg = freeze([\n    \"accent-height\",\n    \"accumulate\",\n    \"additive\",\n    \"alignment-baseline\",\n    \"ascent\",\n    \"attributename\",\n    \"attributetype\",\n    \"azimuth\",\n    \"basefrequency\",\n    \"baseline-shift\",\n    \"begin\",\n    \"bias\",\n    \"by\",\n    \"class\",\n    \"clip\",\n    \"clippathunits\",\n    \"clip-path\",\n    \"clip-rule\",\n    \"color\",\n    \"color-interpolation\",\n    \"color-interpolation-filters\",\n    \"color-profile\",\n    \"color-rendering\",\n    \"cx\",\n    \"cy\",\n    \"d\",\n    \"dx\",\n    \"dy\",\n    \"diffuseconstant\",\n    \"direction\",\n    \"display\",\n    \"divisor\",\n    \"dur\",\n    \"edgemode\",\n    \"elevation\",\n    \"end\",\n    \"fill\",\n    \"fill-opacity\",\n    \"fill-rule\",\n    \"filter\",\n    \"filterunits\",\n    \"flood-color\",\n    \"flood-opacity\",\n    \"font-family\",\n    \"font-size\",\n    \"font-size-adjust\",\n    \"font-stretch\",\n    \"font-style\",\n    \"font-variant\",\n    \"font-weight\",\n    \"fx\",\n    \"fy\",\n    \"g1\",\n    \"g2\",\n    \"glyph-name\",\n    \"glyphref\",\n    \"gradientunits\",\n    \"gradienttransform\",\n    \"height\",\n    \"href\",\n    \"id\",\n    \"image-rendering\",\n    \"in\",\n    \"in2\",\n    \"k\",\n    \"k1\",\n    \"k2\",\n    \"k3\",\n    \"k4\",\n    \"kerning\",\n    \"keypoints\",\n    \"keysplines\",\n    \"keytimes\",\n    \"lang\",\n    \"lengthadjust\",\n    \"letter-spacing\",\n    \"kernelmatrix\",\n    \"kernelunitlength\",\n    \"lighting-color\",\n    \"local\",\n    \"marker-end\",\n    \"marker-mid\",\n    \"marker-start\",\n    \"markerheight\",\n    \"markerunits\",\n    \"markerwidth\",\n    \"maskcontentunits\",\n    \"maskunits\",\n    \"max\",\n    \"mask\",\n    \"media\",\n    \"method\",\n    \"mode\",\n    \"min\",\n    \"name\",\n    \"numoctaves\",\n    \"offset\",\n    \"operator\",\n    \"opacity\",\n    \"order\",\n    \"orient\",\n    \"orientation\",\n    \"origin\",\n    \"overflow\",\n    \"paint-order\",\n    \"path\",\n    \"pathlength\",\n    \"patterncontentunits\",\n    \"patterntransform\",\n    \"patternunits\",\n    \"points\",\n    \"preservealpha\",\n    \"preserveaspectratio\",\n    \"primitiveunits\",\n    \"r\",\n    \"rx\",\n    \"ry\",\n    \"radius\",\n    \"refx\",\n    \"refy\",\n    \"repeatcount\",\n    \"repeatdur\",\n    \"restart\",\n    \"result\",\n    \"rotate\",\n    \"scale\",\n    \"seed\",\n    \"shape-rendering\",\n    \"specularconstant\",\n    \"specularexponent\",\n    \"spreadmethod\",\n    \"startoffset\",\n    \"stddeviation\",\n    \"stitchtiles\",\n    \"stop-color\",\n    \"stop-opacity\",\n    \"stroke-dasharray\",\n    \"stroke-dashoffset\",\n    \"stroke-linecap\",\n    \"stroke-linejoin\",\n    \"stroke-miterlimit\",\n    \"stroke-opacity\",\n    \"stroke\",\n    \"stroke-width\",\n    \"style\",\n    \"surfacescale\",\n    \"systemlanguage\",\n    \"tabindex\",\n    \"targetx\",\n    \"targety\",\n    \"transform\",\n    \"transform-origin\",\n    \"text-anchor\",\n    \"text-decoration\",\n    \"text-rendering\",\n    \"textlength\",\n    \"type\",\n    \"u1\",\n    \"u2\",\n    \"unicode\",\n    \"values\",\n    \"viewbox\",\n    \"visibility\",\n    \"version\",\n    \"vert-adv-y\",\n    \"vert-origin-x\",\n    \"vert-origin-y\",\n    \"width\",\n    \"word-spacing\",\n    \"wrap\",\n    \"writing-mode\",\n    \"xchannelselector\",\n    \"ychannelselector\",\n    \"x\",\n    \"x1\",\n    \"x2\",\n    \"xmlns\",\n    \"y\",\n    \"y1\",\n    \"y2\",\n    \"z\",\n    \"zoomandpan\"\n]);\nvar mathMl = freeze([\n    \"accent\",\n    \"accentunder\",\n    \"align\",\n    \"bevelled\",\n    \"close\",\n    \"columnsalign\",\n    \"columnlines\",\n    \"columnspan\",\n    \"denomalign\",\n    \"depth\",\n    \"dir\",\n    \"display\",\n    \"displaystyle\",\n    \"encoding\",\n    \"fence\",\n    \"frame\",\n    \"height\",\n    \"href\",\n    \"id\",\n    \"largeop\",\n    \"length\",\n    \"linethickness\",\n    \"lspace\",\n    \"lquote\",\n    \"mathbackground\",\n    \"mathcolor\",\n    \"mathsize\",\n    \"mathvariant\",\n    \"maxsize\",\n    \"minsize\",\n    \"movablelimits\",\n    \"notation\",\n    \"numalign\",\n    \"open\",\n    \"rowalign\",\n    \"rowlines\",\n    \"rowspacing\",\n    \"rowspan\",\n    \"rspace\",\n    \"rquote\",\n    \"scriptlevel\",\n    \"scriptminsize\",\n    \"scriptsizemultiplier\",\n    \"selection\",\n    \"separator\",\n    \"separators\",\n    \"stretchy\",\n    \"subscriptshift\",\n    \"supscriptshift\",\n    \"symmetric\",\n    \"voffset\",\n    \"width\",\n    \"xmlns\"\n]);\nvar xml = freeze([\n    \"xlink:href\",\n    \"xml:id\",\n    \"xlink:title\",\n    \"xml:space\",\n    \"xmlns:xlink\"\n]);\n// eslint-disable-next-line unicorn/better-regex\nvar MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nvar ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nvar TMPLIT_EXPR = seal(/\\${[\\w\\W]*}/gm);\nvar DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]+$/); // eslint-disable-line no-useless-escape\nvar ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nvar IS_ALLOWED_URI = seal(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nvar IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nvar ATTR_WHITESPACE = seal(/[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nvar DOCTYPE_NAME = seal(/^html$/i);\nvar CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\nvar getGlobal = function getGlobal() {\n    return  true ? null : 0;\n};\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {Document} document The document object (to determine policy name suffix)\n * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported).\n */ var _createTrustedTypesPolicy = function _createTrustedTypesPolicy(trustedTypes, document) {\n    if (_typeof(trustedTypes) !== \"object\" || typeof trustedTypes.createPolicy !== \"function\") {\n        return null;\n    }\n    // Allow the callers to control the unique policy name\n    // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n    // Policy creation with duplicate names throws in Trusted Types.\n    var suffix = null;\n    var ATTR_NAME = \"data-tt-policy-suffix\";\n    if (document.currentScript && document.currentScript.hasAttribute(ATTR_NAME)) {\n        suffix = document.currentScript.getAttribute(ATTR_NAME);\n    }\n    var policyName = \"dompurify\" + (suffix ? \"#\" + suffix : \"\");\n    try {\n        return trustedTypes.createPolicy(policyName, {\n            createHTML: function createHTML(html) {\n                return html;\n            },\n            createScriptURL: function createScriptURL(scriptUrl) {\n                return scriptUrl;\n            }\n        });\n    } catch (_) {\n        // Policy creation failed (most likely another DOMPurify script has\n        // already run). Skip creating the policy, as this will only cause errors\n        // if TT are enforced.\n        console.warn(\"TrustedTypes policy \" + policyName + \" could not be created.\");\n        return null;\n    }\n};\nfunction createDOMPurify() {\n    var window1 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : getGlobal();\n    var DOMPurify = function DOMPurify(root) {\n        return createDOMPurify(root);\n    };\n    /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */ DOMPurify.version = \"2.5.8\";\n    /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */ DOMPurify.removed = [];\n    if (!window1 || !window1.document || window1.document.nodeType !== 9) {\n        // Not running in a browser, provide a factory function\n        // so that you can pass your own Window\n        DOMPurify.isSupported = false;\n        return DOMPurify;\n    }\n    var originalDocument = window1.document;\n    var document = window1.document;\n    var DocumentFragment = window1.DocumentFragment, HTMLTemplateElement = window1.HTMLTemplateElement, Node = window1.Node, Element = window1.Element, NodeFilter = window1.NodeFilter, _window$NamedNodeMap = window1.NamedNodeMap, NamedNodeMap = _window$NamedNodeMap === void 0 ? window1.NamedNodeMap || window1.MozNamedAttrMap : _window$NamedNodeMap, HTMLFormElement = window1.HTMLFormElement, DOMParser = window1.DOMParser, trustedTypes = window1.trustedTypes;\n    var ElementPrototype = Element.prototype;\n    var cloneNode = lookupGetter(ElementPrototype, \"cloneNode\");\n    var getNextSibling = lookupGetter(ElementPrototype, \"nextSibling\");\n    var getChildNodes = lookupGetter(ElementPrototype, \"childNodes\");\n    var getParentNode = lookupGetter(ElementPrototype, \"parentNode\");\n    // As per issue #47, the web-components registry is inherited by a\n    // new document created via createHTMLDocument. As per the spec\n    // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n    // a new empty registry is used when creating a template contents owner\n    // document, so we use that as our parent document to ensure nothing\n    // is inherited.\n    if (typeof HTMLTemplateElement === \"function\") {\n        var template = document.createElement(\"template\");\n        if (template.content && template.content.ownerDocument) {\n            document = template.content.ownerDocument;\n        }\n    }\n    var trustedTypesPolicy = _createTrustedTypesPolicy(trustedTypes, originalDocument);\n    var emptyHTML = trustedTypesPolicy ? trustedTypesPolicy.createHTML(\"\") : \"\";\n    var _document = document, implementation = _document.implementation, createNodeIterator = _document.createNodeIterator, createDocumentFragment = _document.createDocumentFragment, getElementsByTagName = _document.getElementsByTagName;\n    var importNode = originalDocument.importNode;\n    var documentMode = {};\n    try {\n        documentMode = clone(document).documentMode ? document.documentMode : {};\n    } catch (_) {}\n    var hooks = {};\n    /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */ DOMPurify.isSupported = typeof getParentNode === \"function\" && implementation && implementation.createHTMLDocument !== undefined && documentMode !== 9;\n    var MUSTACHE_EXPR$1 = MUSTACHE_EXPR, ERB_EXPR$1 = ERB_EXPR, TMPLIT_EXPR$1 = TMPLIT_EXPR, DATA_ATTR$1 = DATA_ATTR, ARIA_ATTR$1 = ARIA_ATTR, IS_SCRIPT_OR_DATA$1 = IS_SCRIPT_OR_DATA, ATTR_WHITESPACE$1 = ATTR_WHITESPACE, CUSTOM_ELEMENT$1 = CUSTOM_ELEMENT;\n    var IS_ALLOWED_URI$1 = IS_ALLOWED_URI;\n    /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */ /* allowed element names */ var ALLOWED_TAGS = null;\n    var DEFAULT_ALLOWED_TAGS = addToSet({}, [].concat(_toConsumableArray(html$1), _toConsumableArray(svg$1), _toConsumableArray(svgFilters), _toConsumableArray(mathMl$1), _toConsumableArray(text)));\n    /* Allowed attribute names */ var ALLOWED_ATTR = null;\n    var DEFAULT_ALLOWED_ATTR = addToSet({}, [].concat(_toConsumableArray(html), _toConsumableArray(svg), _toConsumableArray(mathMl), _toConsumableArray(xml)));\n    /*\n   * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */ var CUSTOM_ELEMENT_HANDLING = Object.seal(Object.create(null, {\n        tagNameCheck: {\n            writable: true,\n            configurable: false,\n            enumerable: true,\n            value: null\n        },\n        attributeNameCheck: {\n            writable: true,\n            configurable: false,\n            enumerable: true,\n            value: null\n        },\n        allowCustomizedBuiltInElements: {\n            writable: true,\n            configurable: false,\n            enumerable: true,\n            value: false\n        }\n    }));\n    /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */ var FORBID_TAGS = null;\n    /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */ var FORBID_ATTR = null;\n    /* Decide if ARIA attributes are okay */ var ALLOW_ARIA_ATTR = true;\n    /* Decide if custom data attributes are okay */ var ALLOW_DATA_ATTR = true;\n    /* Decide if unknown protocols are okay */ var ALLOW_UNKNOWN_PROTOCOLS = false;\n    /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */ var ALLOW_SELF_CLOSE_IN_ATTR = true;\n    /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */ var SAFE_FOR_TEMPLATES = false;\n    /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */ var SAFE_FOR_XML = true;\n    /* Decide if document with <html>... should be returned */ var WHOLE_DOCUMENT = false;\n    /* Track whether config is already set on this instance of DOMPurify. */ var SET_CONFIG = false;\n    /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */ var FORCE_BODY = false;\n    /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */ var RETURN_DOM = false;\n    /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */ var RETURN_DOM_FRAGMENT = false;\n    /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */ var RETURN_TRUSTED_TYPE = false;\n    /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */ var SANITIZE_DOM = true;\n    /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */ var SANITIZE_NAMED_PROPS = false;\n    var SANITIZE_NAMED_PROPS_PREFIX = \"user-content-\";\n    /* Keep element content when removing element? */ var KEEP_CONTENT = true;\n    /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */ var IN_PLACE = false;\n    /* Allow usage of profiles like html, svg and mathMl */ var USE_PROFILES = {};\n    /* Tags to ignore content of when KEEP_CONTENT is true */ var FORBID_CONTENTS = null;\n    var DEFAULT_FORBID_CONTENTS = addToSet({}, [\n        \"annotation-xml\",\n        \"audio\",\n        \"colgroup\",\n        \"desc\",\n        \"foreignobject\",\n        \"head\",\n        \"iframe\",\n        \"math\",\n        \"mi\",\n        \"mn\",\n        \"mo\",\n        \"ms\",\n        \"mtext\",\n        \"noembed\",\n        \"noframes\",\n        \"noscript\",\n        \"plaintext\",\n        \"script\",\n        \"style\",\n        \"svg\",\n        \"template\",\n        \"thead\",\n        \"title\",\n        \"video\",\n        \"xmp\"\n    ]);\n    /* Tags that are safe for data: URIs */ var DATA_URI_TAGS = null;\n    var DEFAULT_DATA_URI_TAGS = addToSet({}, [\n        \"audio\",\n        \"video\",\n        \"img\",\n        \"source\",\n        \"image\",\n        \"track\"\n    ]);\n    /* Attributes safe for values like \"javascript:\" */ var URI_SAFE_ATTRIBUTES = null;\n    var DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n        \"alt\",\n        \"class\",\n        \"for\",\n        \"id\",\n        \"label\",\n        \"name\",\n        \"pattern\",\n        \"placeholder\",\n        \"role\",\n        \"summary\",\n        \"title\",\n        \"value\",\n        \"style\",\n        \"xmlns\"\n    ]);\n    var MATHML_NAMESPACE = \"http://www.w3.org/1998/Math/MathML\";\n    var SVG_NAMESPACE = \"http://www.w3.org/2000/svg\";\n    var HTML_NAMESPACE = \"http://www.w3.org/1999/xhtml\";\n    /* Document namespace */ var NAMESPACE = HTML_NAMESPACE;\n    var IS_EMPTY_INPUT = false;\n    /* Allowed XHTML+XML namespaces */ var ALLOWED_NAMESPACES = null;\n    var DEFAULT_ALLOWED_NAMESPACES = addToSet({}, [\n        MATHML_NAMESPACE,\n        SVG_NAMESPACE,\n        HTML_NAMESPACE\n    ], stringToString);\n    /* Parsing of strict XHTML documents */ var PARSER_MEDIA_TYPE;\n    var SUPPORTED_PARSER_MEDIA_TYPES = [\n        \"application/xhtml+xml\",\n        \"text/html\"\n    ];\n    var DEFAULT_PARSER_MEDIA_TYPE = \"text/html\";\n    var transformCaseFunc;\n    /* Keep a reference to config to pass to hooks */ var CONFIG = null;\n    /* Ideally, do not touch anything below this line */ /* ______________________________________________ */ var formElement = document.createElement(\"form\");\n    var isRegexOrFunction = function isRegexOrFunction(testValue) {\n        return testValue instanceof RegExp || testValue instanceof Function;\n    };\n    /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */ // eslint-disable-next-line complexity\n    var _parseConfig = function _parseConfig(cfg) {\n        if (CONFIG && CONFIG === cfg) {\n            return;\n        }\n        /* Shield configuration object from tampering */ if (!cfg || _typeof(cfg) !== \"object\") {\n            cfg = {};\n        }\n        /* Shield configuration object from prototype pollution */ cfg = clone(cfg);\n        PARSER_MEDIA_TYPE = // eslint-disable-next-line unicorn/prefer-includes\n        SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1 ? PARSER_MEDIA_TYPE = DEFAULT_PARSER_MEDIA_TYPE : PARSER_MEDIA_TYPE = cfg.PARSER_MEDIA_TYPE;\n        // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n        transformCaseFunc = PARSER_MEDIA_TYPE === \"application/xhtml+xml\" ? stringToString : stringToLowerCase;\n        /* Set configuration parameters */ ALLOWED_TAGS = \"ALLOWED_TAGS\" in cfg ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc) : DEFAULT_ALLOWED_TAGS;\n        ALLOWED_ATTR = \"ALLOWED_ATTR\" in cfg ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc) : DEFAULT_ALLOWED_ATTR;\n        ALLOWED_NAMESPACES = \"ALLOWED_NAMESPACES\" in cfg ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString) : DEFAULT_ALLOWED_NAMESPACES;\n        URI_SAFE_ATTRIBUTES = \"ADD_URI_SAFE_ATTR\" in cfg ? addToSet(clone(DEFAULT_URI_SAFE_ATTRIBUTES), // eslint-disable-line indent\n        cfg.ADD_URI_SAFE_ATTR, // eslint-disable-line indent\n        transformCaseFunc // eslint-disable-line indent\n        ) // eslint-disable-line indent\n         : DEFAULT_URI_SAFE_ATTRIBUTES;\n        DATA_URI_TAGS = \"ADD_DATA_URI_TAGS\" in cfg ? addToSet(clone(DEFAULT_DATA_URI_TAGS), // eslint-disable-line indent\n        cfg.ADD_DATA_URI_TAGS, // eslint-disable-line indent\n        transformCaseFunc // eslint-disable-line indent\n        ) // eslint-disable-line indent\n         : DEFAULT_DATA_URI_TAGS;\n        FORBID_CONTENTS = \"FORBID_CONTENTS\" in cfg ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc) : DEFAULT_FORBID_CONTENTS;\n        FORBID_TAGS = \"FORBID_TAGS\" in cfg ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc) : {};\n        FORBID_ATTR = \"FORBID_ATTR\" in cfg ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc) : {};\n        USE_PROFILES = \"USE_PROFILES\" in cfg ? cfg.USE_PROFILES : false;\n        ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n        ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n        ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n        ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n        SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n        SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n        WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n        RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n        RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n        RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n        FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n        SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n        SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n        KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n        IN_PLACE = cfg.IN_PLACE || false; // Default false\n        IS_ALLOWED_URI$1 = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI$1;\n        NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n        CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n        if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)) {\n            CUSTOM_ELEMENT_HANDLING.tagNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n        }\n        if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)) {\n            CUSTOM_ELEMENT_HANDLING.attributeNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n        }\n        if (cfg.CUSTOM_ELEMENT_HANDLING && typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements === \"boolean\") {\n            CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements = cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n        }\n        if (SAFE_FOR_TEMPLATES) {\n            ALLOW_DATA_ATTR = false;\n        }\n        if (RETURN_DOM_FRAGMENT) {\n            RETURN_DOM = true;\n        }\n        /* Parse profile info */ if (USE_PROFILES) {\n            ALLOWED_TAGS = addToSet({}, _toConsumableArray(text));\n            ALLOWED_ATTR = [];\n            if (USE_PROFILES.html === true) {\n                addToSet(ALLOWED_TAGS, html$1);\n                addToSet(ALLOWED_ATTR, html);\n            }\n            if (USE_PROFILES.svg === true) {\n                addToSet(ALLOWED_TAGS, svg$1);\n                addToSet(ALLOWED_ATTR, svg);\n                addToSet(ALLOWED_ATTR, xml);\n            }\n            if (USE_PROFILES.svgFilters === true) {\n                addToSet(ALLOWED_TAGS, svgFilters);\n                addToSet(ALLOWED_ATTR, svg);\n                addToSet(ALLOWED_ATTR, xml);\n            }\n            if (USE_PROFILES.mathMl === true) {\n                addToSet(ALLOWED_TAGS, mathMl$1);\n                addToSet(ALLOWED_ATTR, mathMl);\n                addToSet(ALLOWED_ATTR, xml);\n            }\n        }\n        /* Merge configuration parameters */ if (cfg.ADD_TAGS) {\n            if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n                ALLOWED_TAGS = clone(ALLOWED_TAGS);\n            }\n            addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n        }\n        if (cfg.ADD_ATTR) {\n            if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n                ALLOWED_ATTR = clone(ALLOWED_ATTR);\n            }\n            addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n        }\n        if (cfg.ADD_URI_SAFE_ATTR) {\n            addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n        }\n        if (cfg.FORBID_CONTENTS) {\n            if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n                FORBID_CONTENTS = clone(FORBID_CONTENTS);\n            }\n            addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n        }\n        /* Add #text in case KEEP_CONTENT is set to true */ if (KEEP_CONTENT) {\n            ALLOWED_TAGS[\"#text\"] = true;\n        }\n        /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */ if (WHOLE_DOCUMENT) {\n            addToSet(ALLOWED_TAGS, [\n                \"html\",\n                \"head\",\n                \"body\"\n            ]);\n        }\n        /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */ if (ALLOWED_TAGS.table) {\n            addToSet(ALLOWED_TAGS, [\n                \"tbody\"\n            ]);\n            delete FORBID_TAGS.tbody;\n        }\n        // Prevent further manipulation of configuration.\n        // Not available in IE8, Safari 5, etc.\n        if (freeze) {\n            freeze(cfg);\n        }\n        CONFIG = cfg;\n    };\n    var MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, [\n        \"mi\",\n        \"mo\",\n        \"mn\",\n        \"ms\",\n        \"mtext\"\n    ]);\n    var HTML_INTEGRATION_POINTS = addToSet({}, [\n        \"annotation-xml\"\n    ]);\n    // Certain elements are allowed in both SVG and HTML\n    // namespace. We need to specify them explicitly\n    // so that they don't get erroneously deleted from\n    // HTML namespace.\n    var COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, [\n        \"title\",\n        \"style\",\n        \"font\",\n        \"a\",\n        \"script\"\n    ]);\n    /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */ var ALL_SVG_TAGS = addToSet({}, svg$1);\n    addToSet(ALL_SVG_TAGS, svgFilters);\n    addToSet(ALL_SVG_TAGS, svgDisallowed);\n    var ALL_MATHML_TAGS = addToSet({}, mathMl$1);\n    addToSet(ALL_MATHML_TAGS, mathMlDisallowed);\n    /**\n   *\n   *\n   * @param  {Element} element a DOM element whose namespace is being checked\n   * @returns {boolean} Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */ var _checkValidNamespace = function _checkValidNamespace(element) {\n        var parent = getParentNode(element);\n        // In JSDOM, if we're inside shadow DOM, then parentNode\n        // can be null. We just simulate parent in this case.\n        if (!parent || !parent.tagName) {\n            parent = {\n                namespaceURI: NAMESPACE,\n                tagName: \"template\"\n            };\n        }\n        var tagName = stringToLowerCase(element.tagName);\n        var parentTagName = stringToLowerCase(parent.tagName);\n        if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n            return false;\n        }\n        if (element.namespaceURI === SVG_NAMESPACE) {\n            // The only way to switch from HTML namespace to SVG\n            // is via <svg>. If it happens via any other tag, then\n            // it should be killed.\n            if (parent.namespaceURI === HTML_NAMESPACE) {\n                return tagName === \"svg\";\n            }\n            // The only way to switch from MathML to SVG is via`\n            // svg if parent is either <annotation-xml> or MathML\n            // text integration points.\n            if (parent.namespaceURI === MATHML_NAMESPACE) {\n                return tagName === \"svg\" && (parentTagName === \"annotation-xml\" || MATHML_TEXT_INTEGRATION_POINTS[parentTagName]);\n            }\n            // We only allow elements that are defined in SVG\n            // spec. All others are disallowed in SVG namespace.\n            return Boolean(ALL_SVG_TAGS[tagName]);\n        }\n        if (element.namespaceURI === MATHML_NAMESPACE) {\n            // The only way to switch from HTML namespace to MathML\n            // is via <math>. If it happens via any other tag, then\n            // it should be killed.\n            if (parent.namespaceURI === HTML_NAMESPACE) {\n                return tagName === \"math\";\n            }\n            // The only way to switch from SVG to MathML is via\n            // <math> and HTML integration points\n            if (parent.namespaceURI === SVG_NAMESPACE) {\n                return tagName === \"math\" && HTML_INTEGRATION_POINTS[parentTagName];\n            }\n            // We only allow elements that are defined in MathML\n            // spec. All others are disallowed in MathML namespace.\n            return Boolean(ALL_MATHML_TAGS[tagName]);\n        }\n        if (element.namespaceURI === HTML_NAMESPACE) {\n            // The only way to switch from SVG to HTML is via\n            // HTML integration points, and from MathML to HTML\n            // is via MathML text integration points\n            if (parent.namespaceURI === SVG_NAMESPACE && !HTML_INTEGRATION_POINTS[parentTagName]) {\n                return false;\n            }\n            if (parent.namespaceURI === MATHML_NAMESPACE && !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]) {\n                return false;\n            }\n            // We disallow tags that are specific for MathML\n            // or SVG and should never appear in HTML namespace\n            return !ALL_MATHML_TAGS[tagName] && (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName]);\n        }\n        // For XHTML and XML documents that support custom namespaces\n        if (PARSER_MEDIA_TYPE === \"application/xhtml+xml\" && ALLOWED_NAMESPACES[element.namespaceURI]) {\n            return true;\n        }\n        // The code should never reach this place (this means\n        // that the element somehow got namespace that is not\n        // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n        // Return false just in case.\n        return false;\n    };\n    /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */ var _forceRemove = function _forceRemove(node) {\n        arrayPush(DOMPurify.removed, {\n            element: node\n        });\n        try {\n            // eslint-disable-next-line unicorn/prefer-dom-node-remove\n            node.parentNode.removeChild(node);\n        } catch (_) {\n            try {\n                node.outerHTML = emptyHTML;\n            } catch (_) {\n                node.remove();\n            }\n        }\n    };\n    /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */ var _removeAttribute = function _removeAttribute(name, node) {\n        try {\n            arrayPush(DOMPurify.removed, {\n                attribute: node.getAttributeNode(name),\n                from: node\n            });\n        } catch (_) {\n            arrayPush(DOMPurify.removed, {\n                attribute: null,\n                from: node\n            });\n        }\n        node.removeAttribute(name);\n        // We void attribute values for unremovable \"is\"\" attributes\n        if (name === \"is\" && !ALLOWED_ATTR[name]) {\n            if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n                try {\n                    _forceRemove(node);\n                } catch (_) {}\n            } else {\n                try {\n                    node.setAttribute(name, \"\");\n                } catch (_) {}\n            }\n        }\n    };\n    /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */ var _initDocument = function _initDocument(dirty) {\n        /* Create a HTML document */ var doc;\n        var leadingWhitespace;\n        if (FORCE_BODY) {\n            dirty = \"<remove></remove>\" + dirty;\n        } else {\n            /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */ var matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n            leadingWhitespace = matches && matches[0];\n        }\n        if (PARSER_MEDIA_TYPE === \"application/xhtml+xml\" && NAMESPACE === HTML_NAMESPACE) {\n            // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n            dirty = '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' + dirty + \"</body></html>\";\n        }\n        var dirtyPayload = trustedTypesPolicy ? trustedTypesPolicy.createHTML(dirty) : dirty;\n        /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */ if (NAMESPACE === HTML_NAMESPACE) {\n            try {\n                doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n            } catch (_) {}\n        }\n        /* Use createHTMLDocument in case DOMParser is not available */ if (!doc || !doc.documentElement) {\n            doc = implementation.createDocument(NAMESPACE, \"template\", null);\n            try {\n                doc.documentElement.innerHTML = IS_EMPTY_INPUT ? emptyHTML : dirtyPayload;\n            } catch (_) {\n            // Syntax error if dirtyPayload is invalid xml\n            }\n        }\n        var body = doc.body || doc.documentElement;\n        if (dirty && leadingWhitespace) {\n            body.insertBefore(document.createTextNode(leadingWhitespace), body.childNodes[0] || null);\n        }\n        /* Work on whole document or just its body */ if (NAMESPACE === HTML_NAMESPACE) {\n            return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? \"html\" : \"body\")[0];\n        }\n        return WHOLE_DOCUMENT ? doc.documentElement : body;\n    };\n    /**\n   * _createIterator\n   *\n   * @param  {Document} root document/fragment to create iterator for\n   * @return {Iterator} iterator instance\n   */ var _createIterator = function _createIterator(root) {\n        return createNodeIterator.call(root.ownerDocument || root, root, // eslint-disable-next-line no-bitwise\n        NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT | NodeFilter.SHOW_PROCESSING_INSTRUCTION | NodeFilter.SHOW_CDATA_SECTION, null, false);\n    };\n    /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */ var _isClobbered = function _isClobbered(elm) {\n        return elm instanceof HTMLFormElement && (typeof elm.nodeName !== \"string\" || typeof elm.textContent !== \"string\" || typeof elm.removeChild !== \"function\" || !(elm.attributes instanceof NamedNodeMap) || typeof elm.removeAttribute !== \"function\" || typeof elm.setAttribute !== \"function\" || typeof elm.namespaceURI !== \"string\" || typeof elm.insertBefore !== \"function\" || typeof elm.hasChildNodes !== \"function\");\n    };\n    /**\n   * _isNode\n   *\n   * @param  {Node} obj object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */ var _isNode = function _isNode(object) {\n        return _typeof(Node) === \"object\" ? object instanceof Node : object && _typeof(object) === \"object\" && typeof object.nodeType === \"number\" && typeof object.nodeName === \"string\";\n    };\n    /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */ var _executeHook = function _executeHook(entryPoint, currentNode, data) {\n        if (!hooks[entryPoint]) {\n            return;\n        }\n        arrayForEach(hooks[entryPoint], function(hook) {\n            hook.call(DOMPurify, currentNode, data, CONFIG);\n        });\n    };\n    /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */ var _sanitizeElements = function _sanitizeElements(currentNode) {\n        var content;\n        /* Execute a hook if present */ _executeHook(\"beforeSanitizeElements\", currentNode, null);\n        /* Check if element is clobbered or can clobber */ if (_isClobbered(currentNode)) {\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Check if tagname contains Unicode */ if (regExpTest(/[\\u0080-\\uFFFF]/, currentNode.nodeName)) {\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Now let's check the element's type and name */ var tagName = transformCaseFunc(currentNode.nodeName);\n        /* Execute a hook if present */ _executeHook(\"uponSanitizeElement\", currentNode, {\n            tagName: tagName,\n            allowedTags: ALLOWED_TAGS\n        });\n        /* Detect mXSS attempts abusing namespace confusion */ if (currentNode.hasChildNodes() && !_isNode(currentNode.firstElementChild) && (!_isNode(currentNode.content) || !_isNode(currentNode.content.firstElementChild)) && regExpTest(/<[/\\w]/g, currentNode.innerHTML) && regExpTest(/<[/\\w]/g, currentNode.textContent)) {\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Mitigate a problem with templates inside select */ if (tagName === \"select\" && regExpTest(/<template/i, currentNode.innerHTML)) {\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Remove any ocurrence of processing instructions */ if (currentNode.nodeType === 7) {\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Remove any kind of possibly harmful comments */ if (SAFE_FOR_XML && currentNode.nodeType === 8 && regExpTest(/<[/\\w]/g, currentNode.data)) {\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Remove element if anything forbids its presence */ if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n            /* Check if we have a custom element to handle */ if (!FORBID_TAGS[tagName] && _basicCustomElementTest(tagName)) {\n                if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)) return false;\n                if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)) return false;\n            }\n            /* Keep content except for bad-listed elements */ if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n                var parentNode = getParentNode(currentNode) || currentNode.parentNode;\n                var childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n                if (childNodes && parentNode) {\n                    var childCount = childNodes.length;\n                    for(var i = childCount - 1; i >= 0; --i){\n                        var childClone = cloneNode(childNodes[i], true);\n                        childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n                        parentNode.insertBefore(childClone, getNextSibling(currentNode));\n                    }\n                }\n            }\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Check whether element has a valid namespace */ if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Make sure that older browsers don't get fallback-tag mXSS */ if ((tagName === \"noscript\" || tagName === \"noembed\" || tagName === \"noframes\") && regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)) {\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Sanitize element content to be template-safe */ if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n            /* Get the element's text content */ content = currentNode.textContent;\n            content = stringReplace(content, MUSTACHE_EXPR$1, \" \");\n            content = stringReplace(content, ERB_EXPR$1, \" \");\n            content = stringReplace(content, TMPLIT_EXPR$1, \" \");\n            if (currentNode.textContent !== content) {\n                arrayPush(DOMPurify.removed, {\n                    element: currentNode.cloneNode()\n                });\n                currentNode.textContent = content;\n            }\n        }\n        /* Execute a hook if present */ _executeHook(\"afterSanitizeElements\", currentNode, null);\n        return false;\n    };\n    /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */ // eslint-disable-next-line complexity\n    var _isValidAttribute = function _isValidAttribute(lcTag, lcName, value) {\n        /* Make sure attribute cannot clobber */ if (SANITIZE_DOM && (lcName === \"id\" || lcName === \"name\") && (value in document || value in formElement)) {\n            return false;\n        }\n        /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */ if (ALLOW_DATA_ATTR && !FORBID_ATTR[lcName] && regExpTest(DATA_ATTR$1, lcName)) ;\n        else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR$1, lcName)) ;\n        else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n            if (// First condition does a very basic check if a) it's basically a valid custom element tagname AND\n            // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n            // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n            _basicCustomElementTest(lcTag) && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag)) && (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName) || CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)) || // Alternative, second condition checks if it's an `is`-attribute, AND\n            // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n            lcName === \"is\" && CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))) ;\n            else {\n                return false;\n            }\n        /* Check value is safe. First, is attr inert? If so, is safe */ } else if (URI_SAFE_ATTRIBUTES[lcName]) ;\n        else if (regExpTest(IS_ALLOWED_URI$1, stringReplace(value, ATTR_WHITESPACE$1, \"\"))) ;\n        else if ((lcName === \"src\" || lcName === \"xlink:href\" || lcName === \"href\") && lcTag !== \"script\" && stringIndexOf(value, \"data:\") === 0 && DATA_URI_TAGS[lcTag]) ;\n        else if (ALLOW_UNKNOWN_PROTOCOLS && !regExpTest(IS_SCRIPT_OR_DATA$1, stringReplace(value, ATTR_WHITESPACE$1, \"\"))) ;\n        else if (value) {\n            return false;\n        } else ;\n        return true;\n    };\n    /**\n   * _basicCustomElementCheck\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   * @param {string} tagName name of the tag of the node to sanitize\n   */ var _basicCustomElementTest = function _basicCustomElementTest(tagName) {\n        return tagName !== \"annotation-xml\" && stringMatch(tagName, CUSTOM_ELEMENT$1);\n    };\n    /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */ var _sanitizeAttributes = function _sanitizeAttributes(currentNode) {\n        var attr;\n        var value;\n        var lcName;\n        var l;\n        /* Execute a hook if present */ _executeHook(\"beforeSanitizeAttributes\", currentNode, null);\n        var attributes = currentNode.attributes;\n        /* Check if we have attributes; if not we might have a text node */ if (!attributes || _isClobbered(currentNode)) {\n            return;\n        }\n        var hookEvent = {\n            attrName: \"\",\n            attrValue: \"\",\n            keepAttr: true,\n            allowedAttributes: ALLOWED_ATTR\n        };\n        l = attributes.length;\n        /* Go backwards over all attributes; safely remove bad ones */ while(l--){\n            attr = attributes[l];\n            var _attr = attr, name = _attr.name, namespaceURI = _attr.namespaceURI;\n            value = name === \"value\" ? attr.value : stringTrim(attr.value);\n            lcName = transformCaseFunc(name);\n            /* Execute a hook if present */ hookEvent.attrName = lcName;\n            hookEvent.attrValue = value;\n            hookEvent.keepAttr = true;\n            hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n            _executeHook(\"uponSanitizeAttribute\", currentNode, hookEvent);\n            value = hookEvent.attrValue;\n            /* Did the hooks approve of the attribute? */ if (hookEvent.forceKeepAttr) {\n                continue;\n            }\n            /* Remove attribute */ _removeAttribute(name, currentNode);\n            /* Did the hooks approve of the attribute? */ if (!hookEvent.keepAttr) {\n                continue;\n            }\n            /* Work around a security issue in jQuery 3.0 */ if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n                _removeAttribute(name, currentNode);\n                continue;\n            }\n            /* Sanitize attribute content to be template-safe */ if (SAFE_FOR_TEMPLATES) {\n                value = stringReplace(value, MUSTACHE_EXPR$1, \" \");\n                value = stringReplace(value, ERB_EXPR$1, \" \");\n                value = stringReplace(value, TMPLIT_EXPR$1, \" \");\n            }\n            /* Is `value` valid for this attribute? */ var lcTag = transformCaseFunc(currentNode.nodeName);\n            if (!_isValidAttribute(lcTag, lcName, value)) {\n                continue;\n            }\n            /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */ if (SANITIZE_NAMED_PROPS && (lcName === \"id\" || lcName === \"name\")) {\n                // Remove the attribute with this value\n                _removeAttribute(name, currentNode);\n                // Prefix the value and later re-create the attribute with the sanitized value\n                value = SANITIZE_NAMED_PROPS_PREFIX + value;\n            }\n            /* Work around a security issue with comments inside attributes */ if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n                _removeAttribute(name, currentNode);\n                continue;\n            }\n            /* Handle attributes that require Trusted Types */ if (trustedTypesPolicy && _typeof(trustedTypes) === \"object\" && typeof trustedTypes.getAttributeType === \"function\") {\n                if (namespaceURI) ;\n                else {\n                    switch(trustedTypes.getAttributeType(lcTag, lcName)){\n                        case \"TrustedHTML\":\n                            {\n                                value = trustedTypesPolicy.createHTML(value);\n                                break;\n                            }\n                        case \"TrustedScriptURL\":\n                            {\n                                value = trustedTypesPolicy.createScriptURL(value);\n                                break;\n                            }\n                    }\n                }\n            }\n            /* Handle invalid data-* attribute set by try-catching it */ try {\n                if (namespaceURI) {\n                    currentNode.setAttributeNS(namespaceURI, name, value);\n                } else {\n                    /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */ currentNode.setAttribute(name, value);\n                }\n                if (_isClobbered(currentNode)) {\n                    _forceRemove(currentNode);\n                } else {\n                    arrayPop(DOMPurify.removed);\n                }\n            } catch (_) {}\n        }\n        /* Execute a hook if present */ _executeHook(\"afterSanitizeAttributes\", currentNode, null);\n    };\n    /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */ var _sanitizeShadowDOM = function _sanitizeShadowDOM(fragment) {\n        var shadowNode;\n        var shadowIterator = _createIterator(fragment);\n        /* Execute a hook if present */ _executeHook(\"beforeSanitizeShadowDOM\", fragment, null);\n        while(shadowNode = shadowIterator.nextNode()){\n            /* Execute a hook if present */ _executeHook(\"uponSanitizeShadowNode\", shadowNode, null);\n            /* Sanitize tags and elements */ _sanitizeElements(shadowNode);\n            /* Check attributes next */ _sanitizeAttributes(shadowNode);\n            /* Deep shadow DOM detected */ if (shadowNode.content instanceof DocumentFragment) {\n                _sanitizeShadowDOM(shadowNode.content);\n            }\n        }\n        /* Execute a hook if present */ _executeHook(\"afterSanitizeShadowDOM\", fragment, null);\n    };\n    /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} configuration object\n   */ // eslint-disable-next-line complexity\n    DOMPurify.sanitize = function(dirty) {\n        var cfg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        var body;\n        var importedNode;\n        var currentNode;\n        var oldNode;\n        var returnNode;\n        /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */ IS_EMPTY_INPUT = !dirty;\n        if (IS_EMPTY_INPUT) {\n            dirty = \"<!-->\";\n        }\n        /* Stringify, in case dirty is an object */ if (typeof dirty !== \"string\" && !_isNode(dirty)) {\n            if (typeof dirty.toString === \"function\") {\n                dirty = dirty.toString();\n                if (typeof dirty !== \"string\") {\n                    throw typeErrorCreate(\"dirty is not a string, aborting\");\n                }\n            } else {\n                throw typeErrorCreate(\"toString is not a function\");\n            }\n        }\n        /* Check we can run. Otherwise fall back or ignore */ if (!DOMPurify.isSupported) {\n            if (_typeof(window1.toStaticHTML) === \"object\" || typeof window1.toStaticHTML === \"function\") {\n                if (typeof dirty === \"string\") {\n                    return window1.toStaticHTML(dirty);\n                }\n                if (_isNode(dirty)) {\n                    return window1.toStaticHTML(dirty.outerHTML);\n                }\n            }\n            return dirty;\n        }\n        /* Assign config vars */ if (!SET_CONFIG) {\n            _parseConfig(cfg);\n        }\n        /* Clean up removed elements */ DOMPurify.removed = [];\n        /* Check if dirty is correctly typed for IN_PLACE */ if (typeof dirty === \"string\") {\n            IN_PLACE = false;\n        }\n        if (IN_PLACE) {\n            /* Do some early pre-sanitization to avoid unsafe root nodes */ if (dirty.nodeName) {\n                var tagName = transformCaseFunc(dirty.nodeName);\n                if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n                    throw typeErrorCreate(\"root node is forbidden and cannot be sanitized in-place\");\n                }\n            }\n        } else if (dirty instanceof Node) {\n            /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */ body = _initDocument(\"<!---->\");\n            importedNode = body.ownerDocument.importNode(dirty, true);\n            if (importedNode.nodeType === 1 && importedNode.nodeName === \"BODY\") {\n                /* Node is already a body, use as is */ body = importedNode;\n            } else if (importedNode.nodeName === \"HTML\") {\n                body = importedNode;\n            } else {\n                // eslint-disable-next-line unicorn/prefer-dom-node-append\n                body.appendChild(importedNode);\n            }\n        } else {\n            /* Exit directly if we have nothing to do */ if (!RETURN_DOM && !SAFE_FOR_TEMPLATES && !WHOLE_DOCUMENT && // eslint-disable-next-line unicorn/prefer-includes\n            dirty.indexOf(\"<\") === -1) {\n                return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(dirty) : dirty;\n            }\n            /* Initialize the document to work on */ body = _initDocument(dirty);\n            /* Check we have a DOM node from the data */ if (!body) {\n                return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : \"\";\n            }\n        }\n        /* Remove first element node (ours) if FORCE_BODY is set */ if (body && FORCE_BODY) {\n            _forceRemove(body.firstChild);\n        }\n        /* Get node iterator */ var nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n        /* Now start iterating over the created document */ while(currentNode = nodeIterator.nextNode()){\n            /* Fix IE's strange behavior with manipulated textNodes #89 */ if (currentNode.nodeType === 3 && currentNode === oldNode) {\n                continue;\n            }\n            /* Sanitize tags and elements */ _sanitizeElements(currentNode);\n            /* Check attributes next */ _sanitizeAttributes(currentNode);\n            /* Shadow DOM detected, sanitize it */ if (currentNode.content instanceof DocumentFragment) {\n                _sanitizeShadowDOM(currentNode.content);\n            }\n            oldNode = currentNode;\n        }\n        oldNode = null;\n        /* If we sanitized `dirty` in-place, return it. */ if (IN_PLACE) {\n            return dirty;\n        }\n        /* Return sanitized string or DOM */ if (RETURN_DOM) {\n            if (RETURN_DOM_FRAGMENT) {\n                returnNode = createDocumentFragment.call(body.ownerDocument);\n                while(body.firstChild){\n                    // eslint-disable-next-line unicorn/prefer-dom-node-append\n                    returnNode.appendChild(body.firstChild);\n                }\n            } else {\n                returnNode = body;\n            }\n            if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmod) {\n                /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */ returnNode = importNode.call(originalDocument, returnNode, true);\n            }\n            return returnNode;\n        }\n        var serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n        /* Serialize doctype if allowed */ if (WHOLE_DOCUMENT && ALLOWED_TAGS[\"!doctype\"] && body.ownerDocument && body.ownerDocument.doctype && body.ownerDocument.doctype.name && regExpTest(DOCTYPE_NAME, body.ownerDocument.doctype.name)) {\n            serializedHTML = \"<!DOCTYPE \" + body.ownerDocument.doctype.name + \">\\n\" + serializedHTML;\n        }\n        /* Sanitize final string template-safe */ if (SAFE_FOR_TEMPLATES) {\n            serializedHTML = stringReplace(serializedHTML, MUSTACHE_EXPR$1, \" \");\n            serializedHTML = stringReplace(serializedHTML, ERB_EXPR$1, \" \");\n            serializedHTML = stringReplace(serializedHTML, TMPLIT_EXPR$1, \" \");\n        }\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(serializedHTML) : serializedHTML;\n    };\n    /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */ DOMPurify.setConfig = function(cfg) {\n        _parseConfig(cfg);\n        SET_CONFIG = true;\n    };\n    /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */ DOMPurify.clearConfig = function() {\n        CONFIG = null;\n        SET_CONFIG = false;\n    };\n    /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {string} tag Tag name of containing element.\n   * @param  {string} attr Attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */ DOMPurify.isValidAttribute = function(tag, attr, value) {\n        /* Initialize shared config vars if necessary. */ if (!CONFIG) {\n            _parseConfig({});\n        }\n        var lcTag = transformCaseFunc(tag);\n        var lcName = transformCaseFunc(attr);\n        return _isValidAttribute(lcTag, lcName, value);\n    };\n    /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */ DOMPurify.addHook = function(entryPoint, hookFunction) {\n        if (typeof hookFunction !== \"function\") {\n            return;\n        }\n        hooks[entryPoint] = hooks[entryPoint] || [];\n        arrayPush(hooks[entryPoint], hookFunction);\n    };\n    /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   * @return {Function} removed(popped) hook\n   */ DOMPurify.removeHook = function(entryPoint) {\n        if (hooks[entryPoint]) {\n            return arrayPop(hooks[entryPoint]);\n        }\n    };\n    /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */ DOMPurify.removeHooks = function(entryPoint) {\n        if (hooks[entryPoint]) {\n            hooks[entryPoint] = [];\n        }\n    };\n    /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   *\n   */ DOMPurify.removeAllHooks = function() {\n        hooks = {};\n    };\n    return DOMPurify;\n}\nvar purify = createDOMPurify();\n //# sourceMappingURL=purify.es.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dompurify/dist/purify.es.js\n");

/***/ })

};
;