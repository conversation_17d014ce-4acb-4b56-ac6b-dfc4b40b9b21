/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/qrcode";
exports.ids = ["vendor-chunks/qrcode"];
exports.modules = {

/***/ "(ssr)/./node_modules/qrcode/lib/browser.js":
/*!********************************************!*\
  !*** ./node_modules/qrcode/lib/browser.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const canPromise = __webpack_require__(/*! ./can-promise */ \"(ssr)/./node_modules/qrcode/lib/can-promise.js\");\nconst QRCode = __webpack_require__(/*! ./core/qrcode */ \"(ssr)/./node_modules/qrcode/lib/core/qrcode.js\");\nconst CanvasRenderer = __webpack_require__(/*! ./renderer/canvas */ \"(ssr)/./node_modules/qrcode/lib/renderer/canvas.js\");\nconst SvgRenderer = __webpack_require__(/*! ./renderer/svg-tag.js */ \"(ssr)/./node_modules/qrcode/lib/renderer/svg-tag.js\");\nfunction renderCanvas(renderFunc, canvas, text, opts, cb) {\n    const args = [].slice.call(arguments, 1);\n    const argsNum = args.length;\n    const isLastArgCb = typeof args[argsNum - 1] === \"function\";\n    if (!isLastArgCb && !canPromise()) {\n        throw new Error(\"Callback required as last argument\");\n    }\n    if (isLastArgCb) {\n        if (argsNum < 2) {\n            throw new Error(\"Too few arguments provided\");\n        }\n        if (argsNum === 2) {\n            cb = text;\n            text = canvas;\n            canvas = opts = undefined;\n        } else if (argsNum === 3) {\n            if (canvas.getContext && typeof cb === \"undefined\") {\n                cb = opts;\n                opts = undefined;\n            } else {\n                cb = opts;\n                opts = text;\n                text = canvas;\n                canvas = undefined;\n            }\n        }\n    } else {\n        if (argsNum < 1) {\n            throw new Error(\"Too few arguments provided\");\n        }\n        if (argsNum === 1) {\n            text = canvas;\n            canvas = opts = undefined;\n        } else if (argsNum === 2 && !canvas.getContext) {\n            opts = text;\n            text = canvas;\n            canvas = undefined;\n        }\n        return new Promise(function(resolve, reject) {\n            try {\n                const data = QRCode.create(text, opts);\n                resolve(renderFunc(data, canvas, opts));\n            } catch (e) {\n                reject(e);\n            }\n        });\n    }\n    try {\n        const data = QRCode.create(text, opts);\n        cb(null, renderFunc(data, canvas, opts));\n    } catch (e) {\n        cb(e);\n    }\n}\nexports.create = QRCode.create;\nexports.toCanvas = renderCanvas.bind(null, CanvasRenderer.render);\nexports.toDataURL = renderCanvas.bind(null, CanvasRenderer.renderToDataURL);\n// only svg for now.\nexports.toString = renderCanvas.bind(null, function(data, _, opts) {\n    return SvgRenderer.render(data, opts);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/browser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/can-promise.js":
/*!************************************************!*\
  !*** ./node_modules/qrcode/lib/can-promise.js ***!
  \************************************************/
/***/ ((module) => {

eval("// can-promise has a crash in some versions of react native that dont have\n// standard global objects\n// https://github.com/soldair/node-qrcode/issues/157\nmodule.exports = function() {\n    return typeof Promise === \"function\" && Promise.prototype && Promise.prototype.then;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iaGVlbWRpbmUvLi9ub2RlX21vZHVsZXMvcXJjb2RlL2xpYi9jYW4tcHJvbWlzZS5qcz85Y2IyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGNhbi1wcm9taXNlIGhhcyBhIGNyYXNoIGluIHNvbWUgdmVyc2lvbnMgb2YgcmVhY3QgbmF0aXZlIHRoYXQgZG9udCBoYXZlXG4vLyBzdGFuZGFyZCBnbG9iYWwgb2JqZWN0c1xuLy8gaHR0cHM6Ly9naXRodWIuY29tL3NvbGRhaXIvbm9kZS1xcmNvZGUvaXNzdWVzLzE1N1xuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uICgpIHtcbiAgcmV0dXJuIHR5cGVvZiBQcm9taXNlID09PSAnZnVuY3Rpb24nICYmIFByb21pc2UucHJvdG90eXBlICYmIFByb21pc2UucHJvdG90eXBlLnRoZW5cbn1cbiJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwiUHJvbWlzZSIsInByb3RvdHlwZSIsInRoZW4iXSwibWFwcGluZ3MiOiJBQUFBLDBFQUEwRTtBQUMxRSwwQkFBMEI7QUFDMUIsb0RBQW9EO0FBRXBEQSxPQUFPQyxPQUFPLEdBQUc7SUFDZixPQUFPLE9BQU9DLFlBQVksY0FBY0EsUUFBUUMsU0FBUyxJQUFJRCxRQUFRQyxTQUFTLENBQUNDLElBQUk7QUFDckYiLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcXJjb2RlL2xpYi9jYW4tcHJvbWlzZS5qcyIsInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/can-promise.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/alignment-pattern.js":
/*!***********************************************************!*\
  !*** ./node_modules/qrcode/lib/core/alignment-pattern.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/**\n * Alignment pattern are fixed reference pattern in defined positions\n * in a matrix symbology, which enables the decode software to re-synchronise\n * the coordinate mapping of the image modules in the event of moderate amounts\n * of distortion of the image.\n *\n * Alignment patterns are present only in QR Code symbols of version 2 or larger\n * and their number depends on the symbol version.\n */ const getSymbolSize = (__webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/qrcode/lib/core/utils.js\").getSymbolSize);\n/**\n * Calculate the row/column coordinates of the center module of each alignment pattern\n * for the specified QR Code version.\n *\n * The alignment patterns are positioned symmetrically on either side of the diagonal\n * running from the top left corner of the symbol to the bottom right corner.\n *\n * Since positions are simmetrical only half of the coordinates are returned.\n * Each item of the array will represent in turn the x and y coordinate.\n * @see {@link getPositions}\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinate\n */ exports.getRowColCoords = function getRowColCoords(version) {\n    if (version === 1) return [];\n    const posCount = Math.floor(version / 7) + 2;\n    const size = getSymbolSize(version);\n    const intervals = size === 145 ? 26 : Math.ceil((size - 13) / (2 * posCount - 2)) * 2;\n    const positions = [\n        size - 7\n    ] // Last coord is always (size - 7)\n    ;\n    for(let i = 1; i < posCount - 1; i++){\n        positions[i] = positions[i - 1] - intervals;\n    }\n    positions.push(6) // First coord is always 6\n    ;\n    return positions.reverse();\n};\n/**\n * Returns an array containing the positions of each alignment pattern.\n * Each array's element represent the center point of the pattern as (x, y) coordinates\n *\n * Coordinates are calculated expanding the row/column coordinates returned by {@link getRowColCoords}\n * and filtering out the items that overlaps with finder pattern\n *\n * @example\n * For a Version 7 symbol {@link getRowColCoords} returns values 6, 22 and 38.\n * The alignment patterns, therefore, are to be centered on (row, column)\n * positions (6,22), (22,6), (22,22), (22,38), (38,22), (38,38).\n * Note that the coordinates (6,6), (6,38), (38,6) are occupied by finder patterns\n * and are not therefore used for alignment patterns.\n *\n * let pos = getPositions(7)\n * // [[6,22], [22,6], [22,22], [22,38], [38,22], [38,38]]\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */ exports.getPositions = function getPositions(version) {\n    const coords = [];\n    const pos = exports.getRowColCoords(version);\n    const posLength = pos.length;\n    for(let i = 0; i < posLength; i++){\n        for(let j = 0; j < posLength; j++){\n            // Skip if position is occupied by finder patterns\n            if (i === 0 && j === 0 || // top-left\n            i === 0 && j === posLength - 1 || // bottom-left\n            i === posLength - 1 && j === 0) {\n                continue;\n            }\n            coords.push([\n                pos[i],\n                pos[j]\n            ]);\n        }\n    }\n    return coords;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/alignment-pattern.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/alphanumeric-data.js":
/*!***********************************************************!*\
  !*** ./node_modules/qrcode/lib/core/alphanumeric-data.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const Mode = __webpack_require__(/*! ./mode */ \"(ssr)/./node_modules/qrcode/lib/core/mode.js\");\n/**\n * Array of characters available in alphanumeric mode\n *\n * As per QR Code specification, to each character\n * is assigned a value from 0 to 44 which in this case coincides\n * with the array index\n *\n * @type {Array}\n */ const ALPHA_NUM_CHARS = [\n    \"0\",\n    \"1\",\n    \"2\",\n    \"3\",\n    \"4\",\n    \"5\",\n    \"6\",\n    \"7\",\n    \"8\",\n    \"9\",\n    \"A\",\n    \"B\",\n    \"C\",\n    \"D\",\n    \"E\",\n    \"F\",\n    \"G\",\n    \"H\",\n    \"I\",\n    \"J\",\n    \"K\",\n    \"L\",\n    \"M\",\n    \"N\",\n    \"O\",\n    \"P\",\n    \"Q\",\n    \"R\",\n    \"S\",\n    \"T\",\n    \"U\",\n    \"V\",\n    \"W\",\n    \"X\",\n    \"Y\",\n    \"Z\",\n    \" \",\n    \"$\",\n    \"%\",\n    \"*\",\n    \"+\",\n    \"-\",\n    \".\",\n    \"/\",\n    \":\"\n];\nfunction AlphanumericData(data) {\n    this.mode = Mode.ALPHANUMERIC;\n    this.data = data;\n}\nAlphanumericData.getBitsLength = function getBitsLength(length) {\n    return 11 * Math.floor(length / 2) + 6 * (length % 2);\n};\nAlphanumericData.prototype.getLength = function getLength() {\n    return this.data.length;\n};\nAlphanumericData.prototype.getBitsLength = function getBitsLength() {\n    return AlphanumericData.getBitsLength(this.data.length);\n};\nAlphanumericData.prototype.write = function write(bitBuffer) {\n    let i;\n    // Input data characters are divided into groups of two characters\n    // and encoded as 11-bit binary codes.\n    for(i = 0; i + 2 <= this.data.length; i += 2){\n        // The character value of the first character is multiplied by 45\n        let value = ALPHA_NUM_CHARS.indexOf(this.data[i]) * 45;\n        // The character value of the second digit is added to the product\n        value += ALPHA_NUM_CHARS.indexOf(this.data[i + 1]);\n        // The sum is then stored as 11-bit binary number\n        bitBuffer.put(value, 11);\n    }\n    // If the number of input data characters is not a multiple of two,\n    // the character value of the final character is encoded as a 6-bit binary number.\n    if (this.data.length % 2) {\n        bitBuffer.put(ALPHA_NUM_CHARS.indexOf(this.data[i]), 6);\n    }\n};\nmodule.exports = AlphanumericData;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcXJjb2RlL2xpYi9jb3JlL2FscGhhbnVtZXJpYy1kYXRhLmpzIiwibWFwcGluZ3MiOiJBQUFBLE1BQU1BLE9BQU9DLG1CQUFPQSxDQUFDO0FBRXJCOzs7Ozs7OztDQVFDLEdBQ0QsTUFBTUMsa0JBQWtCO0lBQ3RCO0lBQUs7SUFBSztJQUFLO0lBQUs7SUFBSztJQUFLO0lBQUs7SUFBSztJQUFLO0lBQzdDO0lBQUs7SUFBSztJQUFLO0lBQUs7SUFBSztJQUFLO0lBQUs7SUFBSztJQUFLO0lBQUs7SUFBSztJQUFLO0lBQzVEO0lBQUs7SUFBSztJQUFLO0lBQUs7SUFBSztJQUFLO0lBQUs7SUFBSztJQUFLO0lBQUs7SUFBSztJQUFLO0lBQzVEO0lBQUs7SUFBSztJQUFLO0lBQUs7SUFBSztJQUFLO0lBQUs7SUFBSztDQUN6QztBQUVELFNBQVNDLGlCQUFrQkMsSUFBSTtJQUM3QixJQUFJLENBQUNDLElBQUksR0FBR0wsS0FBS00sWUFBWTtJQUM3QixJQUFJLENBQUNGLElBQUksR0FBR0E7QUFDZDtBQUVBRCxpQkFBaUJJLGFBQWEsR0FBRyxTQUFTQSxjQUFlQyxNQUFNO0lBQzdELE9BQU8sS0FBS0MsS0FBS0MsS0FBSyxDQUFDRixTQUFTLEtBQUssSUFBS0EsQ0FBQUEsU0FBUztBQUNyRDtBQUVBTCxpQkFBaUJRLFNBQVMsQ0FBQ0MsU0FBUyxHQUFHLFNBQVNBO0lBQzlDLE9BQU8sSUFBSSxDQUFDUixJQUFJLENBQUNJLE1BQU07QUFDekI7QUFFQUwsaUJBQWlCUSxTQUFTLENBQUNKLGFBQWEsR0FBRyxTQUFTQTtJQUNsRCxPQUFPSixpQkFBaUJJLGFBQWEsQ0FBQyxJQUFJLENBQUNILElBQUksQ0FBQ0ksTUFBTTtBQUN4RDtBQUVBTCxpQkFBaUJRLFNBQVMsQ0FBQ0UsS0FBSyxHQUFHLFNBQVNBLE1BQU9DLFNBQVM7SUFDMUQsSUFBSUM7SUFFSixrRUFBa0U7SUFDbEUsc0NBQXNDO0lBQ3RDLElBQUtBLElBQUksR0FBR0EsSUFBSSxLQUFLLElBQUksQ0FBQ1gsSUFBSSxDQUFDSSxNQUFNLEVBQUVPLEtBQUssRUFBRztRQUM3QyxpRUFBaUU7UUFDakUsSUFBSUMsUUFBUWQsZ0JBQWdCZSxPQUFPLENBQUMsSUFBSSxDQUFDYixJQUFJLENBQUNXLEVBQUUsSUFBSTtRQUVwRCxrRUFBa0U7UUFDbEVDLFNBQVNkLGdCQUFnQmUsT0FBTyxDQUFDLElBQUksQ0FBQ2IsSUFBSSxDQUFDVyxJQUFJLEVBQUU7UUFFakQsaURBQWlEO1FBQ2pERCxVQUFVSSxHQUFHLENBQUNGLE9BQU87SUFDdkI7SUFFQSxtRUFBbUU7SUFDbkUsa0ZBQWtGO0lBQ2xGLElBQUksSUFBSSxDQUFDWixJQUFJLENBQUNJLE1BQU0sR0FBRyxHQUFHO1FBQ3hCTSxVQUFVSSxHQUFHLENBQUNoQixnQkFBZ0JlLE9BQU8sQ0FBQyxJQUFJLENBQUNiLElBQUksQ0FBQ1csRUFBRSxHQUFHO0lBQ3ZEO0FBQ0Y7QUFFQUksT0FBT0MsT0FBTyxHQUFHakIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iaGVlbWRpbmUvLi9ub2RlX21vZHVsZXMvcXJjb2RlL2xpYi9jb3JlL2FscGhhbnVtZXJpYy1kYXRhLmpzPzI3NzgiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgTW9kZSA9IHJlcXVpcmUoJy4vbW9kZScpXG5cbi8qKlxuICogQXJyYXkgb2YgY2hhcmFjdGVycyBhdmFpbGFibGUgaW4gYWxwaGFudW1lcmljIG1vZGVcbiAqXG4gKiBBcyBwZXIgUVIgQ29kZSBzcGVjaWZpY2F0aW9uLCB0byBlYWNoIGNoYXJhY3RlclxuICogaXMgYXNzaWduZWQgYSB2YWx1ZSBmcm9tIDAgdG8gNDQgd2hpY2ggaW4gdGhpcyBjYXNlIGNvaW5jaWRlc1xuICogd2l0aCB0aGUgYXJyYXkgaW5kZXhcbiAqXG4gKiBAdHlwZSB7QXJyYXl9XG4gKi9cbmNvbnN0IEFMUEhBX05VTV9DSEFSUyA9IFtcbiAgJzAnLCAnMScsICcyJywgJzMnLCAnNCcsICc1JywgJzYnLCAnNycsICc4JywgJzknLFxuICAnQScsICdCJywgJ0MnLCAnRCcsICdFJywgJ0YnLCAnRycsICdIJywgJ0knLCAnSicsICdLJywgJ0wnLCAnTScsXG4gICdOJywgJ08nLCAnUCcsICdRJywgJ1InLCAnUycsICdUJywgJ1UnLCAnVicsICdXJywgJ1gnLCAnWScsICdaJyxcbiAgJyAnLCAnJCcsICclJywgJyonLCAnKycsICctJywgJy4nLCAnLycsICc6J1xuXVxuXG5mdW5jdGlvbiBBbHBoYW51bWVyaWNEYXRhIChkYXRhKSB7XG4gIHRoaXMubW9kZSA9IE1vZGUuQUxQSEFOVU1FUklDXG4gIHRoaXMuZGF0YSA9IGRhdGFcbn1cblxuQWxwaGFudW1lcmljRGF0YS5nZXRCaXRzTGVuZ3RoID0gZnVuY3Rpb24gZ2V0Qml0c0xlbmd0aCAobGVuZ3RoKSB7XG4gIHJldHVybiAxMSAqIE1hdGguZmxvb3IobGVuZ3RoIC8gMikgKyA2ICogKGxlbmd0aCAlIDIpXG59XG5cbkFscGhhbnVtZXJpY0RhdGEucHJvdG90eXBlLmdldExlbmd0aCA9IGZ1bmN0aW9uIGdldExlbmd0aCAoKSB7XG4gIHJldHVybiB0aGlzLmRhdGEubGVuZ3RoXG59XG5cbkFscGhhbnVtZXJpY0RhdGEucHJvdG90eXBlLmdldEJpdHNMZW5ndGggPSBmdW5jdGlvbiBnZXRCaXRzTGVuZ3RoICgpIHtcbiAgcmV0dXJuIEFscGhhbnVtZXJpY0RhdGEuZ2V0Qml0c0xlbmd0aCh0aGlzLmRhdGEubGVuZ3RoKVxufVxuXG5BbHBoYW51bWVyaWNEYXRhLnByb3RvdHlwZS53cml0ZSA9IGZ1bmN0aW9uIHdyaXRlIChiaXRCdWZmZXIpIHtcbiAgbGV0IGlcblxuICAvLyBJbnB1dCBkYXRhIGNoYXJhY3RlcnMgYXJlIGRpdmlkZWQgaW50byBncm91cHMgb2YgdHdvIGNoYXJhY3RlcnNcbiAgLy8gYW5kIGVuY29kZWQgYXMgMTEtYml0IGJpbmFyeSBjb2Rlcy5cbiAgZm9yIChpID0gMDsgaSArIDIgPD0gdGhpcy5kYXRhLmxlbmd0aDsgaSArPSAyKSB7XG4gICAgLy8gVGhlIGNoYXJhY3RlciB2YWx1ZSBvZiB0aGUgZmlyc3QgY2hhcmFjdGVyIGlzIG11bHRpcGxpZWQgYnkgNDVcbiAgICBsZXQgdmFsdWUgPSBBTFBIQV9OVU1fQ0hBUlMuaW5kZXhPZih0aGlzLmRhdGFbaV0pICogNDVcblxuICAgIC8vIFRoZSBjaGFyYWN0ZXIgdmFsdWUgb2YgdGhlIHNlY29uZCBkaWdpdCBpcyBhZGRlZCB0byB0aGUgcHJvZHVjdFxuICAgIHZhbHVlICs9IEFMUEhBX05VTV9DSEFSUy5pbmRleE9mKHRoaXMuZGF0YVtpICsgMV0pXG5cbiAgICAvLyBUaGUgc3VtIGlzIHRoZW4gc3RvcmVkIGFzIDExLWJpdCBiaW5hcnkgbnVtYmVyXG4gICAgYml0QnVmZmVyLnB1dCh2YWx1ZSwgMTEpXG4gIH1cblxuICAvLyBJZiB0aGUgbnVtYmVyIG9mIGlucHV0IGRhdGEgY2hhcmFjdGVycyBpcyBub3QgYSBtdWx0aXBsZSBvZiB0d28sXG4gIC8vIHRoZSBjaGFyYWN0ZXIgdmFsdWUgb2YgdGhlIGZpbmFsIGNoYXJhY3RlciBpcyBlbmNvZGVkIGFzIGEgNi1iaXQgYmluYXJ5IG51bWJlci5cbiAgaWYgKHRoaXMuZGF0YS5sZW5ndGggJSAyKSB7XG4gICAgYml0QnVmZmVyLnB1dChBTFBIQV9OVU1fQ0hBUlMuaW5kZXhPZih0aGlzLmRhdGFbaV0pLCA2KVxuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gQWxwaGFudW1lcmljRGF0YVxuIl0sIm5hbWVzIjpbIk1vZGUiLCJyZXF1aXJlIiwiQUxQSEFfTlVNX0NIQVJTIiwiQWxwaGFudW1lcmljRGF0YSIsImRhdGEiLCJtb2RlIiwiQUxQSEFOVU1FUklDIiwiZ2V0Qml0c0xlbmd0aCIsImxlbmd0aCIsIk1hdGgiLCJmbG9vciIsInByb3RvdHlwZSIsImdldExlbmd0aCIsIndyaXRlIiwiYml0QnVmZmVyIiwiaSIsInZhbHVlIiwiaW5kZXhPZiIsInB1dCIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/alphanumeric-data.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/bit-buffer.js":
/*!****************************************************!*\
  !*** ./node_modules/qrcode/lib/core/bit-buffer.js ***!
  \****************************************************/
/***/ ((module) => {

eval("function BitBuffer() {\n    this.buffer = [];\n    this.length = 0;\n}\nBitBuffer.prototype = {\n    get: function(index) {\n        const bufIndex = Math.floor(index / 8);\n        return (this.buffer[bufIndex] >>> 7 - index % 8 & 1) === 1;\n    },\n    put: function(num, length) {\n        for(let i = 0; i < length; i++){\n            this.putBit((num >>> length - i - 1 & 1) === 1);\n        }\n    },\n    getLengthInBits: function() {\n        return this.length;\n    },\n    putBit: function(bit) {\n        const bufIndex = Math.floor(this.length / 8);\n        if (this.buffer.length <= bufIndex) {\n            this.buffer.push(0);\n        }\n        if (bit) {\n            this.buffer[bufIndex] |= 0x80 >>> this.length % 8;\n        }\n        this.length++;\n    }\n};\nmodule.exports = BitBuffer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/bit-buffer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/bit-matrix.js":
/*!****************************************************!*\
  !*** ./node_modules/qrcode/lib/core/bit-matrix.js ***!
  \****************************************************/
/***/ ((module) => {

eval("/**\n * Helper class to handle QR Code symbol modules\n *\n * @param {Number} size Symbol size\n */ function BitMatrix(size) {\n    if (!size || size < 1) {\n        throw new Error(\"BitMatrix size must be defined and greater than 0\");\n    }\n    this.size = size;\n    this.data = new Uint8Array(size * size);\n    this.reservedBit = new Uint8Array(size * size);\n}\n/**\n * Set bit value at specified location\n * If reserved flag is set, this bit will be ignored during masking process\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n * @param {Boolean} reserved\n */ BitMatrix.prototype.set = function(row, col, value, reserved) {\n    const index = row * this.size + col;\n    this.data[index] = value;\n    if (reserved) this.reservedBit[index] = true;\n};\n/**\n * Returns bit value at specified location\n *\n * @param  {Number}  row\n * @param  {Number}  col\n * @return {Boolean}\n */ BitMatrix.prototype.get = function(row, col) {\n    return this.data[row * this.size + col];\n};\n/**\n * Applies xor operator at specified location\n * (used during masking process)\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n */ BitMatrix.prototype.xor = function(row, col, value) {\n    this.data[row * this.size + col] ^= value;\n};\n/**\n * Check if bit at specified location is reserved\n *\n * @param {Number}   row\n * @param {Number}   col\n * @return {Boolean}\n */ BitMatrix.prototype.isReserved = function(row, col) {\n    return this.reservedBit[row * this.size + col];\n};\nmodule.exports = BitMatrix;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/bit-matrix.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/byte-data.js":
/*!***************************************************!*\
  !*** ./node_modules/qrcode/lib/core/byte-data.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const encodeUtf8 = __webpack_require__(/*! encode-utf8 */ \"(ssr)/./node_modules/encode-utf8/index.js\");\nconst Mode = __webpack_require__(/*! ./mode */ \"(ssr)/./node_modules/qrcode/lib/core/mode.js\");\nfunction ByteData(data) {\n    this.mode = Mode.BYTE;\n    if (typeof data === \"string\") {\n        data = encodeUtf8(data);\n    }\n    this.data = new Uint8Array(data);\n}\nByteData.getBitsLength = function getBitsLength(length) {\n    return length * 8;\n};\nByteData.prototype.getLength = function getLength() {\n    return this.data.length;\n};\nByteData.prototype.getBitsLength = function getBitsLength() {\n    return ByteData.getBitsLength(this.data.length);\n};\nByteData.prototype.write = function(bitBuffer) {\n    for(let i = 0, l = this.data.length; i < l; i++){\n        bitBuffer.put(this.data[i], 8);\n    }\n};\nmodule.exports = ByteData;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/byte-data.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/error-correction-code.js":
/*!***************************************************************!*\
  !*** ./node_modules/qrcode/lib/core/error-correction-code.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const ECLevel = __webpack_require__(/*! ./error-correction-level */ \"(ssr)/./node_modules/qrcode/lib/core/error-correction-level.js\");\nconst EC_BLOCKS_TABLE = [\n    // L  M  Q  H\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    2,\n    2,\n    1,\n    2,\n    2,\n    4,\n    1,\n    2,\n    4,\n    4,\n    2,\n    4,\n    4,\n    4,\n    2,\n    4,\n    6,\n    5,\n    2,\n    4,\n    6,\n    6,\n    2,\n    5,\n    8,\n    8,\n    4,\n    5,\n    8,\n    8,\n    4,\n    5,\n    8,\n    11,\n    4,\n    8,\n    10,\n    11,\n    4,\n    9,\n    12,\n    16,\n    4,\n    9,\n    16,\n    16,\n    6,\n    10,\n    12,\n    18,\n    6,\n    10,\n    17,\n    16,\n    6,\n    11,\n    16,\n    19,\n    6,\n    13,\n    18,\n    21,\n    7,\n    14,\n    21,\n    25,\n    8,\n    16,\n    20,\n    25,\n    8,\n    17,\n    23,\n    25,\n    9,\n    17,\n    23,\n    34,\n    9,\n    18,\n    25,\n    30,\n    10,\n    20,\n    27,\n    32,\n    12,\n    21,\n    29,\n    35,\n    12,\n    23,\n    34,\n    37,\n    12,\n    25,\n    34,\n    40,\n    13,\n    26,\n    35,\n    42,\n    14,\n    28,\n    38,\n    45,\n    15,\n    29,\n    40,\n    48,\n    16,\n    31,\n    43,\n    51,\n    17,\n    33,\n    45,\n    54,\n    18,\n    35,\n    48,\n    57,\n    19,\n    37,\n    51,\n    60,\n    19,\n    38,\n    53,\n    63,\n    20,\n    40,\n    56,\n    66,\n    21,\n    43,\n    59,\n    70,\n    22,\n    45,\n    62,\n    74,\n    24,\n    47,\n    65,\n    77,\n    25,\n    49,\n    68,\n    81\n];\nconst EC_CODEWORDS_TABLE = [\n    // L  M  Q  H\n    7,\n    10,\n    13,\n    17,\n    10,\n    16,\n    22,\n    28,\n    15,\n    26,\n    36,\n    44,\n    20,\n    36,\n    52,\n    64,\n    26,\n    48,\n    72,\n    88,\n    36,\n    64,\n    96,\n    112,\n    40,\n    72,\n    108,\n    130,\n    48,\n    88,\n    132,\n    156,\n    60,\n    110,\n    160,\n    192,\n    72,\n    130,\n    192,\n    224,\n    80,\n    150,\n    224,\n    264,\n    96,\n    176,\n    260,\n    308,\n    104,\n    198,\n    288,\n    352,\n    120,\n    216,\n    320,\n    384,\n    132,\n    240,\n    360,\n    432,\n    144,\n    280,\n    408,\n    480,\n    168,\n    308,\n    448,\n    532,\n    180,\n    338,\n    504,\n    588,\n    196,\n    364,\n    546,\n    650,\n    224,\n    416,\n    600,\n    700,\n    224,\n    442,\n    644,\n    750,\n    252,\n    476,\n    690,\n    816,\n    270,\n    504,\n    750,\n    900,\n    300,\n    560,\n    810,\n    960,\n    312,\n    588,\n    870,\n    1050,\n    336,\n    644,\n    952,\n    1110,\n    360,\n    700,\n    1020,\n    1200,\n    390,\n    728,\n    1050,\n    1260,\n    420,\n    784,\n    1140,\n    1350,\n    450,\n    812,\n    1200,\n    1440,\n    480,\n    868,\n    1290,\n    1530,\n    510,\n    924,\n    1350,\n    1620,\n    540,\n    980,\n    1440,\n    1710,\n    570,\n    1036,\n    1530,\n    1800,\n    570,\n    1064,\n    1590,\n    1890,\n    600,\n    1120,\n    1680,\n    1980,\n    630,\n    1204,\n    1770,\n    2100,\n    660,\n    1260,\n    1860,\n    2220,\n    720,\n    1316,\n    1950,\n    2310,\n    750,\n    1372,\n    2040,\n    2430\n];\n/**\r\n * Returns the number of error correction block that the QR Code should contain\r\n * for the specified version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction blocks\r\n */ exports.getBlocksCount = function getBlocksCount(version, errorCorrectionLevel) {\n    switch(errorCorrectionLevel){\n        case ECLevel.L:\n            return EC_BLOCKS_TABLE[(version - 1) * 4 + 0];\n        case ECLevel.M:\n            return EC_BLOCKS_TABLE[(version - 1) * 4 + 1];\n        case ECLevel.Q:\n            return EC_BLOCKS_TABLE[(version - 1) * 4 + 2];\n        case ECLevel.H:\n            return EC_BLOCKS_TABLE[(version - 1) * 4 + 3];\n        default:\n            return undefined;\n    }\n};\n/**\r\n * Returns the number of error correction codewords to use for the specified\r\n * version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction codewords\r\n */ exports.getTotalCodewordsCount = function getTotalCodewordsCount(version, errorCorrectionLevel) {\n    switch(errorCorrectionLevel){\n        case ECLevel.L:\n            return EC_CODEWORDS_TABLE[(version - 1) * 4 + 0];\n        case ECLevel.M:\n            return EC_CODEWORDS_TABLE[(version - 1) * 4 + 1];\n        case ECLevel.Q:\n            return EC_CODEWORDS_TABLE[(version - 1) * 4 + 2];\n        case ECLevel.H:\n            return EC_CODEWORDS_TABLE[(version - 1) * 4 + 3];\n        default:\n            return undefined;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/error-correction-code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/error-correction-level.js":
/*!****************************************************************!*\
  !*** ./node_modules/qrcode/lib/core/error-correction-level.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("exports.L = {\n    bit: 1\n};\nexports.M = {\n    bit: 0\n};\nexports.Q = {\n    bit: 3\n};\nexports.H = {\n    bit: 2\n};\nfunction fromString(string) {\n    if (typeof string !== \"string\") {\n        throw new Error(\"Param is not a string\");\n    }\n    const lcStr = string.toLowerCase();\n    switch(lcStr){\n        case \"l\":\n        case \"low\":\n            return exports.L;\n        case \"m\":\n        case \"medium\":\n            return exports.M;\n        case \"q\":\n        case \"quartile\":\n            return exports.Q;\n        case \"h\":\n        case \"high\":\n            return exports.H;\n        default:\n            throw new Error(\"Unknown EC Level: \" + string);\n    }\n}\nexports.isValid = function isValid(level) {\n    return level && typeof level.bit !== \"undefined\" && level.bit >= 0 && level.bit < 4;\n};\nexports.from = function from(value, defaultValue) {\n    if (exports.isValid(value)) {\n        return value;\n    }\n    try {\n        return fromString(value);\n    } catch (e) {\n        return defaultValue;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/error-correction-level.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/finder-pattern.js":
/*!********************************************************!*\
  !*** ./node_modules/qrcode/lib/core/finder-pattern.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const getSymbolSize = (__webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/qrcode/lib/core/utils.js\").getSymbolSize);\nconst FINDER_PATTERN_SIZE = 7;\n/**\n * Returns an array containing the positions of each finder pattern.\n * Each array's element represent the top-left point of the pattern as (x, y) coordinates\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */ exports.getPositions = function getPositions(version) {\n    const size = getSymbolSize(version);\n    return [\n        // top-left\n        [\n            0,\n            0\n        ],\n        // top-right\n        [\n            size - FINDER_PATTERN_SIZE,\n            0\n        ],\n        // bottom-left\n        [\n            0,\n            size - FINDER_PATTERN_SIZE\n        ]\n    ];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcXJjb2RlL2xpYi9jb3JlL2ZpbmRlci1wYXR0ZXJuLmpzIiwibWFwcGluZ3MiOiJBQUFBLE1BQU1BLGdCQUFnQkMsbUdBQWdDO0FBQ3RELE1BQU1DLHNCQUFzQjtBQUU1Qjs7Ozs7O0NBTUMsR0FDREMsb0JBQW9CLEdBQUcsU0FBU0MsYUFBY0MsT0FBTztJQUNuRCxNQUFNQyxPQUFPTixjQUFjSztJQUUzQixPQUFPO1FBQ0wsV0FBVztRQUNYO1lBQUM7WUFBRztTQUFFO1FBQ04sWUFBWTtRQUNaO1lBQUNDLE9BQU9KO1lBQXFCO1NBQUU7UUFDL0IsY0FBYztRQUNkO1lBQUM7WUFBR0ksT0FBT0o7U0FBb0I7S0FDaEM7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL2JoZWVtZGluZS8uL25vZGVfbW9kdWxlcy9xcmNvZGUvbGliL2NvcmUvZmluZGVyLXBhdHRlcm4uanM/YTAzNyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBnZXRTeW1ib2xTaXplID0gcmVxdWlyZSgnLi91dGlscycpLmdldFN5bWJvbFNpemVcbmNvbnN0IEZJTkRFUl9QQVRURVJOX1NJWkUgPSA3XG5cbi8qKlxuICogUmV0dXJucyBhbiBhcnJheSBjb250YWluaW5nIHRoZSBwb3NpdGlvbnMgb2YgZWFjaCBmaW5kZXIgcGF0dGVybi5cbiAqIEVhY2ggYXJyYXkncyBlbGVtZW50IHJlcHJlc2VudCB0aGUgdG9wLWxlZnQgcG9pbnQgb2YgdGhlIHBhdHRlcm4gYXMgKHgsIHkpIGNvb3JkaW5hdGVzXG4gKlxuICogQHBhcmFtICB7TnVtYmVyfSB2ZXJzaW9uIFFSIENvZGUgdmVyc2lvblxuICogQHJldHVybiB7QXJyYXl9ICAgICAgICAgIEFycmF5IG9mIGNvb3JkaW5hdGVzXG4gKi9cbmV4cG9ydHMuZ2V0UG9zaXRpb25zID0gZnVuY3Rpb24gZ2V0UG9zaXRpb25zICh2ZXJzaW9uKSB7XG4gIGNvbnN0IHNpemUgPSBnZXRTeW1ib2xTaXplKHZlcnNpb24pXG5cbiAgcmV0dXJuIFtcbiAgICAvLyB0b3AtbGVmdFxuICAgIFswLCAwXSxcbiAgICAvLyB0b3AtcmlnaHRcbiAgICBbc2l6ZSAtIEZJTkRFUl9QQVRURVJOX1NJWkUsIDBdLFxuICAgIC8vIGJvdHRvbS1sZWZ0XG4gICAgWzAsIHNpemUgLSBGSU5ERVJfUEFUVEVSTl9TSVpFXVxuICBdXG59XG4iXSwibmFtZXMiOlsiZ2V0U3ltYm9sU2l6ZSIsInJlcXVpcmUiLCJGSU5ERVJfUEFUVEVSTl9TSVpFIiwiZXhwb3J0cyIsImdldFBvc2l0aW9ucyIsInZlcnNpb24iLCJzaXplIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/finder-pattern.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/format-info.js":
/*!*****************************************************!*\
  !*** ./node_modules/qrcode/lib/core/format-info.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const Utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/qrcode/lib/core/utils.js\");\nconst G15 = 1 << 10 | 1 << 8 | 1 << 5 | 1 << 4 | 1 << 2 | 1 << 1 | 1 << 0;\nconst G15_MASK = 1 << 14 | 1 << 12 | 1 << 10 | 1 << 4 | 1 << 1;\nconst G15_BCH = Utils.getBCHDigit(G15);\n/**\n * Returns format information with relative error correction bits\n *\n * The format information is a 15-bit sequence containing 5 data bits,\n * with 10 error correction bits calculated using the (15, 5) BCH code.\n *\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Number} mask                 Mask pattern\n * @return {Number}                      Encoded format information bits\n */ exports.getEncodedBits = function getEncodedBits(errorCorrectionLevel, mask) {\n    const data = errorCorrectionLevel.bit << 3 | mask;\n    let d = data << 10;\n    while(Utils.getBCHDigit(d) - G15_BCH >= 0){\n        d ^= G15 << Utils.getBCHDigit(d) - G15_BCH;\n    }\n    // xor final data with mask pattern in order to ensure that\n    // no combination of Error Correction Level and data mask pattern\n    // will result in an all-zero data string\n    return (data << 10 | d) ^ G15_MASK;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcXJjb2RlL2xpYi9jb3JlL2Zvcm1hdC1pbmZvLmpzIiwibWFwcGluZ3MiOiJBQUFBLE1BQU1BLFFBQVFDLG1CQUFPQSxDQUFDO0FBRXRCLE1BQU1DLE1BQU0sS0FBTSxLQUFPLEtBQUssSUFBTSxLQUFLLElBQU0sS0FBSyxJQUFNLEtBQUssSUFBTSxLQUFLLElBQU0sS0FBSztBQUNyRixNQUFNQyxXQUFXLEtBQU0sS0FBTyxLQUFLLEtBQU8sS0FBSyxLQUFPLEtBQUssSUFBTSxLQUFLO0FBQ3RFLE1BQU1DLFVBQVVKLE1BQU1LLFdBQVcsQ0FBQ0g7QUFFbEM7Ozs7Ozs7OztDQVNDLEdBQ0RJLHNCQUFzQixHQUFHLFNBQVNDLGVBQWdCQyxvQkFBb0IsRUFBRUMsSUFBSTtJQUMxRSxNQUFNQyxPQUFRLHFCQUFzQkMsR0FBRyxJQUFJLElBQUtGO0lBQ2hELElBQUlHLElBQUlGLFFBQVE7SUFFaEIsTUFBT1YsTUFBTUssV0FBVyxDQUFDTyxLQUFLUixXQUFXLEVBQUc7UUFDMUNRLEtBQU1WLE9BQVFGLE1BQU1LLFdBQVcsQ0FBQ08sS0FBS1I7SUFDdkM7SUFFQSwyREFBMkQ7SUFDM0QsaUVBQWlFO0lBQ2pFLHlDQUF5QztJQUN6QyxPQUFPLENBQUMsUUFBUyxLQUFNUSxDQUFBQSxJQUFLVDtBQUM5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2JoZWVtZGluZS8uL25vZGVfbW9kdWxlcy9xcmNvZGUvbGliL2NvcmUvZm9ybWF0LWluZm8uanM/OTIwOSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBVdGlscyA9IHJlcXVpcmUoJy4vdXRpbHMnKVxuXG5jb25zdCBHMTUgPSAoMSA8PCAxMCkgfCAoMSA8PCA4KSB8ICgxIDw8IDUpIHwgKDEgPDwgNCkgfCAoMSA8PCAyKSB8ICgxIDw8IDEpIHwgKDEgPDwgMClcbmNvbnN0IEcxNV9NQVNLID0gKDEgPDwgMTQpIHwgKDEgPDwgMTIpIHwgKDEgPDwgMTApIHwgKDEgPDwgNCkgfCAoMSA8PCAxKVxuY29uc3QgRzE1X0JDSCA9IFV0aWxzLmdldEJDSERpZ2l0KEcxNSlcblxuLyoqXG4gKiBSZXR1cm5zIGZvcm1hdCBpbmZvcm1hdGlvbiB3aXRoIHJlbGF0aXZlIGVycm9yIGNvcnJlY3Rpb24gYml0c1xuICpcbiAqIFRoZSBmb3JtYXQgaW5mb3JtYXRpb24gaXMgYSAxNS1iaXQgc2VxdWVuY2UgY29udGFpbmluZyA1IGRhdGEgYml0cyxcbiAqIHdpdGggMTAgZXJyb3IgY29ycmVjdGlvbiBiaXRzIGNhbGN1bGF0ZWQgdXNpbmcgdGhlICgxNSwgNSkgQkNIIGNvZGUuXG4gKlxuICogQHBhcmFtICB7TnVtYmVyfSBlcnJvckNvcnJlY3Rpb25MZXZlbCBFcnJvciBjb3JyZWN0aW9uIGxldmVsXG4gKiBAcGFyYW0gIHtOdW1iZXJ9IG1hc2sgICAgICAgICAgICAgICAgIE1hc2sgcGF0dGVyblxuICogQHJldHVybiB7TnVtYmVyfSAgICAgICAgICAgICAgICAgICAgICBFbmNvZGVkIGZvcm1hdCBpbmZvcm1hdGlvbiBiaXRzXG4gKi9cbmV4cG9ydHMuZ2V0RW5jb2RlZEJpdHMgPSBmdW5jdGlvbiBnZXRFbmNvZGVkQml0cyAoZXJyb3JDb3JyZWN0aW9uTGV2ZWwsIG1hc2spIHtcbiAgY29uc3QgZGF0YSA9ICgoZXJyb3JDb3JyZWN0aW9uTGV2ZWwuYml0IDw8IDMpIHwgbWFzaylcbiAgbGV0IGQgPSBkYXRhIDw8IDEwXG5cbiAgd2hpbGUgKFV0aWxzLmdldEJDSERpZ2l0KGQpIC0gRzE1X0JDSCA+PSAwKSB7XG4gICAgZCBePSAoRzE1IDw8IChVdGlscy5nZXRCQ0hEaWdpdChkKSAtIEcxNV9CQ0gpKVxuICB9XG5cbiAgLy8geG9yIGZpbmFsIGRhdGEgd2l0aCBtYXNrIHBhdHRlcm4gaW4gb3JkZXIgdG8gZW5zdXJlIHRoYXRcbiAgLy8gbm8gY29tYmluYXRpb24gb2YgRXJyb3IgQ29ycmVjdGlvbiBMZXZlbCBhbmQgZGF0YSBtYXNrIHBhdHRlcm5cbiAgLy8gd2lsbCByZXN1bHQgaW4gYW4gYWxsLXplcm8gZGF0YSBzdHJpbmdcbiAgcmV0dXJuICgoZGF0YSA8PCAxMCkgfCBkKSBeIEcxNV9NQVNLXG59XG4iXSwibmFtZXMiOlsiVXRpbHMiLCJyZXF1aXJlIiwiRzE1IiwiRzE1X01BU0siLCJHMTVfQkNIIiwiZ2V0QkNIRGlnaXQiLCJleHBvcnRzIiwiZ2V0RW5jb2RlZEJpdHMiLCJlcnJvckNvcnJlY3Rpb25MZXZlbCIsIm1hc2siLCJkYXRhIiwiYml0IiwiZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/format-info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/galois-field.js":
/*!******************************************************!*\
  !*** ./node_modules/qrcode/lib/core/galois-field.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("const EXP_TABLE = new Uint8Array(512);\nconst LOG_TABLE = new Uint8Array(256) /**\n * Precompute the log and anti-log tables for faster computation later\n *\n * For each possible value in the galois field 2^8, we will pre-compute\n * the logarithm and anti-logarithm (exponential) of this value\n *\n * ref {@link https://en.wikiversity.org/wiki/Reed%E2%80%93Solomon_codes_for_coders#Introduction_to_mathematical_fields}\n */ ;\n(function initTables() {\n    let x = 1;\n    for(let i = 0; i < 255; i++){\n        EXP_TABLE[i] = x;\n        LOG_TABLE[x] = i;\n        x <<= 1 // multiply by 2\n        ;\n        // The QR code specification says to use byte-wise modulo 100011101 arithmetic.\n        // This means that when a number is 256 or larger, it should be XORed with 0x11D.\n        if (x & 0x100) {\n            x ^= 0x11D;\n        }\n    }\n    // Optimization: double the size of the anti-log table so that we don't need to mod 255 to\n    // stay inside the bounds (because we will mainly use this table for the multiplication of\n    // two GF numbers, no more).\n    // @see {@link mul}\n    for(let i = 255; i < 512; i++){\n        EXP_TABLE[i] = EXP_TABLE[i - 255];\n    }\n})();\n/**\n * Returns log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */ exports.log = function log(n) {\n    if (n < 1) throw new Error(\"log(\" + n + \")\");\n    return LOG_TABLE[n];\n};\n/**\n * Returns anti-log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */ exports.exp = function exp(n) {\n    return EXP_TABLE[n];\n};\n/**\n * Multiplies two number inside Galois Field\n *\n * @param  {Number} x\n * @param  {Number} y\n * @return {Number}\n */ exports.mul = function mul(x, y) {\n    if (x === 0 || y === 0) return 0;\n    // should be EXP_TABLE[(LOG_TABLE[x] + LOG_TABLE[y]) % 255] if EXP_TABLE wasn't oversized\n    // @see {@link initTables}\n    return EXP_TABLE[LOG_TABLE[x] + LOG_TABLE[y]];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/galois-field.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/kanji-data.js":
/*!****************************************************!*\
  !*** ./node_modules/qrcode/lib/core/kanji-data.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const Mode = __webpack_require__(/*! ./mode */ \"(ssr)/./node_modules/qrcode/lib/core/mode.js\");\nconst Utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/qrcode/lib/core/utils.js\");\nfunction KanjiData(data) {\n    this.mode = Mode.KANJI;\n    this.data = data;\n}\nKanjiData.getBitsLength = function getBitsLength(length) {\n    return length * 13;\n};\nKanjiData.prototype.getLength = function getLength() {\n    return this.data.length;\n};\nKanjiData.prototype.getBitsLength = function getBitsLength() {\n    return KanjiData.getBitsLength(this.data.length);\n};\nKanjiData.prototype.write = function(bitBuffer) {\n    let i;\n    // In the Shift JIS system, Kanji characters are represented by a two byte combination.\n    // These byte values are shifted from the JIS X 0208 values.\n    // JIS X 0208 gives details of the shift coded representation.\n    for(i = 0; i < this.data.length; i++){\n        let value = Utils.toSJIS(this.data[i]);\n        // For characters with Shift JIS values from 0x8140 to 0x9FFC:\n        if (value >= 0x8140 && value <= 0x9FFC) {\n            // Subtract 0x8140 from Shift JIS value\n            value -= 0x8140;\n        // For characters with Shift JIS values from 0xE040 to 0xEBBF\n        } else if (value >= 0xE040 && value <= 0xEBBF) {\n            // Subtract 0xC140 from Shift JIS value\n            value -= 0xC140;\n        } else {\n            throw new Error(\"Invalid SJIS character: \" + this.data[i] + \"\\n\" + \"Make sure your charset is UTF-8\");\n        }\n        // Multiply most significant byte of result by 0xC0\n        // and add least significant byte to product\n        value = (value >>> 8 & 0xff) * 0xC0 + (value & 0xff);\n        // Convert result to a 13-bit binary string\n        bitBuffer.put(value, 13);\n    }\n};\nmodule.exports = KanjiData;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/kanji-data.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/mask-pattern.js":
/*!******************************************************!*\
  !*** ./node_modules/qrcode/lib/core/mask-pattern.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/**\n * Data mask pattern reference\n * @type {Object}\n */ exports.Patterns = {\n    PATTERN000: 0,\n    PATTERN001: 1,\n    PATTERN010: 2,\n    PATTERN011: 3,\n    PATTERN100: 4,\n    PATTERN101: 5,\n    PATTERN110: 6,\n    PATTERN111: 7\n};\n/**\n * Weighted penalty scores for the undesirable features\n * @type {Object}\n */ const PenaltyScores = {\n    N1: 3,\n    N2: 3,\n    N3: 40,\n    N4: 10\n};\n/**\n * Check if mask pattern value is valid\n *\n * @param  {Number}  mask    Mask pattern\n * @return {Boolean}         true if valid, false otherwise\n */ exports.isValid = function isValid(mask) {\n    return mask != null && mask !== \"\" && !isNaN(mask) && mask >= 0 && mask <= 7;\n};\n/**\n * Returns mask pattern from a value.\n * If value is not valid, returns undefined\n *\n * @param  {Number|String} value        Mask pattern value\n * @return {Number}                     Valid mask pattern or undefined\n */ exports.from = function from(value) {\n    return exports.isValid(value) ? parseInt(value, 10) : undefined;\n};\n/**\n* Find adjacent modules in row/column with the same color\n* and assign a penalty value.\n*\n* Points: N1 + i\n* i is the amount by which the number of adjacent modules of the same color exceeds 5\n*/ exports.getPenaltyN1 = function getPenaltyN1(data) {\n    const size = data.size;\n    let points = 0;\n    let sameCountCol = 0;\n    let sameCountRow = 0;\n    let lastCol = null;\n    let lastRow = null;\n    for(let row = 0; row < size; row++){\n        sameCountCol = sameCountRow = 0;\n        lastCol = lastRow = null;\n        for(let col = 0; col < size; col++){\n            let module = data.get(row, col);\n            if (module === lastCol) {\n                sameCountCol++;\n            } else {\n                if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5);\n                lastCol = module;\n                sameCountCol = 1;\n            }\n            module = data.get(col, row);\n            if (module === lastRow) {\n                sameCountRow++;\n            } else {\n                if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5);\n                lastRow = module;\n                sameCountRow = 1;\n            }\n        }\n        if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5);\n        if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5);\n    }\n    return points;\n};\n/**\n * Find 2x2 blocks with the same color and assign a penalty value\n *\n * Points: N2 * (m - 1) * (n - 1)\n */ exports.getPenaltyN2 = function getPenaltyN2(data) {\n    const size = data.size;\n    let points = 0;\n    for(let row = 0; row < size - 1; row++){\n        for(let col = 0; col < size - 1; col++){\n            const last = data.get(row, col) + data.get(row, col + 1) + data.get(row + 1, col) + data.get(row + 1, col + 1);\n            if (last === 4 || last === 0) points++;\n        }\n    }\n    return points * PenaltyScores.N2;\n};\n/**\n * Find 1:1:3:1:1 ratio (dark:light:dark:light:dark) pattern in row/column,\n * preceded or followed by light area 4 modules wide\n *\n * Points: N3 * number of pattern found\n */ exports.getPenaltyN3 = function getPenaltyN3(data) {\n    const size = data.size;\n    let points = 0;\n    let bitsCol = 0;\n    let bitsRow = 0;\n    for(let row = 0; row < size; row++){\n        bitsCol = bitsRow = 0;\n        for(let col = 0; col < size; col++){\n            bitsCol = bitsCol << 1 & 0x7FF | data.get(row, col);\n            if (col >= 10 && (bitsCol === 0x5D0 || bitsCol === 0x05D)) points++;\n            bitsRow = bitsRow << 1 & 0x7FF | data.get(col, row);\n            if (col >= 10 && (bitsRow === 0x5D0 || bitsRow === 0x05D)) points++;\n        }\n    }\n    return points * PenaltyScores.N3;\n};\n/**\n * Calculate proportion of dark modules in entire symbol\n *\n * Points: N4 * k\n *\n * k is the rating of the deviation of the proportion of dark modules\n * in the symbol from 50% in steps of 5%\n */ exports.getPenaltyN4 = function getPenaltyN4(data) {\n    let darkCount = 0;\n    const modulesCount = data.data.length;\n    for(let i = 0; i < modulesCount; i++)darkCount += data.data[i];\n    const k = Math.abs(Math.ceil(darkCount * 100 / modulesCount / 5) - 10);\n    return k * PenaltyScores.N4;\n};\n/**\n * Return mask value at given position\n *\n * @param  {Number} maskPattern Pattern reference value\n * @param  {Number} i           Row\n * @param  {Number} j           Column\n * @return {Boolean}            Mask value\n */ function getMaskAt(maskPattern, i, j) {\n    switch(maskPattern){\n        case exports.Patterns.PATTERN000:\n            return (i + j) % 2 === 0;\n        case exports.Patterns.PATTERN001:\n            return i % 2 === 0;\n        case exports.Patterns.PATTERN010:\n            return j % 3 === 0;\n        case exports.Patterns.PATTERN011:\n            return (i + j) % 3 === 0;\n        case exports.Patterns.PATTERN100:\n            return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 === 0;\n        case exports.Patterns.PATTERN101:\n            return i * j % 2 + i * j % 3 === 0;\n        case exports.Patterns.PATTERN110:\n            return (i * j % 2 + i * j % 3) % 2 === 0;\n        case exports.Patterns.PATTERN111:\n            return (i * j % 3 + (i + j) % 2) % 2 === 0;\n        default:\n            throw new Error(\"bad maskPattern:\" + maskPattern);\n    }\n}\n/**\n * Apply a mask pattern to a BitMatrix\n *\n * @param  {Number}    pattern Pattern reference number\n * @param  {BitMatrix} data    BitMatrix data\n */ exports.applyMask = function applyMask(pattern, data) {\n    const size = data.size;\n    for(let col = 0; col < size; col++){\n        for(let row = 0; row < size; row++){\n            if (data.isReserved(row, col)) continue;\n            data.xor(row, col, getMaskAt(pattern, row, col));\n        }\n    }\n};\n/**\n * Returns the best mask pattern for data\n *\n * @param  {BitMatrix} data\n * @return {Number} Mask pattern reference number\n */ exports.getBestMask = function getBestMask(data, setupFormatFunc) {\n    const numPatterns = Object.keys(exports.Patterns).length;\n    let bestPattern = 0;\n    let lowerPenalty = Infinity;\n    for(let p = 0; p < numPatterns; p++){\n        setupFormatFunc(p);\n        exports.applyMask(p, data);\n        // Calculate penalty\n        const penalty = exports.getPenaltyN1(data) + exports.getPenaltyN2(data) + exports.getPenaltyN3(data) + exports.getPenaltyN4(data);\n        // Undo previously applied mask\n        exports.applyMask(p, data);\n        if (penalty < lowerPenalty) {\n            lowerPenalty = penalty;\n            bestPattern = p;\n        }\n    }\n    return bestPattern;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/mask-pattern.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/mode.js":
/*!**********************************************!*\
  !*** ./node_modules/qrcode/lib/core/mode.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const VersionCheck = __webpack_require__(/*! ./version-check */ \"(ssr)/./node_modules/qrcode/lib/core/version-check.js\");\nconst Regex = __webpack_require__(/*! ./regex */ \"(ssr)/./node_modules/qrcode/lib/core/regex.js\");\n/**\n * Numeric mode encodes data from the decimal digit set (0 - 9)\n * (byte values 30HEX to 39HEX).\n * Normally, 3 data characters are represented by 10 bits.\n *\n * @type {Object}\n */ exports.NUMERIC = {\n    id: \"Numeric\",\n    bit: 1 << 0,\n    ccBits: [\n        10,\n        12,\n        14\n    ]\n};\n/**\n * Alphanumeric mode encodes data from a set of 45 characters,\n * i.e. 10 numeric digits (0 - 9),\n *      26 alphabetic characters (A - Z),\n *   and 9 symbols (SP, $, %, *, +, -, ., /, :).\n * Normally, two input characters are represented by 11 bits.\n *\n * @type {Object}\n */ exports.ALPHANUMERIC = {\n    id: \"Alphanumeric\",\n    bit: 1 << 1,\n    ccBits: [\n        9,\n        11,\n        13\n    ]\n};\n/**\n * In byte mode, data is encoded at 8 bits per character.\n *\n * @type {Object}\n */ exports.BYTE = {\n    id: \"Byte\",\n    bit: 1 << 2,\n    ccBits: [\n        8,\n        16,\n        16\n    ]\n};\n/**\n * The Kanji mode efficiently encodes Kanji characters in accordance with\n * the Shift JIS system based on JIS X 0208.\n * The Shift JIS values are shifted from the JIS X 0208 values.\n * JIS X 0208 gives details of the shift coded representation.\n * Each two-byte character value is compacted to a 13-bit binary codeword.\n *\n * @type {Object}\n */ exports.KANJI = {\n    id: \"Kanji\",\n    bit: 1 << 3,\n    ccBits: [\n        8,\n        10,\n        12\n    ]\n};\n/**\n * Mixed mode will contain a sequences of data in a combination of any of\n * the modes described above\n *\n * @type {Object}\n */ exports.MIXED = {\n    bit: -1\n};\n/**\n * Returns the number of bits needed to store the data length\n * according to QR Code specifications.\n *\n * @param  {Mode}   mode    Data mode\n * @param  {Number} version QR Code version\n * @return {Number}         Number of bits\n */ exports.getCharCountIndicator = function getCharCountIndicator(mode, version) {\n    if (!mode.ccBits) throw new Error(\"Invalid mode: \" + mode);\n    if (!VersionCheck.isValid(version)) {\n        throw new Error(\"Invalid version: \" + version);\n    }\n    if (version >= 1 && version < 10) return mode.ccBits[0];\n    else if (version < 27) return mode.ccBits[1];\n    return mode.ccBits[2];\n};\n/**\n * Returns the most efficient mode to store the specified data\n *\n * @param  {String} dataStr Input data string\n * @return {Mode}           Best mode\n */ exports.getBestModeForData = function getBestModeForData(dataStr) {\n    if (Regex.testNumeric(dataStr)) return exports.NUMERIC;\n    else if (Regex.testAlphanumeric(dataStr)) return exports.ALPHANUMERIC;\n    else if (Regex.testKanji(dataStr)) return exports.KANJI;\n    else return exports.BYTE;\n};\n/**\n * Return mode name as string\n *\n * @param {Mode} mode Mode object\n * @returns {String}  Mode name\n */ exports.toString = function toString(mode) {\n    if (mode && mode.id) return mode.id;\n    throw new Error(\"Invalid mode\");\n};\n/**\n * Check if input param is a valid mode object\n *\n * @param   {Mode}    mode Mode object\n * @returns {Boolean} True if valid mode, false otherwise\n */ exports.isValid = function isValid(mode) {\n    return mode && mode.bit && mode.ccBits;\n};\n/**\n * Get mode object from its name\n *\n * @param   {String} string Mode name\n * @returns {Mode}          Mode object\n */ function fromString(string) {\n    if (typeof string !== \"string\") {\n        throw new Error(\"Param is not a string\");\n    }\n    const lcStr = string.toLowerCase();\n    switch(lcStr){\n        case \"numeric\":\n            return exports.NUMERIC;\n        case \"alphanumeric\":\n            return exports.ALPHANUMERIC;\n        case \"kanji\":\n            return exports.KANJI;\n        case \"byte\":\n            return exports.BYTE;\n        default:\n            throw new Error(\"Unknown mode: \" + string);\n    }\n}\n/**\n * Returns mode from a value.\n * If value is not a valid mode, returns defaultValue\n *\n * @param  {Mode|String} value        Encoding mode\n * @param  {Mode}        defaultValue Fallback value\n * @return {Mode}                     Encoding mode\n */ exports.from = function from(value, defaultValue) {\n    if (exports.isValid(value)) {\n        return value;\n    }\n    try {\n        return fromString(value);\n    } catch (e) {\n        return defaultValue;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcXJjb2RlL2xpYi9jb3JlL21vZGUuanMiLCJtYXBwaW5ncyI6IkFBQUEsTUFBTUEsZUFBZUMsbUJBQU9BLENBQUM7QUFDN0IsTUFBTUMsUUFBUUQsbUJBQU9BLENBQUM7QUFFdEI7Ozs7OztDQU1DLEdBQ0RFLGVBQWUsR0FBRztJQUNoQkUsSUFBSTtJQUNKQyxLQUFLLEtBQUs7SUFDVkMsUUFBUTtRQUFDO1FBQUk7UUFBSTtLQUFHO0FBQ3RCO0FBRUE7Ozs7Ozs7O0NBUUMsR0FDREosb0JBQW9CLEdBQUc7SUFDckJFLElBQUk7SUFDSkMsS0FBSyxLQUFLO0lBQ1ZDLFFBQVE7UUFBQztRQUFHO1FBQUk7S0FBRztBQUNyQjtBQUVBOzs7O0NBSUMsR0FDREosWUFBWSxHQUFHO0lBQ2JFLElBQUk7SUFDSkMsS0FBSyxLQUFLO0lBQ1ZDLFFBQVE7UUFBQztRQUFHO1FBQUk7S0FBRztBQUNyQjtBQUVBOzs7Ozs7OztDQVFDLEdBQ0RKLGFBQWEsR0FBRztJQUNkRSxJQUFJO0lBQ0pDLEtBQUssS0FBSztJQUNWQyxRQUFRO1FBQUM7UUFBRztRQUFJO0tBQUc7QUFDckI7QUFFQTs7Ozs7Q0FLQyxHQUNESixhQUFhLEdBQUc7SUFDZEcsS0FBSyxDQUFDO0FBQ1I7QUFFQTs7Ozs7OztDQU9DLEdBQ0RILDZCQUE2QixHQUFHLFNBQVNTLHNCQUF1QkMsSUFBSSxFQUFFQyxPQUFPO0lBQzNFLElBQUksQ0FBQ0QsS0FBS04sTUFBTSxFQUFFLE1BQU0sSUFBSVEsTUFBTSxtQkFBbUJGO0lBRXJELElBQUksQ0FBQ2IsYUFBYWdCLE9BQU8sQ0FBQ0YsVUFBVTtRQUNsQyxNQUFNLElBQUlDLE1BQU0sc0JBQXNCRDtJQUN4QztJQUVBLElBQUlBLFdBQVcsS0FBS0EsVUFBVSxJQUFJLE9BQU9ELEtBQUtOLE1BQU0sQ0FBQyxFQUFFO1NBQ2xELElBQUlPLFVBQVUsSUFBSSxPQUFPRCxLQUFLTixNQUFNLENBQUMsRUFBRTtJQUM1QyxPQUFPTSxLQUFLTixNQUFNLENBQUMsRUFBRTtBQUN2QjtBQUVBOzs7OztDQUtDLEdBQ0RKLDBCQUEwQixHQUFHLFNBQVNjLG1CQUFvQkMsT0FBTztJQUMvRCxJQUFJaEIsTUFBTWlCLFdBQVcsQ0FBQ0QsVUFBVSxPQUFPZixRQUFRQyxPQUFPO1NBQ2pELElBQUlGLE1BQU1rQixnQkFBZ0IsQ0FBQ0YsVUFBVSxPQUFPZixRQUFRSyxZQUFZO1NBQ2hFLElBQUlOLE1BQU1tQixTQUFTLENBQUNILFVBQVUsT0FBT2YsUUFBUU8sS0FBSztTQUNsRCxPQUFPUCxRQUFRTSxJQUFJO0FBQzFCO0FBRUE7Ozs7O0NBS0MsR0FDRE4sZ0JBQWdCLEdBQUcsU0FBU21CLFNBQVVULElBQUk7SUFDeEMsSUFBSUEsUUFBUUEsS0FBS1IsRUFBRSxFQUFFLE9BQU9RLEtBQUtSLEVBQUU7SUFDbkMsTUFBTSxJQUFJVSxNQUFNO0FBQ2xCO0FBRUE7Ozs7O0NBS0MsR0FDRFosZUFBZSxHQUFHLFNBQVNhLFFBQVNILElBQUk7SUFDdEMsT0FBT0EsUUFBUUEsS0FBS1AsR0FBRyxJQUFJTyxLQUFLTixNQUFNO0FBQ3hDO0FBRUE7Ozs7O0NBS0MsR0FDRCxTQUFTZ0IsV0FBWUMsTUFBTTtJQUN6QixJQUFJLE9BQU9BLFdBQVcsVUFBVTtRQUM5QixNQUFNLElBQUlULE1BQU07SUFDbEI7SUFFQSxNQUFNVSxRQUFRRCxPQUFPRSxXQUFXO0lBRWhDLE9BQVFEO1FBQ04sS0FBSztZQUNILE9BQU90QixRQUFRQyxPQUFPO1FBQ3hCLEtBQUs7WUFDSCxPQUFPRCxRQUFRSyxZQUFZO1FBQzdCLEtBQUs7WUFDSCxPQUFPTCxRQUFRTyxLQUFLO1FBQ3RCLEtBQUs7WUFDSCxPQUFPUCxRQUFRTSxJQUFJO1FBQ3JCO1lBQ0UsTUFBTSxJQUFJTSxNQUFNLG1CQUFtQlM7SUFDdkM7QUFDRjtBQUVBOzs7Ozs7O0NBT0MsR0FDRHJCLFlBQVksR0FBRyxTQUFTd0IsS0FBTUMsS0FBSyxFQUFFQyxZQUFZO0lBQy9DLElBQUkxQixRQUFRYSxPQUFPLENBQUNZLFFBQVE7UUFDMUIsT0FBT0E7SUFDVDtJQUVBLElBQUk7UUFDRixPQUFPTCxXQUFXSztJQUNwQixFQUFFLE9BQU9FLEdBQUc7UUFDVixPQUFPRDtJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iaGVlbWRpbmUvLi9ub2RlX21vZHVsZXMvcXJjb2RlL2xpYi9jb3JlL21vZGUuanM/ODM3YSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBWZXJzaW9uQ2hlY2sgPSByZXF1aXJlKCcuL3ZlcnNpb24tY2hlY2snKVxuY29uc3QgUmVnZXggPSByZXF1aXJlKCcuL3JlZ2V4JylcblxuLyoqXG4gKiBOdW1lcmljIG1vZGUgZW5jb2RlcyBkYXRhIGZyb20gdGhlIGRlY2ltYWwgZGlnaXQgc2V0ICgwIC0gOSlcbiAqIChieXRlIHZhbHVlcyAzMEhFWCB0byAzOUhFWCkuXG4gKiBOb3JtYWxseSwgMyBkYXRhIGNoYXJhY3RlcnMgYXJlIHJlcHJlc2VudGVkIGJ5IDEwIGJpdHMuXG4gKlxuICogQHR5cGUge09iamVjdH1cbiAqL1xuZXhwb3J0cy5OVU1FUklDID0ge1xuICBpZDogJ051bWVyaWMnLFxuICBiaXQ6IDEgPDwgMCxcbiAgY2NCaXRzOiBbMTAsIDEyLCAxNF1cbn1cblxuLyoqXG4gKiBBbHBoYW51bWVyaWMgbW9kZSBlbmNvZGVzIGRhdGEgZnJvbSBhIHNldCBvZiA0NSBjaGFyYWN0ZXJzLFxuICogaS5lLiAxMCBudW1lcmljIGRpZ2l0cyAoMCAtIDkpLFxuICogICAgICAyNiBhbHBoYWJldGljIGNoYXJhY3RlcnMgKEEgLSBaKSxcbiAqICAgYW5kIDkgc3ltYm9scyAoU1AsICQsICUsICosICssIC0sIC4sIC8sIDopLlxuICogTm9ybWFsbHksIHR3byBpbnB1dCBjaGFyYWN0ZXJzIGFyZSByZXByZXNlbnRlZCBieSAxMSBiaXRzLlxuICpcbiAqIEB0eXBlIHtPYmplY3R9XG4gKi9cbmV4cG9ydHMuQUxQSEFOVU1FUklDID0ge1xuICBpZDogJ0FscGhhbnVtZXJpYycsXG4gIGJpdDogMSA8PCAxLFxuICBjY0JpdHM6IFs5LCAxMSwgMTNdXG59XG5cbi8qKlxuICogSW4gYnl0ZSBtb2RlLCBkYXRhIGlzIGVuY29kZWQgYXQgOCBiaXRzIHBlciBjaGFyYWN0ZXIuXG4gKlxuICogQHR5cGUge09iamVjdH1cbiAqL1xuZXhwb3J0cy5CWVRFID0ge1xuICBpZDogJ0J5dGUnLFxuICBiaXQ6IDEgPDwgMixcbiAgY2NCaXRzOiBbOCwgMTYsIDE2XVxufVxuXG4vKipcbiAqIFRoZSBLYW5qaSBtb2RlIGVmZmljaWVudGx5IGVuY29kZXMgS2FuamkgY2hhcmFjdGVycyBpbiBhY2NvcmRhbmNlIHdpdGhcbiAqIHRoZSBTaGlmdCBKSVMgc3lzdGVtIGJhc2VkIG9uIEpJUyBYIDAyMDguXG4gKiBUaGUgU2hpZnQgSklTIHZhbHVlcyBhcmUgc2hpZnRlZCBmcm9tIHRoZSBKSVMgWCAwMjA4IHZhbHVlcy5cbiAqIEpJUyBYIDAyMDggZ2l2ZXMgZGV0YWlscyBvZiB0aGUgc2hpZnQgY29kZWQgcmVwcmVzZW50YXRpb24uXG4gKiBFYWNoIHR3by1ieXRlIGNoYXJhY3RlciB2YWx1ZSBpcyBjb21wYWN0ZWQgdG8gYSAxMy1iaXQgYmluYXJ5IGNvZGV3b3JkLlxuICpcbiAqIEB0eXBlIHtPYmplY3R9XG4gKi9cbmV4cG9ydHMuS0FOSkkgPSB7XG4gIGlkOiAnS2FuamknLFxuICBiaXQ6IDEgPDwgMyxcbiAgY2NCaXRzOiBbOCwgMTAsIDEyXVxufVxuXG4vKipcbiAqIE1peGVkIG1vZGUgd2lsbCBjb250YWluIGEgc2VxdWVuY2VzIG9mIGRhdGEgaW4gYSBjb21iaW5hdGlvbiBvZiBhbnkgb2ZcbiAqIHRoZSBtb2RlcyBkZXNjcmliZWQgYWJvdmVcbiAqXG4gKiBAdHlwZSB7T2JqZWN0fVxuICovXG5leHBvcnRzLk1JWEVEID0ge1xuICBiaXQ6IC0xXG59XG5cbi8qKlxuICogUmV0dXJucyB0aGUgbnVtYmVyIG9mIGJpdHMgbmVlZGVkIHRvIHN0b3JlIHRoZSBkYXRhIGxlbmd0aFxuICogYWNjb3JkaW5nIHRvIFFSIENvZGUgc3BlY2lmaWNhdGlvbnMuXG4gKlxuICogQHBhcmFtICB7TW9kZX0gICBtb2RlICAgIERhdGEgbW9kZVxuICogQHBhcmFtICB7TnVtYmVyfSB2ZXJzaW9uIFFSIENvZGUgdmVyc2lvblxuICogQHJldHVybiB7TnVtYmVyfSAgICAgICAgIE51bWJlciBvZiBiaXRzXG4gKi9cbmV4cG9ydHMuZ2V0Q2hhckNvdW50SW5kaWNhdG9yID0gZnVuY3Rpb24gZ2V0Q2hhckNvdW50SW5kaWNhdG9yIChtb2RlLCB2ZXJzaW9uKSB7XG4gIGlmICghbW9kZS5jY0JpdHMpIHRocm93IG5ldyBFcnJvcignSW52YWxpZCBtb2RlOiAnICsgbW9kZSlcblxuICBpZiAoIVZlcnNpb25DaGVjay5pc1ZhbGlkKHZlcnNpb24pKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIHZlcnNpb246ICcgKyB2ZXJzaW9uKVxuICB9XG5cbiAgaWYgKHZlcnNpb24gPj0gMSAmJiB2ZXJzaW9uIDwgMTApIHJldHVybiBtb2RlLmNjQml0c1swXVxuICBlbHNlIGlmICh2ZXJzaW9uIDwgMjcpIHJldHVybiBtb2RlLmNjQml0c1sxXVxuICByZXR1cm4gbW9kZS5jY0JpdHNbMl1cbn1cblxuLyoqXG4gKiBSZXR1cm5zIHRoZSBtb3N0IGVmZmljaWVudCBtb2RlIHRvIHN0b3JlIHRoZSBzcGVjaWZpZWQgZGF0YVxuICpcbiAqIEBwYXJhbSAge1N0cmluZ30gZGF0YVN0ciBJbnB1dCBkYXRhIHN0cmluZ1xuICogQHJldHVybiB7TW9kZX0gICAgICAgICAgIEJlc3QgbW9kZVxuICovXG5leHBvcnRzLmdldEJlc3RNb2RlRm9yRGF0YSA9IGZ1bmN0aW9uIGdldEJlc3RNb2RlRm9yRGF0YSAoZGF0YVN0cikge1xuICBpZiAoUmVnZXgudGVzdE51bWVyaWMoZGF0YVN0cikpIHJldHVybiBleHBvcnRzLk5VTUVSSUNcbiAgZWxzZSBpZiAoUmVnZXgudGVzdEFscGhhbnVtZXJpYyhkYXRhU3RyKSkgcmV0dXJuIGV4cG9ydHMuQUxQSEFOVU1FUklDXG4gIGVsc2UgaWYgKFJlZ2V4LnRlc3RLYW5qaShkYXRhU3RyKSkgcmV0dXJuIGV4cG9ydHMuS0FOSklcbiAgZWxzZSByZXR1cm4gZXhwb3J0cy5CWVRFXG59XG5cbi8qKlxuICogUmV0dXJuIG1vZGUgbmFtZSBhcyBzdHJpbmdcbiAqXG4gKiBAcGFyYW0ge01vZGV9IG1vZGUgTW9kZSBvYmplY3RcbiAqIEByZXR1cm5zIHtTdHJpbmd9ICBNb2RlIG5hbWVcbiAqL1xuZXhwb3J0cy50b1N0cmluZyA9IGZ1bmN0aW9uIHRvU3RyaW5nIChtb2RlKSB7XG4gIGlmIChtb2RlICYmIG1vZGUuaWQpIHJldHVybiBtb2RlLmlkXG4gIHRocm93IG5ldyBFcnJvcignSW52YWxpZCBtb2RlJylcbn1cblxuLyoqXG4gKiBDaGVjayBpZiBpbnB1dCBwYXJhbSBpcyBhIHZhbGlkIG1vZGUgb2JqZWN0XG4gKlxuICogQHBhcmFtICAge01vZGV9ICAgIG1vZGUgTW9kZSBvYmplY3RcbiAqIEByZXR1cm5zIHtCb29sZWFufSBUcnVlIGlmIHZhbGlkIG1vZGUsIGZhbHNlIG90aGVyd2lzZVxuICovXG5leHBvcnRzLmlzVmFsaWQgPSBmdW5jdGlvbiBpc1ZhbGlkIChtb2RlKSB7XG4gIHJldHVybiBtb2RlICYmIG1vZGUuYml0ICYmIG1vZGUuY2NCaXRzXG59XG5cbi8qKlxuICogR2V0IG1vZGUgb2JqZWN0IGZyb20gaXRzIG5hbWVcbiAqXG4gKiBAcGFyYW0gICB7U3RyaW5nfSBzdHJpbmcgTW9kZSBuYW1lXG4gKiBAcmV0dXJucyB7TW9kZX0gICAgICAgICAgTW9kZSBvYmplY3RcbiAqL1xuZnVuY3Rpb24gZnJvbVN0cmluZyAoc3RyaW5nKSB7XG4gIGlmICh0eXBlb2Ygc3RyaW5nICE9PSAnc3RyaW5nJykge1xuICAgIHRocm93IG5ldyBFcnJvcignUGFyYW0gaXMgbm90IGEgc3RyaW5nJylcbiAgfVxuXG4gIGNvbnN0IGxjU3RyID0gc3RyaW5nLnRvTG93ZXJDYXNlKClcblxuICBzd2l0Y2ggKGxjU3RyKSB7XG4gICAgY2FzZSAnbnVtZXJpYyc6XG4gICAgICByZXR1cm4gZXhwb3J0cy5OVU1FUklDXG4gICAgY2FzZSAnYWxwaGFudW1lcmljJzpcbiAgICAgIHJldHVybiBleHBvcnRzLkFMUEhBTlVNRVJJQ1xuICAgIGNhc2UgJ2thbmppJzpcbiAgICAgIHJldHVybiBleHBvcnRzLktBTkpJXG4gICAgY2FzZSAnYnl0ZSc6XG4gICAgICByZXR1cm4gZXhwb3J0cy5CWVRFXG4gICAgZGVmYXVsdDpcbiAgICAgIHRocm93IG5ldyBFcnJvcignVW5rbm93biBtb2RlOiAnICsgc3RyaW5nKVxuICB9XG59XG5cbi8qKlxuICogUmV0dXJucyBtb2RlIGZyb20gYSB2YWx1ZS5cbiAqIElmIHZhbHVlIGlzIG5vdCBhIHZhbGlkIG1vZGUsIHJldHVybnMgZGVmYXVsdFZhbHVlXG4gKlxuICogQHBhcmFtICB7TW9kZXxTdHJpbmd9IHZhbHVlICAgICAgICBFbmNvZGluZyBtb2RlXG4gKiBAcGFyYW0gIHtNb2RlfSAgICAgICAgZGVmYXVsdFZhbHVlIEZhbGxiYWNrIHZhbHVlXG4gKiBAcmV0dXJuIHtNb2RlfSAgICAgICAgICAgICAgICAgICAgIEVuY29kaW5nIG1vZGVcbiAqL1xuZXhwb3J0cy5mcm9tID0gZnVuY3Rpb24gZnJvbSAodmFsdWUsIGRlZmF1bHRWYWx1ZSkge1xuICBpZiAoZXhwb3J0cy5pc1ZhbGlkKHZhbHVlKSkge1xuICAgIHJldHVybiB2YWx1ZVxuICB9XG5cbiAgdHJ5IHtcbiAgICByZXR1cm4gZnJvbVN0cmluZyh2YWx1ZSlcbiAgfSBjYXRjaCAoZSkge1xuICAgIHJldHVybiBkZWZhdWx0VmFsdWVcbiAgfVxufVxuIl0sIm5hbWVzIjpbIlZlcnNpb25DaGVjayIsInJlcXVpcmUiLCJSZWdleCIsImV4cG9ydHMiLCJOVU1FUklDIiwiaWQiLCJiaXQiLCJjY0JpdHMiLCJBTFBIQU5VTUVSSUMiLCJCWVRFIiwiS0FOSkkiLCJNSVhFRCIsImdldENoYXJDb3VudEluZGljYXRvciIsIm1vZGUiLCJ2ZXJzaW9uIiwiRXJyb3IiLCJpc1ZhbGlkIiwiZ2V0QmVzdE1vZGVGb3JEYXRhIiwiZGF0YVN0ciIsInRlc3ROdW1lcmljIiwidGVzdEFscGhhbnVtZXJpYyIsInRlc3RLYW5qaSIsInRvU3RyaW5nIiwiZnJvbVN0cmluZyIsInN0cmluZyIsImxjU3RyIiwidG9Mb3dlckNhc2UiLCJmcm9tIiwidmFsdWUiLCJkZWZhdWx0VmFsdWUiLCJlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/mode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/numeric-data.js":
/*!******************************************************!*\
  !*** ./node_modules/qrcode/lib/core/numeric-data.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const Mode = __webpack_require__(/*! ./mode */ \"(ssr)/./node_modules/qrcode/lib/core/mode.js\");\nfunction NumericData(data) {\n    this.mode = Mode.NUMERIC;\n    this.data = data.toString();\n}\nNumericData.getBitsLength = function getBitsLength(length) {\n    return 10 * Math.floor(length / 3) + (length % 3 ? length % 3 * 3 + 1 : 0);\n};\nNumericData.prototype.getLength = function getLength() {\n    return this.data.length;\n};\nNumericData.prototype.getBitsLength = function getBitsLength() {\n    return NumericData.getBitsLength(this.data.length);\n};\nNumericData.prototype.write = function write(bitBuffer) {\n    let i, group, value;\n    // The input data string is divided into groups of three digits,\n    // and each group is converted to its 10-bit binary equivalent.\n    for(i = 0; i + 3 <= this.data.length; i += 3){\n        group = this.data.substr(i, 3);\n        value = parseInt(group, 10);\n        bitBuffer.put(value, 10);\n    }\n    // If the number of input digits is not an exact multiple of three,\n    // the final one or two digits are converted to 4 or 7 bits respectively.\n    const remainingNum = this.data.length - i;\n    if (remainingNum > 0) {\n        group = this.data.substr(i);\n        value = parseInt(group, 10);\n        bitBuffer.put(value, remainingNum * 3 + 1);\n    }\n};\nmodule.exports = NumericData;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/numeric-data.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/polynomial.js":
/*!****************************************************!*\
  !*** ./node_modules/qrcode/lib/core/polynomial.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const GF = __webpack_require__(/*! ./galois-field */ \"(ssr)/./node_modules/qrcode/lib/core/galois-field.js\");\n/**\n * Multiplies two polynomials inside Galois Field\n *\n * @param  {Uint8Array} p1 Polynomial\n * @param  {Uint8Array} p2 Polynomial\n * @return {Uint8Array}    Product of p1 and p2\n */ exports.mul = function mul(p1, p2) {\n    const coeff = new Uint8Array(p1.length + p2.length - 1);\n    for(let i = 0; i < p1.length; i++){\n        for(let j = 0; j < p2.length; j++){\n            coeff[i + j] ^= GF.mul(p1[i], p2[j]);\n        }\n    }\n    return coeff;\n};\n/**\n * Calculate the remainder of polynomials division\n *\n * @param  {Uint8Array} divident Polynomial\n * @param  {Uint8Array} divisor  Polynomial\n * @return {Uint8Array}          Remainder\n */ exports.mod = function mod(divident, divisor) {\n    let result = new Uint8Array(divident);\n    while(result.length - divisor.length >= 0){\n        const coeff = result[0];\n        for(let i = 0; i < divisor.length; i++){\n            result[i] ^= GF.mul(divisor[i], coeff);\n        }\n        // remove all zeros from buffer head\n        let offset = 0;\n        while(offset < result.length && result[offset] === 0)offset++;\n        result = result.slice(offset);\n    }\n    return result;\n};\n/**\n * Generate an irreducible generator polynomial of specified degree\n * (used by Reed-Solomon encoder)\n *\n * @param  {Number} degree Degree of the generator polynomial\n * @return {Uint8Array}    Buffer containing polynomial coefficients\n */ exports.generateECPolynomial = function generateECPolynomial(degree) {\n    let poly = new Uint8Array([\n        1\n    ]);\n    for(let i = 0; i < degree; i++){\n        poly = exports.mul(poly, new Uint8Array([\n            1,\n            GF.exp(i)\n        ]));\n    }\n    return poly;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/polynomial.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/qrcode.js":
/*!************************************************!*\
  !*** ./node_modules/qrcode/lib/core/qrcode.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const Utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/qrcode/lib/core/utils.js\");\nconst ECLevel = __webpack_require__(/*! ./error-correction-level */ \"(ssr)/./node_modules/qrcode/lib/core/error-correction-level.js\");\nconst BitBuffer = __webpack_require__(/*! ./bit-buffer */ \"(ssr)/./node_modules/qrcode/lib/core/bit-buffer.js\");\nconst BitMatrix = __webpack_require__(/*! ./bit-matrix */ \"(ssr)/./node_modules/qrcode/lib/core/bit-matrix.js\");\nconst AlignmentPattern = __webpack_require__(/*! ./alignment-pattern */ \"(ssr)/./node_modules/qrcode/lib/core/alignment-pattern.js\");\nconst FinderPattern = __webpack_require__(/*! ./finder-pattern */ \"(ssr)/./node_modules/qrcode/lib/core/finder-pattern.js\");\nconst MaskPattern = __webpack_require__(/*! ./mask-pattern */ \"(ssr)/./node_modules/qrcode/lib/core/mask-pattern.js\");\nconst ECCode = __webpack_require__(/*! ./error-correction-code */ \"(ssr)/./node_modules/qrcode/lib/core/error-correction-code.js\");\nconst ReedSolomonEncoder = __webpack_require__(/*! ./reed-solomon-encoder */ \"(ssr)/./node_modules/qrcode/lib/core/reed-solomon-encoder.js\");\nconst Version = __webpack_require__(/*! ./version */ \"(ssr)/./node_modules/qrcode/lib/core/version.js\");\nconst FormatInfo = __webpack_require__(/*! ./format-info */ \"(ssr)/./node_modules/qrcode/lib/core/format-info.js\");\nconst Mode = __webpack_require__(/*! ./mode */ \"(ssr)/./node_modules/qrcode/lib/core/mode.js\");\nconst Segments = __webpack_require__(/*! ./segments */ \"(ssr)/./node_modules/qrcode/lib/core/segments.js\");\n/**\n * QRCode for JavaScript\n *\n * modified by Ryan Day for nodejs support\n * Copyright (c) 2011 Ryan Day\n *\n * Licensed under the MIT license:\n *   http://www.opensource.org/licenses/mit-license.php\n *\n//---------------------------------------------------------------------\n// QRCode for JavaScript\n//\n// Copyright (c) 2009 Kazuhiko Arase\n//\n// URL: http://www.d-project.com/\n//\n// Licensed under the MIT license:\n//   http://www.opensource.org/licenses/mit-license.php\n//\n// The word \"QR Code\" is registered trademark of\n// DENSO WAVE INCORPORATED\n//   http://www.denso-wave.com/qrcode/faqpatent-e.html\n//\n//---------------------------------------------------------------------\n*/ /**\n * Add finder patterns bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */ function setupFinderPattern(matrix, version) {\n    const size = matrix.size;\n    const pos = FinderPattern.getPositions(version);\n    for(let i = 0; i < pos.length; i++){\n        const row = pos[i][0];\n        const col = pos[i][1];\n        for(let r = -1; r <= 7; r++){\n            if (row + r <= -1 || size <= row + r) continue;\n            for(let c = -1; c <= 7; c++){\n                if (col + c <= -1 || size <= col + c) continue;\n                if (r >= 0 && r <= 6 && (c === 0 || c === 6) || c >= 0 && c <= 6 && (r === 0 || r === 6) || r >= 2 && r <= 4 && c >= 2 && c <= 4) {\n                    matrix.set(row + r, col + c, true, true);\n                } else {\n                    matrix.set(row + r, col + c, false, true);\n                }\n            }\n        }\n    }\n}\n/**\n * Add timing pattern bits to matrix\n *\n * Note: this function must be called before {@link setupAlignmentPattern}\n *\n * @param  {BitMatrix} matrix Modules matrix\n */ function setupTimingPattern(matrix) {\n    const size = matrix.size;\n    for(let r = 8; r < size - 8; r++){\n        const value = r % 2 === 0;\n        matrix.set(r, 6, value, true);\n        matrix.set(6, r, value, true);\n    }\n}\n/**\n * Add alignment patterns bits to matrix\n *\n * Note: this function must be called after {@link setupTimingPattern}\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */ function setupAlignmentPattern(matrix, version) {\n    const pos = AlignmentPattern.getPositions(version);\n    for(let i = 0; i < pos.length; i++){\n        const row = pos[i][0];\n        const col = pos[i][1];\n        for(let r = -2; r <= 2; r++){\n            for(let c = -2; c <= 2; c++){\n                if (r === -2 || r === 2 || c === -2 || c === 2 || r === 0 && c === 0) {\n                    matrix.set(row + r, col + c, true, true);\n                } else {\n                    matrix.set(row + r, col + c, false, true);\n                }\n            }\n        }\n    }\n}\n/**\n * Add version info bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */ function setupVersionInfo(matrix, version) {\n    const size = matrix.size;\n    const bits = Version.getEncodedBits(version);\n    let row, col, mod;\n    for(let i = 0; i < 18; i++){\n        row = Math.floor(i / 3);\n        col = i % 3 + size - 8 - 3;\n        mod = (bits >> i & 1) === 1;\n        matrix.set(row, col, mod, true);\n        matrix.set(col, row, mod, true);\n    }\n}\n/**\n * Add format info bits to matrix\n *\n * @param  {BitMatrix} matrix               Modules matrix\n * @param  {ErrorCorrectionLevel}    errorCorrectionLevel Error correction level\n * @param  {Number}    maskPattern          Mask pattern reference value\n */ function setupFormatInfo(matrix, errorCorrectionLevel, maskPattern) {\n    const size = matrix.size;\n    const bits = FormatInfo.getEncodedBits(errorCorrectionLevel, maskPattern);\n    let i, mod;\n    for(i = 0; i < 15; i++){\n        mod = (bits >> i & 1) === 1;\n        // vertical\n        if (i < 6) {\n            matrix.set(i, 8, mod, true);\n        } else if (i < 8) {\n            matrix.set(i + 1, 8, mod, true);\n        } else {\n            matrix.set(size - 15 + i, 8, mod, true);\n        }\n        // horizontal\n        if (i < 8) {\n            matrix.set(8, size - i - 1, mod, true);\n        } else if (i < 9) {\n            matrix.set(8, 15 - i - 1 + 1, mod, true);\n        } else {\n            matrix.set(8, 15 - i - 1, mod, true);\n        }\n    }\n    // fixed module\n    matrix.set(size - 8, 8, 1, true);\n}\n/**\n * Add encoded data bits to matrix\n *\n * @param  {BitMatrix}  matrix Modules matrix\n * @param  {Uint8Array} data   Data codewords\n */ function setupData(matrix, data) {\n    const size = matrix.size;\n    let inc = -1;\n    let row = size - 1;\n    let bitIndex = 7;\n    let byteIndex = 0;\n    for(let col = size - 1; col > 0; col -= 2){\n        if (col === 6) col--;\n        while(true){\n            for(let c = 0; c < 2; c++){\n                if (!matrix.isReserved(row, col - c)) {\n                    let dark = false;\n                    if (byteIndex < data.length) {\n                        dark = (data[byteIndex] >>> bitIndex & 1) === 1;\n                    }\n                    matrix.set(row, col - c, dark);\n                    bitIndex--;\n                    if (bitIndex === -1) {\n                        byteIndex++;\n                        bitIndex = 7;\n                    }\n                }\n            }\n            row += inc;\n            if (row < 0 || size <= row) {\n                row -= inc;\n                inc = -inc;\n                break;\n            }\n        }\n    }\n}\n/**\n * Create encoded codewords from data input\n *\n * @param  {Number}   version              QR Code version\n * @param  {ErrorCorrectionLevel}   errorCorrectionLevel Error correction level\n * @param  {ByteData} data                 Data input\n * @return {Uint8Array}                    Buffer containing encoded codewords\n */ function createData(version, errorCorrectionLevel, segments) {\n    // Prepare data buffer\n    const buffer = new BitBuffer();\n    segments.forEach(function(data) {\n        // prefix data with mode indicator (4 bits)\n        buffer.put(data.mode.bit, 4);\n        // Prefix data with character count indicator.\n        // The character count indicator is a string of bits that represents the\n        // number of characters that are being encoded.\n        // The character count indicator must be placed after the mode indicator\n        // and must be a certain number of bits long, depending on the QR version\n        // and data mode\n        // @see {@link Mode.getCharCountIndicator}.\n        buffer.put(data.getLength(), Mode.getCharCountIndicator(data.mode, version));\n        // add binary data sequence to buffer\n        data.write(buffer);\n    });\n    // Calculate required number of bits\n    const totalCodewords = Utils.getSymbolTotalCodewords(version);\n    const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);\n    const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8;\n    // Add a terminator.\n    // If the bit string is shorter than the total number of required bits,\n    // a terminator of up to four 0s must be added to the right side of the string.\n    // If the bit string is more than four bits shorter than the required number of bits,\n    // add four 0s to the end.\n    if (buffer.getLengthInBits() + 4 <= dataTotalCodewordsBits) {\n        buffer.put(0, 4);\n    }\n    // If the bit string is fewer than four bits shorter, add only the number of 0s that\n    // are needed to reach the required number of bits.\n    // After adding the terminator, if the number of bits in the string is not a multiple of 8,\n    // pad the string on the right with 0s to make the string's length a multiple of 8.\n    while(buffer.getLengthInBits() % 8 !== 0){\n        buffer.putBit(0);\n    }\n    // Add pad bytes if the string is still shorter than the total number of required bits.\n    // Extend the buffer to fill the data capacity of the symbol corresponding to\n    // the Version and Error Correction Level by adding the Pad Codewords 11101100 (0xEC)\n    // and 00010001 (0x11) alternately.\n    const remainingByte = (dataTotalCodewordsBits - buffer.getLengthInBits()) / 8;\n    for(let i = 0; i < remainingByte; i++){\n        buffer.put(i % 2 ? 0x11 : 0xEC, 8);\n    }\n    return createCodewords(buffer, version, errorCorrectionLevel);\n}\n/**\n * Encode input data with Reed-Solomon and return codewords with\n * relative error correction bits\n *\n * @param  {BitBuffer} bitBuffer            Data to encode\n * @param  {Number}    version              QR Code version\n * @param  {ErrorCorrectionLevel} errorCorrectionLevel Error correction level\n * @return {Uint8Array}                     Buffer containing encoded codewords\n */ function createCodewords(bitBuffer, version, errorCorrectionLevel) {\n    // Total codewords for this QR code version (Data + Error correction)\n    const totalCodewords = Utils.getSymbolTotalCodewords(version);\n    // Total number of error correction codewords\n    const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);\n    // Total number of data codewords\n    const dataTotalCodewords = totalCodewords - ecTotalCodewords;\n    // Total number of blocks\n    const ecTotalBlocks = ECCode.getBlocksCount(version, errorCorrectionLevel);\n    // Calculate how many blocks each group should contain\n    const blocksInGroup2 = totalCodewords % ecTotalBlocks;\n    const blocksInGroup1 = ecTotalBlocks - blocksInGroup2;\n    const totalCodewordsInGroup1 = Math.floor(totalCodewords / ecTotalBlocks);\n    const dataCodewordsInGroup1 = Math.floor(dataTotalCodewords / ecTotalBlocks);\n    const dataCodewordsInGroup2 = dataCodewordsInGroup1 + 1;\n    // Number of EC codewords is the same for both groups\n    const ecCount = totalCodewordsInGroup1 - dataCodewordsInGroup1;\n    // Initialize a Reed-Solomon encoder with a generator polynomial of degree ecCount\n    const rs = new ReedSolomonEncoder(ecCount);\n    let offset = 0;\n    const dcData = new Array(ecTotalBlocks);\n    const ecData = new Array(ecTotalBlocks);\n    let maxDataSize = 0;\n    const buffer = new Uint8Array(bitBuffer.buffer);\n    // Divide the buffer into the required number of blocks\n    for(let b = 0; b < ecTotalBlocks; b++){\n        const dataSize = b < blocksInGroup1 ? dataCodewordsInGroup1 : dataCodewordsInGroup2;\n        // extract a block of data from buffer\n        dcData[b] = buffer.slice(offset, offset + dataSize);\n        // Calculate EC codewords for this data block\n        ecData[b] = rs.encode(dcData[b]);\n        offset += dataSize;\n        maxDataSize = Math.max(maxDataSize, dataSize);\n    }\n    // Create final data\n    // Interleave the data and error correction codewords from each block\n    const data = new Uint8Array(totalCodewords);\n    let index = 0;\n    let i, r;\n    // Add data codewords\n    for(i = 0; i < maxDataSize; i++){\n        for(r = 0; r < ecTotalBlocks; r++){\n            if (i < dcData[r].length) {\n                data[index++] = dcData[r][i];\n            }\n        }\n    }\n    // Apped EC codewords\n    for(i = 0; i < ecCount; i++){\n        for(r = 0; r < ecTotalBlocks; r++){\n            data[index++] = ecData[r][i];\n        }\n    }\n    return data;\n}\n/**\n * Build QR Code symbol\n *\n * @param  {String} data                 Input string\n * @param  {Number} version              QR Code version\n * @param  {ErrorCorretionLevel} errorCorrectionLevel Error level\n * @param  {MaskPattern} maskPattern     Mask pattern\n * @return {Object}                      Object containing symbol data\n */ function createSymbol(data, version, errorCorrectionLevel, maskPattern) {\n    let segments;\n    if (Array.isArray(data)) {\n        segments = Segments.fromArray(data);\n    } else if (typeof data === \"string\") {\n        let estimatedVersion = version;\n        if (!estimatedVersion) {\n            const rawSegments = Segments.rawSplit(data);\n            // Estimate best version that can contain raw splitted segments\n            estimatedVersion = Version.getBestVersionForData(rawSegments, errorCorrectionLevel);\n        }\n        // Build optimized segments\n        // If estimated version is undefined, try with the highest version\n        segments = Segments.fromString(data, estimatedVersion || 40);\n    } else {\n        throw new Error(\"Invalid data\");\n    }\n    // Get the min version that can contain data\n    const bestVersion = Version.getBestVersionForData(segments, errorCorrectionLevel);\n    // If no version is found, data cannot be stored\n    if (!bestVersion) {\n        throw new Error(\"The amount of data is too big to be stored in a QR Code\");\n    }\n    // If not specified, use min version as default\n    if (!version) {\n        version = bestVersion;\n    // Check if the specified version can contain the data\n    } else if (version < bestVersion) {\n        throw new Error(\"\\n\" + \"The chosen QR Code version cannot contain this amount of data.\\n\" + \"Minimum version required to store current data is: \" + bestVersion + \".\\n\");\n    }\n    const dataBits = createData(version, errorCorrectionLevel, segments);\n    // Allocate matrix buffer\n    const moduleCount = Utils.getSymbolSize(version);\n    const modules = new BitMatrix(moduleCount);\n    // Add function modules\n    setupFinderPattern(modules, version);\n    setupTimingPattern(modules);\n    setupAlignmentPattern(modules, version);\n    // Add temporary dummy bits for format info just to set them as reserved.\n    // This is needed to prevent these bits from being masked by {@link MaskPattern.applyMask}\n    // since the masking operation must be performed only on the encoding region.\n    // These blocks will be replaced with correct values later in code.\n    setupFormatInfo(modules, errorCorrectionLevel, 0);\n    if (version >= 7) {\n        setupVersionInfo(modules, version);\n    }\n    // Add data codewords\n    setupData(modules, dataBits);\n    if (isNaN(maskPattern)) {\n        // Find best mask pattern\n        maskPattern = MaskPattern.getBestMask(modules, setupFormatInfo.bind(null, modules, errorCorrectionLevel));\n    }\n    // Apply mask pattern\n    MaskPattern.applyMask(maskPattern, modules);\n    // Replace format info bits with correct values\n    setupFormatInfo(modules, errorCorrectionLevel, maskPattern);\n    return {\n        modules: modules,\n        version: version,\n        errorCorrectionLevel: errorCorrectionLevel,\n        maskPattern: maskPattern,\n        segments: segments\n    };\n}\n/**\n * QR Code\n *\n * @param {String | Array} data                 Input data\n * @param {Object} options                      Optional configurations\n * @param {Number} options.version              QR Code version\n * @param {String} options.errorCorrectionLevel Error correction level\n * @param {Function} options.toSJISFunc         Helper func to convert utf8 to sjis\n */ exports.create = function create(data, options) {\n    if (typeof data === \"undefined\" || data === \"\") {\n        throw new Error(\"No input text\");\n    }\n    let errorCorrectionLevel = ECLevel.M;\n    let version;\n    let mask;\n    if (typeof options !== \"undefined\") {\n        // Use higher error correction level as default\n        errorCorrectionLevel = ECLevel.from(options.errorCorrectionLevel, ECLevel.M);\n        version = Version.from(options.version);\n        mask = MaskPattern.from(options.maskPattern);\n        if (options.toSJISFunc) {\n            Utils.setToSJISFunction(options.toSJISFunc);\n        }\n    }\n    return createSymbol(data, version, errorCorrectionLevel, mask);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/qrcode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/reed-solomon-encoder.js":
/*!**************************************************************!*\
  !*** ./node_modules/qrcode/lib/core/reed-solomon-encoder.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const Polynomial = __webpack_require__(/*! ./polynomial */ \"(ssr)/./node_modules/qrcode/lib/core/polynomial.js\");\nfunction ReedSolomonEncoder(degree) {\n    this.genPoly = undefined;\n    this.degree = degree;\n    if (this.degree) this.initialize(this.degree);\n}\n/**\n * Initialize the encoder.\n * The input param should correspond to the number of error correction codewords.\n *\n * @param  {Number} degree\n */ ReedSolomonEncoder.prototype.initialize = function initialize(degree) {\n    // create an irreducible generator polynomial\n    this.degree = degree;\n    this.genPoly = Polynomial.generateECPolynomial(this.degree);\n};\n/**\n * Encodes a chunk of data\n *\n * @param  {Uint8Array} data Buffer containing input data\n * @return {Uint8Array}      Buffer containing encoded data\n */ ReedSolomonEncoder.prototype.encode = function encode(data) {\n    if (!this.genPoly) {\n        throw new Error(\"Encoder not initialized\");\n    }\n    // Calculate EC for this data block\n    // extends data size to data+genPoly size\n    const paddedData = new Uint8Array(data.length + this.degree);\n    paddedData.set(data);\n    // The error correction codewords are the remainder after dividing the data codewords\n    // by a generator polynomial\n    const remainder = Polynomial.mod(paddedData, this.genPoly);\n    // return EC data blocks (last n byte, where n is the degree of genPoly)\n    // If coefficients number in remainder are less than genPoly degree,\n    // pad with 0s to the left to reach the needed number of coefficients\n    const start = this.degree - remainder.length;\n    if (start > 0) {\n        const buff = new Uint8Array(this.degree);\n        buff.set(remainder, start);\n        return buff;\n    }\n    return remainder;\n};\nmodule.exports = ReedSolomonEncoder;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/reed-solomon-encoder.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/regex.js":
/*!***********************************************!*\
  !*** ./node_modules/qrcode/lib/core/regex.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("const numeric = \"[0-9]+\";\nconst alphanumeric = \"[A-Z $%*+\\\\-./:]+\";\nlet kanji = \"(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|\" + \"[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|\" + \"[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|\" + \"[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+\";\nkanji = kanji.replace(/u/g, \"\\\\u\");\nconst byte = \"(?:(?![A-Z0-9 $%*+\\\\-./:]|\" + kanji + \")(?:.|[\\r\\n]))+\";\nexports.KANJI = new RegExp(kanji, \"g\");\nexports.BYTE_KANJI = new RegExp(\"[^A-Z0-9 $%*+\\\\-./:]+\", \"g\");\nexports.BYTE = new RegExp(byte, \"g\");\nexports.NUMERIC = new RegExp(numeric, \"g\");\nexports.ALPHANUMERIC = new RegExp(alphanumeric, \"g\");\nconst TEST_KANJI = new RegExp(\"^\" + kanji + \"$\");\nconst TEST_NUMERIC = new RegExp(\"^\" + numeric + \"$\");\nconst TEST_ALPHANUMERIC = new RegExp(\"^[A-Z0-9 $%*+\\\\-./:]+$\");\nexports.testKanji = function testKanji(str) {\n    return TEST_KANJI.test(str);\n};\nexports.testNumeric = function testNumeric(str) {\n    return TEST_NUMERIC.test(str);\n};\nexports.testAlphanumeric = function testAlphanumeric(str) {\n    return TEST_ALPHANUMERIC.test(str);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/regex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/segments.js":
/*!**************************************************!*\
  !*** ./node_modules/qrcode/lib/core/segments.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const Mode = __webpack_require__(/*! ./mode */ \"(ssr)/./node_modules/qrcode/lib/core/mode.js\");\nconst NumericData = __webpack_require__(/*! ./numeric-data */ \"(ssr)/./node_modules/qrcode/lib/core/numeric-data.js\");\nconst AlphanumericData = __webpack_require__(/*! ./alphanumeric-data */ \"(ssr)/./node_modules/qrcode/lib/core/alphanumeric-data.js\");\nconst ByteData = __webpack_require__(/*! ./byte-data */ \"(ssr)/./node_modules/qrcode/lib/core/byte-data.js\");\nconst KanjiData = __webpack_require__(/*! ./kanji-data */ \"(ssr)/./node_modules/qrcode/lib/core/kanji-data.js\");\nconst Regex = __webpack_require__(/*! ./regex */ \"(ssr)/./node_modules/qrcode/lib/core/regex.js\");\nconst Utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/qrcode/lib/core/utils.js\");\nconst dijkstra = __webpack_require__(/*! dijkstrajs */ \"(ssr)/./node_modules/dijkstrajs/dijkstra.js\");\n/**\n * Returns UTF8 byte length\n *\n * @param  {String} str Input string\n * @return {Number}     Number of byte\n */ function getStringByteLength(str) {\n    return unescape(encodeURIComponent(str)).length;\n}\n/**\n * Get a list of segments of the specified mode\n * from a string\n *\n * @param  {Mode}   mode Segment mode\n * @param  {String} str  String to process\n * @return {Array}       Array of object with segments data\n */ function getSegments(regex, mode, str) {\n    const segments = [];\n    let result;\n    while((result = regex.exec(str)) !== null){\n        segments.push({\n            data: result[0],\n            index: result.index,\n            mode: mode,\n            length: result[0].length\n        });\n    }\n    return segments;\n}\n/**\n * Extracts a series of segments with the appropriate\n * modes from a string\n *\n * @param  {String} dataStr Input string\n * @return {Array}          Array of object with segments data\n */ function getSegmentsFromString(dataStr) {\n    const numSegs = getSegments(Regex.NUMERIC, Mode.NUMERIC, dataStr);\n    const alphaNumSegs = getSegments(Regex.ALPHANUMERIC, Mode.ALPHANUMERIC, dataStr);\n    let byteSegs;\n    let kanjiSegs;\n    if (Utils.isKanjiModeEnabled()) {\n        byteSegs = getSegments(Regex.BYTE, Mode.BYTE, dataStr);\n        kanjiSegs = getSegments(Regex.KANJI, Mode.KANJI, dataStr);\n    } else {\n        byteSegs = getSegments(Regex.BYTE_KANJI, Mode.BYTE, dataStr);\n        kanjiSegs = [];\n    }\n    const segs = numSegs.concat(alphaNumSegs, byteSegs, kanjiSegs);\n    return segs.sort(function(s1, s2) {\n        return s1.index - s2.index;\n    }).map(function(obj) {\n        return {\n            data: obj.data,\n            mode: obj.mode,\n            length: obj.length\n        };\n    });\n}\n/**\n * Returns how many bits are needed to encode a string of\n * specified length with the specified mode\n *\n * @param  {Number} length String length\n * @param  {Mode} mode     Segment mode\n * @return {Number}        Bit length\n */ function getSegmentBitsLength(length, mode) {\n    switch(mode){\n        case Mode.NUMERIC:\n            return NumericData.getBitsLength(length);\n        case Mode.ALPHANUMERIC:\n            return AlphanumericData.getBitsLength(length);\n        case Mode.KANJI:\n            return KanjiData.getBitsLength(length);\n        case Mode.BYTE:\n            return ByteData.getBitsLength(length);\n    }\n}\n/**\n * Merges adjacent segments which have the same mode\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */ function mergeSegments(segs) {\n    return segs.reduce(function(acc, curr) {\n        const prevSeg = acc.length - 1 >= 0 ? acc[acc.length - 1] : null;\n        if (prevSeg && prevSeg.mode === curr.mode) {\n            acc[acc.length - 1].data += curr.data;\n            return acc;\n        }\n        acc.push(curr);\n        return acc;\n    }, []);\n}\n/**\n * Generates a list of all possible nodes combination which\n * will be used to build a segments graph.\n *\n * Nodes are divided by groups. Each group will contain a list of all the modes\n * in which is possible to encode the given text.\n *\n * For example the text '12345' can be encoded as Numeric, Alphanumeric or Byte.\n * The group for '12345' will contain then 3 objects, one for each\n * possible encoding mode.\n *\n * Each node represents a possible segment.\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */ function buildNodes(segs) {\n    const nodes = [];\n    for(let i = 0; i < segs.length; i++){\n        const seg = segs[i];\n        switch(seg.mode){\n            case Mode.NUMERIC:\n                nodes.push([\n                    seg,\n                    {\n                        data: seg.data,\n                        mode: Mode.ALPHANUMERIC,\n                        length: seg.length\n                    },\n                    {\n                        data: seg.data,\n                        mode: Mode.BYTE,\n                        length: seg.length\n                    }\n                ]);\n                break;\n            case Mode.ALPHANUMERIC:\n                nodes.push([\n                    seg,\n                    {\n                        data: seg.data,\n                        mode: Mode.BYTE,\n                        length: seg.length\n                    }\n                ]);\n                break;\n            case Mode.KANJI:\n                nodes.push([\n                    seg,\n                    {\n                        data: seg.data,\n                        mode: Mode.BYTE,\n                        length: getStringByteLength(seg.data)\n                    }\n                ]);\n                break;\n            case Mode.BYTE:\n                nodes.push([\n                    {\n                        data: seg.data,\n                        mode: Mode.BYTE,\n                        length: getStringByteLength(seg.data)\n                    }\n                ]);\n        }\n    }\n    return nodes;\n}\n/**\n * Builds a graph from a list of nodes.\n * All segments in each node group will be connected with all the segments of\n * the next group and so on.\n *\n * At each connection will be assigned a weight depending on the\n * segment's byte length.\n *\n * @param  {Array} nodes    Array of object with segments data\n * @param  {Number} version QR Code version\n * @return {Object}         Graph of all possible segments\n */ function buildGraph(nodes, version) {\n    const table = {};\n    const graph = {\n        start: {}\n    };\n    let prevNodeIds = [\n        \"start\"\n    ];\n    for(let i = 0; i < nodes.length; i++){\n        const nodeGroup = nodes[i];\n        const currentNodeIds = [];\n        for(let j = 0; j < nodeGroup.length; j++){\n            const node = nodeGroup[j];\n            const key = \"\" + i + j;\n            currentNodeIds.push(key);\n            table[key] = {\n                node: node,\n                lastCount: 0\n            };\n            graph[key] = {};\n            for(let n = 0; n < prevNodeIds.length; n++){\n                const prevNodeId = prevNodeIds[n];\n                if (table[prevNodeId] && table[prevNodeId].node.mode === node.mode) {\n                    graph[prevNodeId][key] = getSegmentBitsLength(table[prevNodeId].lastCount + node.length, node.mode) - getSegmentBitsLength(table[prevNodeId].lastCount, node.mode);\n                    table[prevNodeId].lastCount += node.length;\n                } else {\n                    if (table[prevNodeId]) table[prevNodeId].lastCount = node.length;\n                    graph[prevNodeId][key] = getSegmentBitsLength(node.length, node.mode) + 4 + Mode.getCharCountIndicator(node.mode, version) // switch cost\n                    ;\n                }\n            }\n        }\n        prevNodeIds = currentNodeIds;\n    }\n    for(let n = 0; n < prevNodeIds.length; n++){\n        graph[prevNodeIds[n]].end = 0;\n    }\n    return {\n        map: graph,\n        table: table\n    };\n}\n/**\n * Builds a segment from a specified data and mode.\n * If a mode is not specified, the more suitable will be used.\n *\n * @param  {String} data             Input data\n * @param  {Mode | String} modesHint Data mode\n * @return {Segment}                 Segment\n */ function buildSingleSegment(data, modesHint) {\n    let mode;\n    const bestMode = Mode.getBestModeForData(data);\n    mode = Mode.from(modesHint, bestMode);\n    // Make sure data can be encoded\n    if (mode !== Mode.BYTE && mode.bit < bestMode.bit) {\n        throw new Error('\"' + data + '\"' + \" cannot be encoded with mode \" + Mode.toString(mode) + \".\\n Suggested mode is: \" + Mode.toString(bestMode));\n    }\n    // Use Mode.BYTE if Kanji support is disabled\n    if (mode === Mode.KANJI && !Utils.isKanjiModeEnabled()) {\n        mode = Mode.BYTE;\n    }\n    switch(mode){\n        case Mode.NUMERIC:\n            return new NumericData(data);\n        case Mode.ALPHANUMERIC:\n            return new AlphanumericData(data);\n        case Mode.KANJI:\n            return new KanjiData(data);\n        case Mode.BYTE:\n            return new ByteData(data);\n    }\n}\n/**\n * Builds a list of segments from an array.\n * Array can contain Strings or Objects with segment's info.\n *\n * For each item which is a string, will be generated a segment with the given\n * string and the more appropriate encoding mode.\n *\n * For each item which is an object, will be generated a segment with the given\n * data and mode.\n * Objects must contain at least the property \"data\".\n * If property \"mode\" is not present, the more suitable mode will be used.\n *\n * @param  {Array} array Array of objects with segments data\n * @return {Array}       Array of Segments\n */ exports.fromArray = function fromArray(array) {\n    return array.reduce(function(acc, seg) {\n        if (typeof seg === \"string\") {\n            acc.push(buildSingleSegment(seg, null));\n        } else if (seg.data) {\n            acc.push(buildSingleSegment(seg.data, seg.mode));\n        }\n        return acc;\n    }, []);\n};\n/**\n * Builds an optimized sequence of segments from a string,\n * which will produce the shortest possible bitstream.\n *\n * @param  {String} data    Input string\n * @param  {Number} version QR Code version\n * @return {Array}          Array of segments\n */ exports.fromString = function fromString(data, version) {\n    const segs = getSegmentsFromString(data, Utils.isKanjiModeEnabled());\n    const nodes = buildNodes(segs);\n    const graph = buildGraph(nodes, version);\n    const path = dijkstra.find_path(graph.map, \"start\", \"end\");\n    const optimizedSegs = [];\n    for(let i = 1; i < path.length - 1; i++){\n        optimizedSegs.push(graph.table[path[i]].node);\n    }\n    return exports.fromArray(mergeSegments(optimizedSegs));\n};\n/**\n * Splits a string in various segments with the modes which\n * best represent their content.\n * The produced segments are far from being optimized.\n * The output of this function is only used to estimate a QR Code version\n * which may contain the data.\n *\n * @param  {string} data Input string\n * @return {Array}       Array of segments\n */ exports.rawSplit = function rawSplit(data) {\n    return exports.fromArray(getSegmentsFromString(data, Utils.isKanjiModeEnabled()));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/segments.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/utils.js":
/*!***********************************************!*\
  !*** ./node_modules/qrcode/lib/core/utils.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("let toSJISFunction;\nconst CODEWORDS_COUNT = [\n    0,\n    26,\n    44,\n    70,\n    100,\n    134,\n    172,\n    196,\n    242,\n    292,\n    346,\n    404,\n    466,\n    532,\n    581,\n    655,\n    733,\n    815,\n    901,\n    991,\n    1085,\n    1156,\n    1258,\n    1364,\n    1474,\n    1588,\n    1706,\n    1828,\n    1921,\n    2051,\n    2185,\n    2323,\n    2465,\n    2611,\n    2761,\n    2876,\n    3034,\n    3196,\n    3362,\n    3532,\n    3706\n];\n/**\n * Returns the QR Code size for the specified version\n *\n * @param  {Number} version QR Code version\n * @return {Number}         size of QR code\n */ exports.getSymbolSize = function getSymbolSize(version) {\n    if (!version) throw new Error('\"version\" cannot be null or undefined');\n    if (version < 1 || version > 40) throw new Error('\"version\" should be in range from 1 to 40');\n    return version * 4 + 17;\n};\n/**\n * Returns the total number of codewords used to store data and EC information.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Data length in bits\n */ exports.getSymbolTotalCodewords = function getSymbolTotalCodewords(version) {\n    return CODEWORDS_COUNT[version];\n};\n/**\n * Encode data with Bose-Chaudhuri-Hocquenghem\n *\n * @param  {Number} data Value to encode\n * @return {Number}      Encoded value\n */ exports.getBCHDigit = function(data) {\n    let digit = 0;\n    while(data !== 0){\n        digit++;\n        data >>>= 1;\n    }\n    return digit;\n};\nexports.setToSJISFunction = function setToSJISFunction(f) {\n    if (typeof f !== \"function\") {\n        throw new Error('\"toSJISFunc\" is not a valid function.');\n    }\n    toSJISFunction = f;\n};\nexports.isKanjiModeEnabled = function() {\n    return typeof toSJISFunction !== \"undefined\";\n};\nexports.toSJIS = function toSJIS(kanji) {\n    return toSJISFunction(kanji);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/version-check.js":
/*!*******************************************************!*\
  !*** ./node_modules/qrcode/lib/core/version-check.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/**\n * Check if QR Code version is valid\n *\n * @param  {Number}  version QR Code version\n * @return {Boolean}         true if valid version, false otherwise\n */ exports.isValid = function isValid(version) {\n    return !isNaN(version) && version >= 1 && version <= 40;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcXJjb2RlL2xpYi9jb3JlL3ZlcnNpb24tY2hlY2suanMiLCJtYXBwaW5ncyI6IkFBQUE7Ozs7O0NBS0MsR0FDREEsZUFBZSxHQUFHLFNBQVNDLFFBQVNDLE9BQU87SUFDekMsT0FBTyxDQUFDQyxNQUFNRCxZQUFZQSxXQUFXLEtBQUtBLFdBQVc7QUFDdkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iaGVlbWRpbmUvLi9ub2RlX21vZHVsZXMvcXJjb2RlL2xpYi9jb3JlL3ZlcnNpb24tY2hlY2suanM/YTgyZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENoZWNrIGlmIFFSIENvZGUgdmVyc2lvbiBpcyB2YWxpZFxuICpcbiAqIEBwYXJhbSAge051bWJlcn0gIHZlcnNpb24gUVIgQ29kZSB2ZXJzaW9uXG4gKiBAcmV0dXJuIHtCb29sZWFufSAgICAgICAgIHRydWUgaWYgdmFsaWQgdmVyc2lvbiwgZmFsc2Ugb3RoZXJ3aXNlXG4gKi9cbmV4cG9ydHMuaXNWYWxpZCA9IGZ1bmN0aW9uIGlzVmFsaWQgKHZlcnNpb24pIHtcbiAgcmV0dXJuICFpc05hTih2ZXJzaW9uKSAmJiB2ZXJzaW9uID49IDEgJiYgdmVyc2lvbiA8PSA0MFxufVxuIl0sIm5hbWVzIjpbImV4cG9ydHMiLCJpc1ZhbGlkIiwidmVyc2lvbiIsImlzTmFOIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/version-check.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/core/version.js":
/*!*************************************************!*\
  !*** ./node_modules/qrcode/lib/core/version.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const Utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/qrcode/lib/core/utils.js\");\nconst ECCode = __webpack_require__(/*! ./error-correction-code */ \"(ssr)/./node_modules/qrcode/lib/core/error-correction-code.js\");\nconst ECLevel = __webpack_require__(/*! ./error-correction-level */ \"(ssr)/./node_modules/qrcode/lib/core/error-correction-level.js\");\nconst Mode = __webpack_require__(/*! ./mode */ \"(ssr)/./node_modules/qrcode/lib/core/mode.js\");\nconst VersionCheck = __webpack_require__(/*! ./version-check */ \"(ssr)/./node_modules/qrcode/lib/core/version-check.js\");\n// Generator polynomial used to encode version information\nconst G18 = 1 << 12 | 1 << 11 | 1 << 10 | 1 << 9 | 1 << 8 | 1 << 5 | 1 << 2 | 1 << 0;\nconst G18_BCH = Utils.getBCHDigit(G18);\nfunction getBestVersionForDataLength(mode, length, errorCorrectionLevel) {\n    for(let currentVersion = 1; currentVersion <= 40; currentVersion++){\n        if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, mode)) {\n            return currentVersion;\n        }\n    }\n    return undefined;\n}\nfunction getReservedBitsCount(mode, version) {\n    // Character count indicator + mode indicator bits\n    return Mode.getCharCountIndicator(mode, version) + 4;\n}\nfunction getTotalBitsFromDataArray(segments, version) {\n    let totalBits = 0;\n    segments.forEach(function(data) {\n        const reservedBits = getReservedBitsCount(data.mode, version);\n        totalBits += reservedBits + data.getBitsLength();\n    });\n    return totalBits;\n}\nfunction getBestVersionForMixedData(segments, errorCorrectionLevel) {\n    for(let currentVersion = 1; currentVersion <= 40; currentVersion++){\n        const length = getTotalBitsFromDataArray(segments, currentVersion);\n        if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, Mode.MIXED)) {\n            return currentVersion;\n        }\n    }\n    return undefined;\n}\n/**\n * Returns version number from a value.\n * If value is not a valid version, returns defaultValue\n *\n * @param  {Number|String} value        QR Code version\n * @param  {Number}        defaultValue Fallback value\n * @return {Number}                     QR Code version number\n */ exports.from = function from(value, defaultValue) {\n    if (VersionCheck.isValid(value)) {\n        return parseInt(value, 10);\n    }\n    return defaultValue;\n};\n/**\n * Returns how much data can be stored with the specified QR code version\n * and error correction level\n *\n * @param  {Number} version              QR Code version (1-40)\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Mode}   mode                 Data mode\n * @return {Number}                      Quantity of storable data\n */ exports.getCapacity = function getCapacity(version, errorCorrectionLevel, mode) {\n    if (!VersionCheck.isValid(version)) {\n        throw new Error(\"Invalid QR Code version\");\n    }\n    // Use Byte mode as default\n    if (typeof mode === \"undefined\") mode = Mode.BYTE;\n    // Total codewords for this QR code version (Data + Error correction)\n    const totalCodewords = Utils.getSymbolTotalCodewords(version);\n    // Total number of error correction codewords\n    const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);\n    // Total number of data codewords\n    const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8;\n    if (mode === Mode.MIXED) return dataTotalCodewordsBits;\n    const usableBits = dataTotalCodewordsBits - getReservedBitsCount(mode, version);\n    // Return max number of storable codewords\n    switch(mode){\n        case Mode.NUMERIC:\n            return Math.floor(usableBits / 10 * 3);\n        case Mode.ALPHANUMERIC:\n            return Math.floor(usableBits / 11 * 2);\n        case Mode.KANJI:\n            return Math.floor(usableBits / 13);\n        case Mode.BYTE:\n        default:\n            return Math.floor(usableBits / 8);\n    }\n};\n/**\n * Returns the minimum version needed to contain the amount of data\n *\n * @param  {Segment} data                    Segment of data\n * @param  {Number} [errorCorrectionLevel=H] Error correction level\n * @param  {Mode} mode                       Data mode\n * @return {Number}                          QR Code version\n */ exports.getBestVersionForData = function getBestVersionForData(data, errorCorrectionLevel) {\n    let seg;\n    const ecl = ECLevel.from(errorCorrectionLevel, ECLevel.M);\n    if (Array.isArray(data)) {\n        if (data.length > 1) {\n            return getBestVersionForMixedData(data, ecl);\n        }\n        if (data.length === 0) {\n            return 1;\n        }\n        seg = data[0];\n    } else {\n        seg = data;\n    }\n    return getBestVersionForDataLength(seg.mode, seg.getLength(), ecl);\n};\n/**\n * Returns version information with relative error correction bits\n *\n * The version information is included in QR Code symbols of version 7 or larger.\n * It consists of an 18-bit sequence containing 6 data bits,\n * with 12 error correction bits calculated using the (18, 6) Golay code.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Encoded version info bits\n */ exports.getEncodedBits = function getEncodedBits(version) {\n    if (!VersionCheck.isValid(version) || version < 7) {\n        throw new Error(\"Invalid QR Code version\");\n    }\n    let d = version << 12;\n    while(Utils.getBCHDigit(d) - G18_BCH >= 0){\n        d ^= G18 << Utils.getBCHDigit(d) - G18_BCH;\n    }\n    return version << 12 | d;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/core/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/index.js":
/*!******************************************!*\
  !*** ./node_modules/qrcode/lib/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*\n*copyright Ryan Day 2012\n*\n* Licensed under the MIT license:\n*   http://www.opensource.org/licenses/mit-license.php\n*\n* this is the main server side application file for node-qrcode.\n* these exports use serverside canvas api methods for file IO and buffers\n*\n*/ module.exports = __webpack_require__(/*! ./server */ \"(ssr)/./node_modules/qrcode/lib/server.js\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcXJjb2RlL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTs7Ozs7Ozs7O0FBU0EsR0FFQUEsaUdBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmhlZW1kaW5lLy4vbm9kZV9tb2R1bGVzL3FyY29kZS9saWIvaW5kZXguanM/OGM5MSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuKmNvcHlyaWdodCBSeWFuIERheSAyMDEyXG4qXG4qIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZTpcbiogICBodHRwOi8vd3d3Lm9wZW5zb3VyY2Uub3JnL2xpY2Vuc2VzL21pdC1saWNlbnNlLnBocFxuKlxuKiB0aGlzIGlzIHRoZSBtYWluIHNlcnZlciBzaWRlIGFwcGxpY2F0aW9uIGZpbGUgZm9yIG5vZGUtcXJjb2RlLlxuKiB0aGVzZSBleHBvcnRzIHVzZSBzZXJ2ZXJzaWRlIGNhbnZhcyBhcGkgbWV0aG9kcyBmb3IgZmlsZSBJTyBhbmQgYnVmZmVyc1xuKlxuKi9cblxubW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL3NlcnZlcicpXG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsInJlcXVpcmUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/renderer/canvas.js":
/*!****************************************************!*\
  !*** ./node_modules/qrcode/lib/renderer/canvas.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const Utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/qrcode/lib/renderer/utils.js\");\nfunction clearCanvas(ctx, canvas, size) {\n    ctx.clearRect(0, 0, canvas.width, canvas.height);\n    if (!canvas.style) canvas.style = {};\n    canvas.height = size;\n    canvas.width = size;\n    canvas.style.height = size + \"px\";\n    canvas.style.width = size + \"px\";\n}\nfunction getCanvasElement() {\n    try {\n        return document.createElement(\"canvas\");\n    } catch (e) {\n        throw new Error(\"You need to specify a canvas element\");\n    }\n}\nexports.render = function render(qrData, canvas, options) {\n    let opts = options;\n    let canvasEl = canvas;\n    if (typeof opts === \"undefined\" && (!canvas || !canvas.getContext)) {\n        opts = canvas;\n        canvas = undefined;\n    }\n    if (!canvas) {\n        canvasEl = getCanvasElement();\n    }\n    opts = Utils.getOptions(opts);\n    const size = Utils.getImageWidth(qrData.modules.size, opts);\n    const ctx = canvasEl.getContext(\"2d\");\n    const image = ctx.createImageData(size, size);\n    Utils.qrToImageData(image.data, qrData, opts);\n    clearCanvas(ctx, canvasEl, size);\n    ctx.putImageData(image, 0, 0);\n    return canvasEl;\n};\nexports.renderToDataURL = function renderToDataURL(qrData, canvas, options) {\n    let opts = options;\n    if (typeof opts === \"undefined\" && (!canvas || !canvas.getContext)) {\n        opts = canvas;\n        canvas = undefined;\n    }\n    if (!opts) opts = {};\n    const canvasEl = exports.render(qrData, canvas, opts);\n    const type = opts.type || \"image/png\";\n    const rendererOpts = opts.rendererOpts || {};\n    return canvasEl.toDataURL(type, rendererOpts.quality);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/renderer/canvas.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/renderer/png.js":
/*!*************************************************!*\
  !*** ./node_modules/qrcode/lib/renderer/png.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const fs = __webpack_require__(/*! fs */ \"fs\");\nconst PNG = (__webpack_require__(/*! pngjs */ \"(ssr)/./node_modules/pngjs/lib/png.js\").PNG);\nconst Utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/qrcode/lib/renderer/utils.js\");\nexports.render = function render(qrData, options) {\n    const opts = Utils.getOptions(options);\n    const pngOpts = opts.rendererOpts;\n    const size = Utils.getImageWidth(qrData.modules.size, opts);\n    pngOpts.width = size;\n    pngOpts.height = size;\n    const pngImage = new PNG(pngOpts);\n    Utils.qrToImageData(pngImage.data, qrData, opts);\n    return pngImage;\n};\nexports.renderToDataURL = function renderToDataURL(qrData, options, cb) {\n    if (typeof cb === \"undefined\") {\n        cb = options;\n        options = undefined;\n    }\n    exports.renderToBuffer(qrData, options, function(err, output) {\n        if (err) cb(err);\n        let url = \"data:image/png;base64,\";\n        url += output.toString(\"base64\");\n        cb(null, url);\n    });\n};\nexports.renderToBuffer = function renderToBuffer(qrData, options, cb) {\n    if (typeof cb === \"undefined\") {\n        cb = options;\n        options = undefined;\n    }\n    const png = exports.render(qrData, options);\n    const buffer = [];\n    png.on(\"error\", cb);\n    png.on(\"data\", function(data) {\n        buffer.push(data);\n    });\n    png.on(\"end\", function() {\n        cb(null, Buffer.concat(buffer));\n    });\n    png.pack();\n};\nexports.renderToFile = function renderToFile(path, qrData, options, cb) {\n    if (typeof cb === \"undefined\") {\n        cb = options;\n        options = undefined;\n    }\n    let called = false;\n    const done = (...args)=>{\n        if (called) return;\n        called = true;\n        cb.apply(null, args);\n    };\n    const stream = fs.createWriteStream(path);\n    stream.on(\"error\", done);\n    stream.on(\"close\", done);\n    exports.renderToFileStream(stream, qrData, options);\n};\nexports.renderToFileStream = function renderToFileStream(stream, qrData, options) {\n    const png = exports.render(qrData, options);\n    png.pack().pipe(stream);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/renderer/png.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/renderer/svg-tag.js":
/*!*****************************************************!*\
  !*** ./node_modules/qrcode/lib/renderer/svg-tag.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const Utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/qrcode/lib/renderer/utils.js\");\nfunction getColorAttrib(color, attrib) {\n    const alpha = color.a / 255;\n    const str = attrib + '=\"' + color.hex + '\"';\n    return alpha < 1 ? str + \" \" + attrib + '-opacity=\"' + alpha.toFixed(2).slice(1) + '\"' : str;\n}\nfunction svgCmd(cmd, x, y) {\n    let str = cmd + x;\n    if (typeof y !== \"undefined\") str += \" \" + y;\n    return str;\n}\nfunction qrToPath(data, size, margin) {\n    let path = \"\";\n    let moveBy = 0;\n    let newRow = false;\n    let lineLength = 0;\n    for(let i = 0; i < data.length; i++){\n        const col = Math.floor(i % size);\n        const row = Math.floor(i / size);\n        if (!col && !newRow) newRow = true;\n        if (data[i]) {\n            lineLength++;\n            if (!(i > 0 && col > 0 && data[i - 1])) {\n                path += newRow ? svgCmd(\"M\", col + margin, 0.5 + row + margin) : svgCmd(\"m\", moveBy, 0);\n                moveBy = 0;\n                newRow = false;\n            }\n            if (!(col + 1 < size && data[i + 1])) {\n                path += svgCmd(\"h\", lineLength);\n                lineLength = 0;\n            }\n        } else {\n            moveBy++;\n        }\n    }\n    return path;\n}\nexports.render = function render(qrData, options, cb) {\n    const opts = Utils.getOptions(options);\n    const size = qrData.modules.size;\n    const data = qrData.modules.data;\n    const qrcodesize = size + opts.margin * 2;\n    const bg = !opts.color.light.a ? \"\" : \"<path \" + getColorAttrib(opts.color.light, \"fill\") + ' d=\"M0 0h' + qrcodesize + \"v\" + qrcodesize + 'H0z\"/>';\n    const path = \"<path \" + getColorAttrib(opts.color.dark, \"stroke\") + ' d=\"' + qrToPath(data, size, opts.margin) + '\"/>';\n    const viewBox = 'viewBox=\"' + \"0 0 \" + qrcodesize + \" \" + qrcodesize + '\"';\n    const width = !opts.width ? \"\" : 'width=\"' + opts.width + '\" height=\"' + opts.width + '\" ';\n    const svgTag = '<svg xmlns=\"http://www.w3.org/2000/svg\" ' + width + viewBox + ' shape-rendering=\"crispEdges\">' + bg + path + \"</svg>\\n\";\n    if (typeof cb === \"function\") {\n        cb(null, svgTag);\n    }\n    return svgTag;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/renderer/svg-tag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/renderer/svg.js":
/*!*************************************************!*\
  !*** ./node_modules/qrcode/lib/renderer/svg.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const svgTagRenderer = __webpack_require__(/*! ./svg-tag */ \"(ssr)/./node_modules/qrcode/lib/renderer/svg-tag.js\");\nexports.render = svgTagRenderer.render;\nexports.renderToFile = function renderToFile(path, qrData, options, cb) {\n    if (typeof cb === \"undefined\") {\n        cb = options;\n        options = undefined;\n    }\n    const fs = __webpack_require__(/*! fs */ \"fs\");\n    const svgTag = exports.render(qrData, options);\n    const xmlStr = '<?xml version=\"1.0\" encoding=\"utf-8\"?>' + '<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">' + svgTag;\n    fs.writeFile(path, xmlStr, cb);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcXJjb2RlL2xpYi9yZW5kZXJlci9zdmcuanMiLCJtYXBwaW5ncyI6IkFBQUEsTUFBTUEsaUJBQWlCQyxtQkFBT0EsQ0FBQztBQUUvQkMsY0FBYyxHQUFHRixlQUFlRyxNQUFNO0FBRXRDRCxvQkFBb0IsR0FBRyxTQUFTRSxhQUFjQyxJQUFJLEVBQUVDLE1BQU0sRUFBRUMsT0FBTyxFQUFFQyxFQUFFO0lBQ3JFLElBQUksT0FBT0EsT0FBTyxhQUFhO1FBQzdCQSxLQUFLRDtRQUNMQSxVQUFVRTtJQUNaO0lBRUEsTUFBTUMsS0FBS1QsbUJBQU9BLENBQUM7SUFDbkIsTUFBTVUsU0FBU1QsUUFBUUMsTUFBTSxDQUFDRyxRQUFRQztJQUV0QyxNQUFNSyxTQUFTLDJDQUNiLHVHQUNBRDtJQUVGRCxHQUFHRyxTQUFTLENBQUNSLE1BQU1PLFFBQVFKO0FBQzdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmhlZW1kaW5lLy4vbm9kZV9tb2R1bGVzL3FyY29kZS9saWIvcmVuZGVyZXIvc3ZnLmpzP2FmNTkiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgc3ZnVGFnUmVuZGVyZXIgPSByZXF1aXJlKCcuL3N2Zy10YWcnKVxuXG5leHBvcnRzLnJlbmRlciA9IHN2Z1RhZ1JlbmRlcmVyLnJlbmRlclxuXG5leHBvcnRzLnJlbmRlclRvRmlsZSA9IGZ1bmN0aW9uIHJlbmRlclRvRmlsZSAocGF0aCwgcXJEYXRhLCBvcHRpb25zLCBjYikge1xuICBpZiAodHlwZW9mIGNiID09PSAndW5kZWZpbmVkJykge1xuICAgIGNiID0gb3B0aW9uc1xuICAgIG9wdGlvbnMgPSB1bmRlZmluZWRcbiAgfVxuXG4gIGNvbnN0IGZzID0gcmVxdWlyZSgnZnMnKVxuICBjb25zdCBzdmdUYWcgPSBleHBvcnRzLnJlbmRlcihxckRhdGEsIG9wdGlvbnMpXG5cbiAgY29uc3QgeG1sU3RyID0gJzw/eG1sIHZlcnNpb249XCIxLjBcIiBlbmNvZGluZz1cInV0Zi04XCI/PicgK1xuICAgICc8IURPQ1RZUEUgc3ZnIFBVQkxJQyBcIi0vL1czQy8vRFREIFNWRyAxLjEvL0VOXCIgXCJodHRwOi8vd3d3LnczLm9yZy9HcmFwaGljcy9TVkcvMS4xL0RURC9zdmcxMS5kdGRcIj4nICtcbiAgICBzdmdUYWdcblxuICBmcy53cml0ZUZpbGUocGF0aCwgeG1sU3RyLCBjYilcbn1cbiJdLCJuYW1lcyI6WyJzdmdUYWdSZW5kZXJlciIsInJlcXVpcmUiLCJleHBvcnRzIiwicmVuZGVyIiwicmVuZGVyVG9GaWxlIiwicGF0aCIsInFyRGF0YSIsIm9wdGlvbnMiLCJjYiIsInVuZGVmaW5lZCIsImZzIiwic3ZnVGFnIiwieG1sU3RyIiwid3JpdGVGaWxlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/renderer/svg.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/renderer/terminal.js":
/*!******************************************************!*\
  !*** ./node_modules/qrcode/lib/renderer/terminal.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const big = __webpack_require__(/*! ./terminal/terminal */ \"(ssr)/./node_modules/qrcode/lib/renderer/terminal/terminal.js\");\nconst small = __webpack_require__(/*! ./terminal/terminal-small */ \"(ssr)/./node_modules/qrcode/lib/renderer/terminal/terminal-small.js\");\nexports.render = function(qrData, options, cb) {\n    if (options && options.small) {\n        return small.render(qrData, options, cb);\n    }\n    return big.render(qrData, options, cb);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcXJjb2RlL2xpYi9yZW5kZXJlci90ZXJtaW5hbC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxNQUFNQSxNQUFNQyxtQkFBT0EsQ0FBQztBQUNwQixNQUFNQyxRQUFRRCxtQkFBT0EsQ0FBQztBQUV0QkUsY0FBYyxHQUFHLFNBQVVFLE1BQU0sRUFBRUMsT0FBTyxFQUFFQyxFQUFFO0lBQzVDLElBQUlELFdBQVdBLFFBQVFKLEtBQUssRUFBRTtRQUM1QixPQUFPQSxNQUFNRSxNQUFNLENBQUNDLFFBQVFDLFNBQVNDO0lBQ3ZDO0lBQ0EsT0FBT1AsSUFBSUksTUFBTSxDQUFDQyxRQUFRQyxTQUFTQztBQUNyQyIsInNvdXJjZXMiOlsid2VicGFjazovL2JoZWVtZGluZS8uL25vZGVfbW9kdWxlcy9xcmNvZGUvbGliL3JlbmRlcmVyL3Rlcm1pbmFsLmpzP2YxMDciXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgYmlnID0gcmVxdWlyZSgnLi90ZXJtaW5hbC90ZXJtaW5hbCcpXG5jb25zdCBzbWFsbCA9IHJlcXVpcmUoJy4vdGVybWluYWwvdGVybWluYWwtc21hbGwnKVxuXG5leHBvcnRzLnJlbmRlciA9IGZ1bmN0aW9uIChxckRhdGEsIG9wdGlvbnMsIGNiKSB7XG4gIGlmIChvcHRpb25zICYmIG9wdGlvbnMuc21hbGwpIHtcbiAgICByZXR1cm4gc21hbGwucmVuZGVyKHFyRGF0YSwgb3B0aW9ucywgY2IpXG4gIH1cbiAgcmV0dXJuIGJpZy5yZW5kZXIocXJEYXRhLCBvcHRpb25zLCBjYilcbn1cbiJdLCJuYW1lcyI6WyJiaWciLCJyZXF1aXJlIiwic21hbGwiLCJleHBvcnRzIiwicmVuZGVyIiwicXJEYXRhIiwib3B0aW9ucyIsImNiIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/renderer/terminal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/renderer/terminal/terminal-small.js":
/*!*********************************************************************!*\
  !*** ./node_modules/qrcode/lib/renderer/terminal/terminal-small.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("const backgroundWhite = \"\\x1b[47m\";\nconst backgroundBlack = \"\\x1b[40m\";\nconst foregroundWhite = \"\\x1b[37m\";\nconst foregroundBlack = \"\\x1b[30m\";\nconst reset = \"\\x1b[0m\";\nconst lineSetupNormal = backgroundWhite + foregroundBlack // setup colors\n;\nconst lineSetupInverse = backgroundBlack + foregroundWhite // setup colors\n;\nconst createPalette = function(lineSetup, foregroundWhite, foregroundBlack) {\n    return {\n        // 1 ... white, 2 ... black, 0 ... transparent (default)\n        \"00\": reset + \" \" + lineSetup,\n        \"01\": reset + foregroundWhite + \"▄\" + lineSetup,\n        \"02\": reset + foregroundBlack + \"▄\" + lineSetup,\n        10: reset + foregroundWhite + \"▀\" + lineSetup,\n        11: \" \",\n        12: \"▄\",\n        20: reset + foregroundBlack + \"▀\" + lineSetup,\n        21: \"▀\",\n        22: \"█\"\n    };\n};\n/**\n * Returns code for QR pixel\n * @param {boolean[][]} modules\n * @param {number} size\n * @param {number} x\n * @param {number} y\n * @return {'0' | '1' | '2'}\n */ const mkCodePixel = function(modules, size, x, y) {\n    const sizePlus = size + 1;\n    if (x >= sizePlus || y >= sizePlus || y < -1 || x < -1) return \"0\";\n    if (x >= size || y >= size || y < 0 || x < 0) return \"1\";\n    const idx = y * size + x;\n    return modules[idx] ? \"2\" : \"1\";\n};\n/**\n * Returns code for four QR pixels. Suitable as key in palette.\n * @param {boolean[][]} modules\n * @param {number} size\n * @param {number} x\n * @param {number} y\n * @return {keyof palette}\n */ const mkCode = function(modules, size, x, y) {\n    return mkCodePixel(modules, size, x, y) + mkCodePixel(modules, size, x, y + 1);\n};\nexports.render = function(qrData, options, cb) {\n    const size = qrData.modules.size;\n    const data = qrData.modules.data;\n    const inverse = !!(options && options.inverse);\n    const lineSetup = options && options.inverse ? lineSetupInverse : lineSetupNormal;\n    const white = inverse ? foregroundBlack : foregroundWhite;\n    const black = inverse ? foregroundWhite : foregroundBlack;\n    const palette = createPalette(lineSetup, white, black);\n    const newLine = reset + \"\\n\" + lineSetup;\n    let output = lineSetup // setup colors\n    ;\n    for(let y = -1; y < size + 1; y += 2){\n        for(let x = -1; x < size; x++){\n            output += palette[mkCode(data, size, x, y)];\n        }\n        output += palette[mkCode(data, size, size, y)] + newLine;\n    }\n    output += reset;\n    if (typeof cb === \"function\") {\n        cb(null, output);\n    }\n    return output;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/renderer/terminal/terminal-small.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/renderer/terminal/terminal.js":
/*!***************************************************************!*\
  !*** ./node_modules/qrcode/lib/renderer/terminal/terminal.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("// let Utils = require('./utils')\nexports.render = function(qrData, options, cb) {\n    const size = qrData.modules.size;\n    const data = qrData.modules.data;\n    // let opts = Utils.getOptions(options)\n    // use same scheme as https://github.com/gtanner/qrcode-terminal because it actually works! =)\n    const black = \"\\x1b[40m  \\x1b[0m\";\n    const white = \"\\x1b[47m  \\x1b[0m\";\n    let output = \"\";\n    const hMargin = Array(size + 3).join(white);\n    const vMargin = Array(2).join(white);\n    output += hMargin + \"\\n\";\n    for(let i = 0; i < size; ++i){\n        output += white;\n        for(let j = 0; j < size; j++){\n            // let topModule = data[i * size + j]\n            // let bottomModule = data[(i + 1) * size + j]\n            output += data[i * size + j] ? black : white // getBlockChar(topModule, bottomModule)\n            ;\n        }\n        // output += white+'\\n'\n        output += vMargin + \"\\n\";\n    }\n    output += hMargin + \"\\n\";\n    if (typeof cb === \"function\") {\n        cb(null, output);\n    }\n    return output;\n} /*\nexports.renderToFile = function renderToFile (path, qrData, options, cb) {\n  if (typeof cb === 'undefined') {\n    cb = options\n    options = undefined\n  }\n\n  let fs = require('fs')\n  let utf8 = exports.render(qrData, options)\n  fs.writeFile(path, utf8, cb)\n}\n*/ ;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/renderer/terminal/terminal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/renderer/utf8.js":
/*!**************************************************!*\
  !*** ./node_modules/qrcode/lib/renderer/utf8.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const Utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/qrcode/lib/renderer/utils.js\");\nconst BLOCK_CHAR = {\n    WW: \" \",\n    WB: \"▄\",\n    BB: \"█\",\n    BW: \"▀\"\n};\nconst INVERTED_BLOCK_CHAR = {\n    BB: \" \",\n    BW: \"▄\",\n    WW: \"█\",\n    WB: \"▀\"\n};\nfunction getBlockChar(top, bottom, blocks) {\n    if (top && bottom) return blocks.BB;\n    if (top && !bottom) return blocks.BW;\n    if (!top && bottom) return blocks.WB;\n    return blocks.WW;\n}\nexports.render = function(qrData, options, cb) {\n    const opts = Utils.getOptions(options);\n    let blocks = BLOCK_CHAR;\n    if (opts.color.dark.hex === \"#ffffff\" || opts.color.light.hex === \"#000000\") {\n        blocks = INVERTED_BLOCK_CHAR;\n    }\n    const size = qrData.modules.size;\n    const data = qrData.modules.data;\n    let output = \"\";\n    let hMargin = Array(size + opts.margin * 2 + 1).join(blocks.WW);\n    hMargin = Array(opts.margin / 2 + 1).join(hMargin + \"\\n\");\n    const vMargin = Array(opts.margin + 1).join(blocks.WW);\n    output += hMargin;\n    for(let i = 0; i < size; i += 2){\n        output += vMargin;\n        for(let j = 0; j < size; j++){\n            const topModule = data[i * size + j];\n            const bottomModule = data[(i + 1) * size + j];\n            output += getBlockChar(topModule, bottomModule, blocks);\n        }\n        output += vMargin + \"\\n\";\n    }\n    output += hMargin.slice(0, -1);\n    if (typeof cb === \"function\") {\n        cb(null, output);\n    }\n    return output;\n};\nexports.renderToFile = function renderToFile(path, qrData, options, cb) {\n    if (typeof cb === \"undefined\") {\n        cb = options;\n        options = undefined;\n    }\n    const fs = __webpack_require__(/*! fs */ \"fs\");\n    const utf8 = exports.render(qrData, options);\n    fs.writeFile(path, utf8, cb);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/renderer/utf8.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/renderer/utils.js":
/*!***************************************************!*\
  !*** ./node_modules/qrcode/lib/renderer/utils.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("function hex2rgba(hex) {\n    if (typeof hex === \"number\") {\n        hex = hex.toString();\n    }\n    if (typeof hex !== \"string\") {\n        throw new Error(\"Color should be defined as hex string\");\n    }\n    let hexCode = hex.slice().replace(\"#\", \"\").split(\"\");\n    if (hexCode.length < 3 || hexCode.length === 5 || hexCode.length > 8) {\n        throw new Error(\"Invalid hex color: \" + hex);\n    }\n    // Convert from short to long form (fff -> ffffff)\n    if (hexCode.length === 3 || hexCode.length === 4) {\n        hexCode = Array.prototype.concat.apply([], hexCode.map(function(c) {\n            return [\n                c,\n                c\n            ];\n        }));\n    }\n    // Add default alpha value\n    if (hexCode.length === 6) hexCode.push(\"F\", \"F\");\n    const hexValue = parseInt(hexCode.join(\"\"), 16);\n    return {\n        r: hexValue >> 24 & 255,\n        g: hexValue >> 16 & 255,\n        b: hexValue >> 8 & 255,\n        a: hexValue & 255,\n        hex: \"#\" + hexCode.slice(0, 6).join(\"\")\n    };\n}\nexports.getOptions = function getOptions(options) {\n    if (!options) options = {};\n    if (!options.color) options.color = {};\n    const margin = typeof options.margin === \"undefined\" || options.margin === null || options.margin < 0 ? 4 : options.margin;\n    const width = options.width && options.width >= 21 ? options.width : undefined;\n    const scale = options.scale || 4;\n    return {\n        width: width,\n        scale: width ? 4 : scale,\n        margin: margin,\n        color: {\n            dark: hex2rgba(options.color.dark || \"#000000ff\"),\n            light: hex2rgba(options.color.light || \"#ffffffff\")\n        },\n        type: options.type,\n        rendererOpts: options.rendererOpts || {}\n    };\n};\nexports.getScale = function getScale(qrSize, opts) {\n    return opts.width && opts.width >= qrSize + opts.margin * 2 ? opts.width / (qrSize + opts.margin * 2) : opts.scale;\n};\nexports.getImageWidth = function getImageWidth(qrSize, opts) {\n    const scale = exports.getScale(qrSize, opts);\n    return Math.floor((qrSize + opts.margin * 2) * scale);\n};\nexports.qrToImageData = function qrToImageData(imgData, qr, opts) {\n    const size = qr.modules.size;\n    const data = qr.modules.data;\n    const scale = exports.getScale(size, opts);\n    const symbolSize = Math.floor((size + opts.margin * 2) * scale);\n    const scaledMargin = opts.margin * scale;\n    const palette = [\n        opts.color.light,\n        opts.color.dark\n    ];\n    for(let i = 0; i < symbolSize; i++){\n        for(let j = 0; j < symbolSize; j++){\n            let posDst = (i * symbolSize + j) * 4;\n            let pxColor = opts.color.light;\n            if (i >= scaledMargin && j >= scaledMargin && i < symbolSize - scaledMargin && j < symbolSize - scaledMargin) {\n                const iSrc = Math.floor((i - scaledMargin) / scale);\n                const jSrc = Math.floor((j - scaledMargin) / scale);\n                pxColor = palette[data[iSrc * size + jSrc] ? 1 : 0];\n            }\n            imgData[posDst++] = pxColor.r;\n            imgData[posDst++] = pxColor.g;\n            imgData[posDst++] = pxColor.b;\n            imgData[posDst] = pxColor.a;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/renderer/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qrcode/lib/server.js":
/*!*******************************************!*\
  !*** ./node_modules/qrcode/lib/server.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("const canPromise = __webpack_require__(/*! ./can-promise */ \"(ssr)/./node_modules/qrcode/lib/can-promise.js\");\nconst QRCode = __webpack_require__(/*! ./core/qrcode */ \"(ssr)/./node_modules/qrcode/lib/core/qrcode.js\");\nconst PngRenderer = __webpack_require__(/*! ./renderer/png */ \"(ssr)/./node_modules/qrcode/lib/renderer/png.js\");\nconst Utf8Renderer = __webpack_require__(/*! ./renderer/utf8 */ \"(ssr)/./node_modules/qrcode/lib/renderer/utf8.js\");\nconst TerminalRenderer = __webpack_require__(/*! ./renderer/terminal */ \"(ssr)/./node_modules/qrcode/lib/renderer/terminal.js\");\nconst SvgRenderer = __webpack_require__(/*! ./renderer/svg */ \"(ssr)/./node_modules/qrcode/lib/renderer/svg.js\");\nfunction checkParams(text, opts, cb) {\n    if (typeof text === \"undefined\") {\n        throw new Error(\"String required as first argument\");\n    }\n    if (typeof cb === \"undefined\") {\n        cb = opts;\n        opts = {};\n    }\n    if (typeof cb !== \"function\") {\n        if (!canPromise()) {\n            throw new Error(\"Callback required as last argument\");\n        } else {\n            opts = cb || {};\n            cb = null;\n        }\n    }\n    return {\n        opts: opts,\n        cb: cb\n    };\n}\nfunction getTypeFromFilename(path) {\n    return path.slice((path.lastIndexOf(\".\") - 1 >>> 0) + 2).toLowerCase();\n}\nfunction getRendererFromType(type) {\n    switch(type){\n        case \"svg\":\n            return SvgRenderer;\n        case \"txt\":\n        case \"utf8\":\n            return Utf8Renderer;\n        case \"png\":\n        case \"image/png\":\n        default:\n            return PngRenderer;\n    }\n}\nfunction getStringRendererFromType(type) {\n    switch(type){\n        case \"svg\":\n            return SvgRenderer;\n        case \"terminal\":\n            return TerminalRenderer;\n        case \"utf8\":\n        default:\n            return Utf8Renderer;\n    }\n}\nfunction render(renderFunc, text, params) {\n    if (!params.cb) {\n        return new Promise(function(resolve, reject) {\n            try {\n                const data = QRCode.create(text, params.opts);\n                return renderFunc(data, params.opts, function(err, data) {\n                    return err ? reject(err) : resolve(data);\n                });\n            } catch (e) {\n                reject(e);\n            }\n        });\n    }\n    try {\n        const data = QRCode.create(text, params.opts);\n        return renderFunc(data, params.opts, params.cb);\n    } catch (e) {\n        params.cb(e);\n    }\n}\nexports.create = QRCode.create;\nexports.toCanvas = __webpack_require__(/*! ./browser */ \"(ssr)/./node_modules/qrcode/lib/browser.js\").toCanvas;\nexports.toString = function toString(text, opts, cb) {\n    const params = checkParams(text, opts, cb);\n    const type = params.opts ? params.opts.type : undefined;\n    const renderer = getStringRendererFromType(type);\n    return render(renderer.render, text, params);\n};\nexports.toDataURL = function toDataURL(text, opts, cb) {\n    const params = checkParams(text, opts, cb);\n    const renderer = getRendererFromType(params.opts.type);\n    return render(renderer.renderToDataURL, text, params);\n};\nexports.toBuffer = function toBuffer(text, opts, cb) {\n    const params = checkParams(text, opts, cb);\n    const renderer = getRendererFromType(params.opts.type);\n    return render(renderer.renderToBuffer, text, params);\n};\nexports.toFile = function toFile(path, text, opts, cb) {\n    if (typeof path !== \"string\" || !(typeof text === \"string\" || typeof text === \"object\")) {\n        throw new Error(\"Invalid argument\");\n    }\n    if (arguments.length < 3 && !canPromise()) {\n        throw new Error(\"Too few arguments provided\");\n    }\n    const params = checkParams(text, opts, cb);\n    const type = params.opts.type || getTypeFromFilename(path);\n    const renderer = getRendererFromType(type);\n    const renderToFile = renderer.renderToFile.bind(null, path);\n    return render(renderToFile, text, params);\n};\nexports.toFileStream = function toFileStream(stream, text, opts) {\n    if (arguments.length < 2) {\n        throw new Error(\"Too few arguments provided\");\n    }\n    const params = checkParams(text, opts, stream.emit.bind(stream, \"error\"));\n    const renderer = getRendererFromType(\"png\") // Only png support for now\n    ;\n    const renderToFileStream = renderer.renderToFileStream.bind(null, stream);\n    render(renderToFileStream, text, params);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qrcode/lib/server.js\n");

/***/ })

};
;